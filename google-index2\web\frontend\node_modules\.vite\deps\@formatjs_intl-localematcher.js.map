{"version": 3, "sources": ["../../@formatjs/intl-localematcher/lib/abstract/CanonicalizeLocaleList.js", "../../@formatjs/intl-localematcher/lib/abstract/utils.js", "../../@formatjs/intl-localematcher/lib/abstract/BestAvailableLocale.js", "../../@formatjs/intl-localematcher/lib/abstract/LookupMatcher.js", "../../@formatjs/intl-localematcher/lib/abstract/BestFitMatcher.js", "../../@formatjs/intl-localematcher/lib/abstract/UnicodeExtensionValue.js", "../../@formatjs/intl-localematcher/lib/abstract/ResolveLocale.js", "../../@formatjs/intl-localematcher/lib/abstract/LookupSupportedLocales.js", "../../@formatjs/intl-localematcher/lib/index.js"], "sourcesContent": ["/**\n * http://ecma-international.org/ecma-402/7.0/index.html#sec-canonicalizelocalelist\n * @param locales\n */\nexport function CanonicalizeLocaleList(locales) {\n    // TODO\n    return Intl.getCanonicalLocales(locales);\n}\n", "export var UNICODE_EXTENSION_SEQUENCE_REGEX = /-u(?:-[0-9a-z]{2,8})+/gi;\nexport function invariant(condition, message, Err) {\n    if (Err === void 0) { Err = Error; }\n    if (!condition) {\n        throw new Err(message);\n    }\n}\n", "/**\n * https://tc39.es/ecma402/#sec-bestavailablelocale\n * @param availableLocales\n * @param locale\n */\nexport function BestAvailableLocale(availableLocales, locale) {\n    var candidate = locale;\n    while (true) {\n        if (availableLocales.has(candidate)) {\n            return candidate;\n        }\n        var pos = candidate.lastIndexOf('-');\n        if (!~pos) {\n            return undefined;\n        }\n        if (pos >= 2 && candidate[pos - 2] === '-') {\n            pos -= 2;\n        }\n        candidate = candidate.slice(0, pos);\n    }\n}\n", "import { UNICODE_EXTENSION_SEQUENCE_REGEX } from './utils';\nimport { BestAvailableLocale } from './BestAvailableLocale';\n/**\n * https://tc39.es/ecma402/#sec-lookupmatcher\n * @param availableLocales\n * @param requestedLocales\n * @param getDefaultLocale\n */\nexport function LookupMatcher(availableLocales, requestedLocales, getDefaultLocale) {\n    var result = { locale: '' };\n    for (var _i = 0, requestedLocales_1 = requestedLocales; _i < requestedLocales_1.length; _i++) {\n        var locale = requestedLocales_1[_i];\n        var noExtensionLocale = locale.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        var availableLocale = BestAvailableLocale(availableLocales, noExtensionLocale);\n        if (availableLocale) {\n            result.locale = availableLocale;\n            if (locale !== noExtensionLocale) {\n                result.extension = locale.slice(noExtensionLocale.length, locale.length);\n            }\n            return result;\n        }\n    }\n    result.locale = getDefaultLocale();\n    return result;\n}\n", "import { BestAvailableLocale } from './BestAvailableLocale';\nimport { UNICODE_EXTENSION_SEQUENCE_REGEX } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-bestfitmatcher\n * @param availableLocales\n * @param requestedLocales\n * @param getDefaultLocale\n */\nexport function BestFitMatcher(availableLocales, requestedLocales, getDefaultLocale) {\n    var minimizedAvailableLocaleMap = {};\n    var availableLocaleMap = {};\n    var canonicalizedLocaleMap = {};\n    var minimizedAvailableLocales = new Set();\n    availableLocales.forEach(function (locale) {\n        var minimizedLocale = new Intl.Locale(locale)\n            .minimize()\n            .toString();\n        var canonicalizedLocale = Intl.getCanonicalLocales(locale)[0] || locale;\n        minimizedAvailableLocaleMap[minimizedLocale] = locale;\n        availableLocaleMap[locale] = locale;\n        canonicalizedLocaleMap[canonicalizedLocale] = locale;\n        minimizedAvailableLocales.add(minimizedLocale);\n        minimizedAvailableLocales.add(locale);\n        minimizedAvailableLocales.add(canonicalizedLocale);\n    });\n    var foundLocale;\n    var extension;\n    for (var _i = 0, requestedLocales_1 = requestedLocales; _i < requestedLocales_1.length; _i++) {\n        var l = requestedLocales_1[_i];\n        if (foundLocale) {\n            break;\n        }\n        var noExtensionLocale = l.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        if (l !== noExtensionLocale) {\n            extension = l.slice(noExtensionLocale.length, l.length);\n        }\n        if (availableLocales.has(noExtensionLocale)) {\n            foundLocale = noExtensionLocale;\n            break;\n        }\n        if (minimizedAvailableLocales.has(noExtensionLocale)) {\n            foundLocale = noExtensionLocale;\n            break;\n        }\n        var locale = new Intl.Locale(noExtensionLocale);\n        var maximizedRequestedLocale = locale.maximize().toString();\n        var minimizedRequestedLocale = locale.minimize().toString();\n        // Check minimized locale\n        if (minimizedAvailableLocales.has(minimizedRequestedLocale)) {\n            foundLocale = minimizedRequestedLocale;\n            break;\n        }\n        // Lookup algo on maximized locale\n        foundLocale = BestAvailableLocale(minimizedAvailableLocales, maximizedRequestedLocale);\n    }\n    if (!foundLocale) {\n        return { locale: getDefaultLocale() };\n    }\n    return {\n        locale: availableLocaleMap[foundLocale] ||\n            canonicalizedLocaleMap[foundLocale] ||\n            minimizedAvailableLocaleMap[foundLocale] ||\n            foundLocale,\n        extension: extension,\n    };\n}\n", "import { invariant } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-unicodeextensionvalue\n * @param extension\n * @param key\n */\nexport function UnicodeExtensionValue(extension, key) {\n    invariant(key.length === 2, 'key must have 2 elements');\n    var size = extension.length;\n    var searchValue = \"-\".concat(key, \"-\");\n    var pos = extension.indexOf(searchValue);\n    if (pos !== -1) {\n        var start = pos + 4;\n        var end = start;\n        var k = start;\n        var done = false;\n        while (!done) {\n            var e = extension.indexOf('-', k);\n            var len = void 0;\n            if (e === -1) {\n                len = size - k;\n            }\n            else {\n                len = e - k;\n            }\n            if (len === 2) {\n                done = true;\n            }\n            else if (e === -1) {\n                end = size;\n                done = true;\n            }\n            else {\n                end = e;\n                k = e + 1;\n            }\n        }\n        return extension.slice(start, end);\n    }\n    searchValue = \"-\".concat(key);\n    pos = extension.indexOf(searchValue);\n    if (pos !== -1 && pos + 3 === size) {\n        return '';\n    }\n    return undefined;\n}\n", "import { LookupMatcher } from './LookupMatcher';\nimport { BestFitMatcher } from './BestFitMatcher';\nimport { invariant } from './utils';\nimport { UnicodeExtensionValue } from './UnicodeExtensionValue';\n/**\n * https://tc39.es/ecma402/#sec-resolvelocale\n */\nexport function ResolveLocale(availableLocales, requestedLocales, options, relevantExtensionKeys, localeData, getDefaultLocale) {\n    var matcher = options.localeMatcher;\n    var r;\n    if (matcher === 'lookup') {\n        r = LookupMatcher(availableLocales, requestedLocales, getDefaultLocale);\n    }\n    else {\n        r = BestFitMatcher(availableLocales, requestedLocales, getDefaultLocale);\n    }\n    var foundLocale = r.locale;\n    var result = { locale: '', dataLocale: foundLocale };\n    var supportedExtension = '-u';\n    for (var _i = 0, relevantExtensionKeys_1 = relevantExtensionKeys; _i < relevantExtensionKeys_1.length; _i++) {\n        var key = relevantExtensionKeys_1[_i];\n        invariant(foundLocale in localeData, \"Missing locale data for \".concat(foundLocale));\n        var foundLocaleData = localeData[foundLocale];\n        invariant(typeof foundLocaleData === 'object' && foundLocaleData !== null, \"locale data \".concat(key, \" must be an object\"));\n        var keyLocaleData = foundLocaleData[key];\n        invariant(Array.isArray(keyLocaleData), \"keyLocaleData for \".concat(key, \" must be an array\"));\n        var value = keyLocaleData[0];\n        invariant(typeof value === 'string' || value === null, \"value must be string or null but got \".concat(typeof value, \" in key \").concat(key));\n        var supportedExtensionAddition = '';\n        if (r.extension) {\n            var requestedValue = UnicodeExtensionValue(r.extension, key);\n            if (requestedValue !== undefined) {\n                if (requestedValue !== '') {\n                    if (~keyLocaleData.indexOf(requestedValue)) {\n                        value = requestedValue;\n                        supportedExtensionAddition = \"-\".concat(key, \"-\").concat(value);\n                    }\n                }\n                else if (~requestedValue.indexOf('true')) {\n                    value = 'true';\n                    supportedExtensionAddition = \"-\".concat(key);\n                }\n            }\n        }\n        if (key in options) {\n            var optionsValue = options[key];\n            invariant(typeof optionsValue === 'string' ||\n                typeof optionsValue === 'undefined' ||\n                optionsValue === null, 'optionsValue must be String, Undefined or Null');\n            if (~keyLocaleData.indexOf(optionsValue)) {\n                if (optionsValue !== value) {\n                    value = optionsValue;\n                    supportedExtensionAddition = '';\n                }\n            }\n        }\n        result[key] = value;\n        supportedExtension += supportedExtensionAddition;\n    }\n    if (supportedExtension.length > 2) {\n        var privateIndex = foundLocale.indexOf('-x-');\n        if (privateIndex === -1) {\n            foundLocale = foundLocale + supportedExtension;\n        }\n        else {\n            var preExtension = foundLocale.slice(0, privateIndex);\n            var postExtension = foundLocale.slice(privateIndex, foundLocale.length);\n            foundLocale = preExtension + supportedExtension + postExtension;\n        }\n        foundLocale = Intl.getCanonicalLocales(foundLocale)[0];\n    }\n    result.locale = foundLocale;\n    return result;\n}\n", "import { UNICODE_EXTENSION_SEQUENCE_REGEX } from './utils';\nimport { BestAvailableLocale } from './BestAvailableLocale';\n/**\n * https://tc39.es/ecma402/#sec-lookupsupportedlocales\n * @param availableLocales\n * @param requestedLocales\n */\nexport function LookupSupportedLocales(availableLocales, requestedLocales) {\n    var subset = [];\n    for (var _i = 0, requestedLocales_1 = requestedLocales; _i < requestedLocales_1.length; _i++) {\n        var locale = requestedLocales_1[_i];\n        var noExtensionLocale = locale.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        var availableLocale = BestAvailableLocale(availableLocales, noExtensionLocale);\n        if (availableLocale) {\n            subset.push(availableLocale);\n        }\n    }\n    return subset;\n}\n", "import { CanonicalizeLocaleList } from './abstract/CanonicalizeLocaleList';\nimport { ResolveLocale } from './abstract/ResolveLocale';\nexport function match(requestedLocales, availableLocales, defaultLocale, opts) {\n    var locales = availableLocales.reduce(function (all, l) {\n        all.add(l);\n        return all;\n    }, new Set());\n    return ResolveLocale(locales, CanonicalizeLocaleList(requestedLocales), {\n        localeMatcher: (opts === null || opts === void 0 ? void 0 : opts.algorithm) || 'best fit',\n    }, [], {}, function () { return defaultLocale; }).locale;\n}\nexport { LookupSupportedLocales } from './abstract/LookupSupportedLocales';\nexport { ResolveLocale } from './abstract/ResolveLocale';\n"], "mappings": ";;;AAIO,SAAS,uBAAuB,SAAS;AAE5C,SAAO,KAAK,oBAAoB,OAAO;AAC3C;;;ACPO,IAAI,mCAAmC;AACvC,SAAS,UAAU,WAAW,SAAS,KAAK;AAC/C,MAAI,QAAQ,QAAQ;AAAE,UAAM;AAAA,EAAO;AACnC,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,IAAI,OAAO;AAAA,EACzB;AACJ;;;ACDO,SAAS,oBAAoB,kBAAkB,QAAQ;AAC1D,MAAI,YAAY;AAChB,SAAO,MAAM;AACT,QAAI,iBAAiB,IAAI,SAAS,GAAG;AACjC,aAAO;AAAA,IACX;AACA,QAAI,MAAM,UAAU,YAAY,GAAG;AACnC,QAAI,CAAC,CAAC,KAAK;AACP,aAAO;AAAA,IACX;AACA,QAAI,OAAO,KAAK,UAAU,MAAM,CAAC,MAAM,KAAK;AACxC,aAAO;AAAA,IACX;AACA,gBAAY,UAAU,MAAM,GAAG,GAAG;AAAA,EACtC;AACJ;;;ACZO,SAAS,cAAc,kBAAkB,kBAAkB,kBAAkB;AAChF,MAAI,SAAS,EAAE,QAAQ,GAAG;AAC1B,WAAS,KAAK,GAAG,qBAAqB,kBAAkB,KAAK,mBAAmB,QAAQ,MAAM;AAC1F,QAAI,SAAS,mBAAmB,EAAE;AAClC,QAAI,oBAAoB,OAAO,QAAQ,kCAAkC,EAAE;AAC3E,QAAI,kBAAkB,oBAAoB,kBAAkB,iBAAiB;AAC7E,QAAI,iBAAiB;AACjB,aAAO,SAAS;AAChB,UAAI,WAAW,mBAAmB;AAC9B,eAAO,YAAY,OAAO,MAAM,kBAAkB,QAAQ,OAAO,MAAM;AAAA,MAC3E;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO,SAAS,iBAAiB;AACjC,SAAO;AACX;;;AChBO,SAAS,eAAe,kBAAkB,kBAAkB,kBAAkB;AACjF,MAAI,8BAA8B,CAAC;AACnC,MAAI,qBAAqB,CAAC;AAC1B,MAAI,yBAAyB,CAAC;AAC9B,MAAI,4BAA4B,oBAAI,IAAI;AACxC,mBAAiB,QAAQ,SAAUA,SAAQ;AACvC,QAAI,kBAAkB,IAAI,KAAK,OAAOA,OAAM,EACvC,SAAS,EACT,SAAS;AACd,QAAI,sBAAsB,KAAK,oBAAoBA,OAAM,EAAE,CAAC,KAAKA;AACjE,gCAA4B,eAAe,IAAIA;AAC/C,uBAAmBA,OAAM,IAAIA;AAC7B,2BAAuB,mBAAmB,IAAIA;AAC9C,8BAA0B,IAAI,eAAe;AAC7C,8BAA0B,IAAIA,OAAM;AACpC,8BAA0B,IAAI,mBAAmB;AAAA,EACrD,CAAC;AACD,MAAI;AACJ,MAAI;AACJ,WAAS,KAAK,GAAG,qBAAqB,kBAAkB,KAAK,mBAAmB,QAAQ,MAAM;AAC1F,QAAI,IAAI,mBAAmB,EAAE;AAC7B,QAAI,aAAa;AACb;AAAA,IACJ;AACA,QAAI,oBAAoB,EAAE,QAAQ,kCAAkC,EAAE;AACtE,QAAI,MAAM,mBAAmB;AACzB,kBAAY,EAAE,MAAM,kBAAkB,QAAQ,EAAE,MAAM;AAAA,IAC1D;AACA,QAAI,iBAAiB,IAAI,iBAAiB,GAAG;AACzC,oBAAc;AACd;AAAA,IACJ;AACA,QAAI,0BAA0B,IAAI,iBAAiB,GAAG;AAClD,oBAAc;AACd;AAAA,IACJ;AACA,QAAI,SAAS,IAAI,KAAK,OAAO,iBAAiB;AAC9C,QAAI,2BAA2B,OAAO,SAAS,EAAE,SAAS;AAC1D,QAAI,2BAA2B,OAAO,SAAS,EAAE,SAAS;AAE1D,QAAI,0BAA0B,IAAI,wBAAwB,GAAG;AACzD,oBAAc;AACd;AAAA,IACJ;AAEA,kBAAc,oBAAoB,2BAA2B,wBAAwB;AAAA,EACzF;AACA,MAAI,CAAC,aAAa;AACd,WAAO,EAAE,QAAQ,iBAAiB,EAAE;AAAA,EACxC;AACA,SAAO;AAAA,IACH,QAAQ,mBAAmB,WAAW,KAClC,uBAAuB,WAAW,KAClC,4BAA4B,WAAW,KACvC;AAAA,IACJ;AAAA,EACJ;AACJ;;;AC3DO,SAAS,sBAAsB,WAAW,KAAK;AAClD,YAAU,IAAI,WAAW,GAAG,0BAA0B;AACtD,MAAI,OAAO,UAAU;AACrB,MAAI,cAAc,IAAI,OAAO,KAAK,GAAG;AACrC,MAAI,MAAM,UAAU,QAAQ,WAAW;AACvC,MAAI,QAAQ,IAAI;AACZ,QAAI,QAAQ,MAAM;AAClB,QAAI,MAAM;AACV,QAAI,IAAI;AACR,QAAI,OAAO;AACX,WAAO,CAAC,MAAM;AACV,UAAI,IAAI,UAAU,QAAQ,KAAK,CAAC;AAChC,UAAI,MAAM;AACV,UAAI,MAAM,IAAI;AACV,cAAM,OAAO;AAAA,MACjB,OACK;AACD,cAAM,IAAI;AAAA,MACd;AACA,UAAI,QAAQ,GAAG;AACX,eAAO;AAAA,MACX,WACS,MAAM,IAAI;AACf,cAAM;AACN,eAAO;AAAA,MACX,OACK;AACD,cAAM;AACN,YAAI,IAAI;AAAA,MACZ;AAAA,IACJ;AACA,WAAO,UAAU,MAAM,OAAO,GAAG;AAAA,EACrC;AACA,gBAAc,IAAI,OAAO,GAAG;AAC5B,QAAM,UAAU,QAAQ,WAAW;AACnC,MAAI,QAAQ,MAAM,MAAM,MAAM,MAAM;AAChC,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACtCO,SAAS,cAAc,kBAAkB,kBAAkB,SAAS,uBAAuB,YAAY,kBAAkB;AAC5H,MAAI,UAAU,QAAQ;AACtB,MAAI;AACJ,MAAI,YAAY,UAAU;AACtB,QAAI,cAAc,kBAAkB,kBAAkB,gBAAgB;AAAA,EAC1E,OACK;AACD,QAAI,eAAe,kBAAkB,kBAAkB,gBAAgB;AAAA,EAC3E;AACA,MAAI,cAAc,EAAE;AACpB,MAAI,SAAS,EAAE,QAAQ,IAAI,YAAY,YAAY;AACnD,MAAI,qBAAqB;AACzB,WAAS,KAAK,GAAG,0BAA0B,uBAAuB,KAAK,wBAAwB,QAAQ,MAAM;AACzG,QAAI,MAAM,wBAAwB,EAAE;AACpC,cAAU,eAAe,YAAY,2BAA2B,OAAO,WAAW,CAAC;AACnF,QAAI,kBAAkB,WAAW,WAAW;AAC5C,cAAU,OAAO,oBAAoB,YAAY,oBAAoB,MAAM,eAAe,OAAO,KAAK,oBAAoB,CAAC;AAC3H,QAAI,gBAAgB,gBAAgB,GAAG;AACvC,cAAU,MAAM,QAAQ,aAAa,GAAG,qBAAqB,OAAO,KAAK,mBAAmB,CAAC;AAC7F,QAAI,QAAQ,cAAc,CAAC;AAC3B,cAAU,OAAO,UAAU,YAAY,UAAU,MAAM,wCAAwC,OAAO,OAAO,OAAO,UAAU,EAAE,OAAO,GAAG,CAAC;AAC3I,QAAI,6BAA6B;AACjC,QAAI,EAAE,WAAW;AACb,UAAI,iBAAiB,sBAAsB,EAAE,WAAW,GAAG;AAC3D,UAAI,mBAAmB,QAAW;AAC9B,YAAI,mBAAmB,IAAI;AACvB,cAAI,CAAC,cAAc,QAAQ,cAAc,GAAG;AACxC,oBAAQ;AACR,yCAA6B,IAAI,OAAO,KAAK,GAAG,EAAE,OAAO,KAAK;AAAA,UAClE;AAAA,QACJ,WACS,CAAC,eAAe,QAAQ,MAAM,GAAG;AACtC,kBAAQ;AACR,uCAA6B,IAAI,OAAO,GAAG;AAAA,QAC/C;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO,SAAS;AAChB,UAAI,eAAe,QAAQ,GAAG;AAC9B,gBAAU,OAAO,iBAAiB,YAC9B,OAAO,iBAAiB,eACxB,iBAAiB,MAAM,gDAAgD;AAC3E,UAAI,CAAC,cAAc,QAAQ,YAAY,GAAG;AACtC,YAAI,iBAAiB,OAAO;AACxB,kBAAQ;AACR,uCAA6B;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,GAAG,IAAI;AACd,0BAAsB;AAAA,EAC1B;AACA,MAAI,mBAAmB,SAAS,GAAG;AAC/B,QAAI,eAAe,YAAY,QAAQ,KAAK;AAC5C,QAAI,iBAAiB,IAAI;AACrB,oBAAc,cAAc;AAAA,IAChC,OACK;AACD,UAAI,eAAe,YAAY,MAAM,GAAG,YAAY;AACpD,UAAI,gBAAgB,YAAY,MAAM,cAAc,YAAY,MAAM;AACtE,oBAAc,eAAe,qBAAqB;AAAA,IACtD;AACA,kBAAc,KAAK,oBAAoB,WAAW,EAAE,CAAC;AAAA,EACzD;AACA,SAAO,SAAS;AAChB,SAAO;AACX;;;AClEO,SAAS,uBAAuB,kBAAkB,kBAAkB;AACvE,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,qBAAqB,kBAAkB,KAAK,mBAAmB,QAAQ,MAAM;AAC1F,QAAI,SAAS,mBAAmB,EAAE;AAClC,QAAI,oBAAoB,OAAO,QAAQ,kCAAkC,EAAE;AAC3E,QAAI,kBAAkB,oBAAoB,kBAAkB,iBAAiB;AAC7E,QAAI,iBAAiB;AACjB,aAAO,KAAK,eAAe;AAAA,IAC/B;AAAA,EACJ;AACA,SAAO;AACX;;;AChBO,SAAS,MAAM,kBAAkB,kBAAkB,eAAe,MAAM;AAC3E,MAAI,UAAU,iBAAiB,OAAO,SAAU,KAAK,GAAG;AACpD,QAAI,IAAI,CAAC;AACT,WAAO;AAAA,EACX,GAAG,oBAAI,IAAI,CAAC;AACZ,SAAO,cAAc,SAAS,uBAAuB,gBAAgB,GAAG;AAAA,IACpE,gBAAgB,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,cAAc;AAAA,EACnF,GAAG,CAAC,GAAG,CAAC,GAAG,WAAY;AAAE,WAAO;AAAA,EAAe,CAAC,EAAE;AACtD;", "names": ["locale"]}