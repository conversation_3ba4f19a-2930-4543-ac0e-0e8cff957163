import { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Layout,
  <PERSON>,
  <PERSON><PERSON>,
  Spin<PERSON>,
  Banner,
  ResourceList,
  ResourceItem,
  Avatar,
  Badge,
  Stack,
  Text,
  Modal,
  Tabs,
  ButtonGroup,
  TextField,
  Pagination,
  Link,
  SkeletonPage,
  Loading
} from '@shopify/polaris';
import ImageMaskEditor from '../components/ImageMaskEditor';

import { useNavigate } from 'react-router-dom';

let productImage ;

export default function ProductMaskEditor() {
  const [loading, setLoading] = useState(false);
  const [productLoading, setProductLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [products, setProducts] = useState([]);
  const [pageLoading, setPageLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [usageInfo, setUsageInfo] = useState(null);
  const [maskData, setMaskData] = useState(null);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [previewModalActive, setPreviewModalActive] = useState(false);
  const [maskPreviewUrl, setMaskPreviewUrl] = useState(null);
  const [showProductSelector, setShowProductSelector] = useState(true);
  const [saveMessage, setSaveMessage] = useState(null);
  const [isProductEnabled, setIsProductEnabled] = useState(false);
  const [targetImage, setTargetImage] = useState(null);
  const [sourceMaskData, setSourceMaskData] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedResult, setGeneratedResult] = useState(null);
  const [previewMessage, setPreviewMessage] = useState(null); // Preview专用的消息状态
  const [hasTask, setHasTask] = useState(false);
  const [taskStatus, setTaskStatus] = useState(null);
  const [isnewGeneration, setisnewGeneration] = useState(false);

  // Pagination and search states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [totalProducts, setTotalProducts] = useState(0);

  // Shop status states
  const [shopInfo, setShopInfo] = useState(null);
  const [metafieldsEnabled, setMetafieldsEnabled] = useState(false);
  const [embedBlockEnabled, setEmbedBlockEnabled] = useState(false);

  // Fetch usage information
  const fetchUsageInfo = useCallback(async () => {
    try {
      const response = await fetch('/api/usage/current');
      const result = await response.json();

      if (result.success) {
        setUsageInfo(result.data);
      }
    } catch (err) {
      console.error('Error fetching usage info:', err);
    }
  }, []);

  // Fetch products on component mount
  useEffect(() => {
    setLoadingProgress(10);
    fetchProducts(1, '');
    fetchUsageInfo();
  }, [fetchUsageInfo]);

  const navigate = useNavigate();

  function SwitchPlan() {
    navigate('/PricingPlans');

  };

  function ViewReport() {
    // 这里使用之前在顶层获取的 navigate
    navigate('/Report');

  };

  const fetchProducts = useCallback(async (page = 1, search = '') => {
    setLoading(true);
    setLoadingProgress(20);
    try {
      const response = await fetch('/api/furniture/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          page: page,
          limit: 20,
          searchQuery: search
        })
      });

      setLoadingProgress(60);
      const result = await response.json();

      if (result.success) {
        setProducts(result.data.products);
        setShopInfo(result.data.shop);
        setMetafieldsEnabled(result.data.shop.metafields_enabled);
        setTotalPages(result.data.totalPages || 1);
        setTotalProducts(result.data.totalProducts || 0);
        setCurrentPage(page);

        // Check embed block status (assuming it's in shop data)
        setEmbedBlockEnabled(result.data.shop.embed_block_enabled || false);
        setLoadingProgress(100);
      } else {
        setError(result.error || 'Failed to fetch products');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
      // 延迟隐藏页面loading，让用户看到完成状态
      setTimeout(() => {
        setPageLoading(false);
      }, 500);
    }
  }, []);

  const fetchMaskData = useCallback(async (productId) => {
    setProductLoading(true);
    setLoadingProgress(10);
    setPageLoading(true);

    try {
      setLoadingProgress(30);
      const response = await fetch('/api/furniture/product/mask-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: productId.replace('gid://shopify/Product/', ''),
          action: 'get'
        })
      });

      setLoadingProgress(70);
      setPageLoading(false);
      const result = await response.json();

      if (result.success) {
        setMaskData(result.data.maskData || null);
        setSelectedProduct(result.data.product);
        setIsProductEnabled(result.data.product.ar_enabled || false); // 设置产品enabled状态
        setLoadingProgress(100);
      } else {
        setError(result.error || 'Failed to fetch mask data');
      }
    } catch (err) {
      setError(err.message);
      setPageLoading(false);
    } finally {
      setTimeout(() => {
        setProductLoading(false);
      }, 300);
    }
  }, []);

  const saveMaskData = useCallback(async () => {
    if (!selectedProduct || !maskData) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/furniture/product/mask-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: selectedProduct.id,
          action: 'save',
          maskData: maskData,
          productImageUrl: selectedProduct.image?.src || selectedProduct.images?.[0]?.src
        })
      });

      const result = await response.json();

      if (result.success) {
        handleSaveSuccess();

        // Reload product status and mask data
        await fetchMaskData(selectedProduct.id);
      } else {
        handleSaveError(result.error || 'Failed to save mask data');
      }
    } catch (err) {
      handleSaveError(err.message);
    } finally {
      setSaving(false);
    }
  }, [selectedProduct, maskData]);

  const handleProductSelect = (productId) => {
    fetchMaskData(productId);
    setShowProductSelector(false); // 隐藏产品选择器
    setSelectedTab(0); // 切换到Mask Editor tab
  };

  // 返回产品选择
  const handleBackToProductSelector = () => {
    setShowProductSelector(true);
    setSelectedProduct(null);
    setMaskData(null);
    setSaveMessage(null);
    setSelectedTab(0);
  };

  // 保存mask后的处理
  const handleSaveSuccess = () => {
    setSaveMessage({ type: 'success', content: 'Mask saved successfully! Generating preview images...' });

    // 开始检查图片生成状态
    checkImageGenerationStatus();
  };

  const handleSaveError = (error) => {
    setSaveMessage({ type: 'error', content: `Failed to save mask: ${error}` });
  };

  // 检查图片生成状态
  const checkImageGenerationStatus = async () => {
    if (!selectedProduct) return;

    try {
      const response = await fetch(`/api/furniture/product/generation-status?productId=${selectedProduct.id}`);
      const result = await response.json();

      if (result.success && result.status) {
        if (result.status === 'completed') {
          setSaveMessage({ type: 'success', content: 'Mask saved and preview images generated successfully!' });
        } else if (result.status === 'processing' ) {
          setSaveMessage({ type: 'success', content: 'Mask saved! Preview images are being generated...' });
          // 继续轮询
          setTimeout(() => checkImageGenerationStatus(), 3000);
        } else if (result.status === 'failed') {
          setSaveMessage({ type: 'error', content: 'Mask saved but preview image generation failed.' });
        }
      }
    } catch (error) {
      console.error('Error checking image generation status:', error);
    }
  };

  // 切换产品启用状态
  const toggleProductEnabled = async () => {
    if (!selectedProduct) return;

    try {
      const response = await fetch(`/api/products/${selectedProduct.id}/toggle-enabled`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled: !isProductEnabled })
      });
 
      if (response.ok) {
        setIsProductEnabled(!isProductEnabled);
        setSaveMessage({
          type: 'success',
          content: `Product ${!isProductEnabled ? 'enabled' : 'disabled'} successfully!`
        });
      }
    } catch (error) {
      setSaveMessage({ type: 'error', content: 'Failed to update product status' });
    }
  };

  // 压缩图片函数
  const compressImage = (file, maxSize = 1000) => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = function() {
        let { width, height } = img;

        // 检查是否需要压缩
        if (width <= maxSize && height <= maxSize) {
          resolve(file);
          return;
        }

        // 计算新尺寸
        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width;
            width = maxSize;
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height;
            height = maxSize;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制并压缩
        ctx.drawImage(img, 0, 0, width, height);
        canvas.toBlob(resolve, 'image/jpeg', 0.8);
      };

      img.src = URL.createObjectURL(file);
    });
  };

  // 处理AI生成
  const handleGenerateAI = async () => {
    if (!targetImage || !sourceMaskData || !selectedProduct) return;

    setIsGenerating(true);
    setisnewGeneration(false);
    try {
      // 创建特殊邮箱地址用于预览
      const previewEmail = `<EMAIL>`;

      // 显示loading状态
      setPreviewMessage({ type: 'success', content: 'Starting AI generation... You could get back after 5 mins.' });

      const response = await fetch('/api/furniture/ai-generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: previewEmail,
          productId: selectedProduct.id,
          sourceImageBase64: targetImage,  // 用户上传的图片作为source
          sourceMaskData: sourceMaskData,  // 用户编辑的source mask
          targetImageBase64: selectedProduct.image?.src, // 产品图片作为target
          targetMaskData: maskData  // 产品的target mask
        })
      });

      const result = await response.json();
      if (result.success) {
        // 设置任务状态
        setHasTask(true);
        setTaskStatus('processing');
        setPreviewMessage({
          type: 'success',
          content: 'AI generation is now processing. This may take several minutes. Please check back later to view your results.'
        });
        // 轮询检查生成状态
        checkGenerationStatus(previewEmail);
      } else {
        setPreviewMessage({ type: 'error', content: 'Failed to start AI generation' });
      }
    } catch (error) {
      setPreviewMessage({ type: 'error', content: 'Error starting AI generation' });
    } finally {
      setIsGenerating(false);
    }
  };

  // 检查生成状态
  const checkGenerationStatus = async (email) => {
    try {
      const response = await fetch(`/api/furniture/ai-status?email=${email}&productId=${selectedProduct.id}`);
      const result = await response.json();

      if (result.success && result.task) {


        if (result.task.status === 'completed') {
          setGeneratedResult({
            imageUrl: result.task.result_image_url,
            taskId: result.task.task_id
          });
        } else if (result.task.status === 'processing' && isnewGeneration===false) {
          // 继续轮询
          setTimeout(() => checkGenerationStatus(email), 30000);
        } else if (result.task.status === 'failed') {
          // failed状态不再轮询
        }

       setHasTask(true);
        setTaskStatus(result.task.status);
      }
    } catch (error) {
      console.error('Error checking generation status:', error);
    }
  };

  // 检查初始状态 - 当切换到Preview tab时调用
  const checkInitialStatus = async () => {
    if (!selectedProduct) return;

    try {
      const previewEmail = `<EMAIL>`;
      const response = await fetch(`/api/furniture/ai-status?email=${previewEmail}&productId=${selectedProduct.id}`);
      const result = await response.json();

      if (result.success && result.task) {
        setHasTask(true);
        setTaskStatus(result.task.status);

        if (result.task.status === 'completed') {
          setGeneratedResult({
            imageUrl: result.task.result_image_url,
            taskId: result.task.task_id
          });
        } else if (result.task.status === 'processing' && isnewGeneration===false) {
          // 继续轮询
          checkGenerationStatus(previewEmail);
        }
      } else {
        setHasTask(false);
        setTaskStatus(null);
      }
    } catch (error) {
      console.error('Error checking initial status:', error);
    }
  };

  // Handle search
  const handleSearch = useCallback((value) => {
    setSearchQuery(value);
    fetchProducts(1, value);
  }, [fetchProducts]);

  // 监听tab切换，当切换到Preview时检查初始状态
  useEffect(() => {
    if (selectedTab === 1 && selectedProduct) { // Preview tab
      checkInitialStatus();
    }
  }, [selectedTab, selectedProduct]);

  // Handle pagination
  const handlePageChange = useCallback((page) => {
    fetchProducts(page, searchQuery);
  }, [fetchProducts, searchQuery]);

  // Enable MetaFields
  const enableMetaFields = useCallback(async () => {
    try {
      const response = await fetch('/api/furniture/shop/metafields/enable', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();

      if (result.success) {
        setMetafieldsEnabled(true);
      } else {
        setError('Failed to enable metafields');
      }
    } catch (error) {
      setError('Error enabling metafields');
    }
  }, []);

  // Navigate to theme editor
  const navigateToThemeEditor = useCallback(() => {
    if (shopInfo && shopInfo.shop_name) {
      const themeEditorUrl = `https://${shopInfo.shop_name}.myshopify.com/admin/themes/current/editor?template=product&addAppBlockId=eeee1a89-4763-4bea-ae10-c7068b12687f/ar-button&target=mainSection`;
      window.open(themeEditorUrl, '_blank');
    }
  }, [shopInfo]); 

  const handleMaskChange = useCallback((newMaskData) => { 
    setMaskData(newMaskData);
  }, []);

  const generateMaskPreview = useCallback(() => {
    if (!maskData || !maskData.paths || maskData.paths.length === 0) {
      setError('No mask data available for preview');
      return;
    }

    if (!productImage) {
      setError('No product image available for mask preview');
      return;
    }

    // Create a temporary image to get actual dimensions
    const img = new Image();
    img.onload = () => {
      // Use actual product image dimensions
      const canvas = document.createElement('canvas');
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      const ctx = canvas.getContext('2d');

      // Fill with black background
      ctx.fillStyle = '#000000';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw white mask areas
      ctx.strokeStyle = '#FFFFFF';
      ctx.fillStyle = '#FFFFFF';
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      maskData.paths.forEach(path => {
        if (path.length < 2) return;

        // Check if we have new format with imageDisplayInfo
        if (maskData.metadata && maskData.metadata.imageDisplayInfo) {
          // New format: coordinates are already relative to original image
          ctx.lineWidth = path[0].size || 20;
          ctx.beginPath();
          ctx.moveTo(path[0].x, path[0].y);

          for (let i = 1; i < path.length; i++) {
            ctx.lineTo(path[i].x, path[i].y);
          }

          ctx.stroke();
        } else {
          // Old format: need to scale coordinates
          const scaleX = maskData.metadata?.scaleX || (canvas.width / 600);
          const scaleY = maskData.metadata?.scaleY || (canvas.height / 400);
          const scaleRatio = Math.min(scaleX, scaleY);

          // Scale the brush size and coordinates
          ctx.lineWidth = (path[0].size || 20) * scaleRatio;
          ctx.beginPath();
          ctx.moveTo(path[0].x * scaleX, path[0].y * scaleY);

          for (let i = 1; i < path.length; i++) {
            ctx.lineTo(path[i].x * scaleX, path[i].y * scaleY);
          }

          ctx.stroke();
        }
      });

      const maskUrl = canvas.toDataURL('image/png');
      setMaskPreviewUrl(maskUrl);
      setPreviewModalActive(true);
    };

    img.src = productImage;
  }, [maskData, productImage]);

  const clearMask = useCallback(() => {
    setMaskData(null);
  }, []);

  const tabs = [
    {
      id: 'editor',
      content: 'Config the Product',
      panelID: 'editor-panel'
    },
    {
      id: 'preview',
      content: 'Try an Place In',
      panelID: 'preview-panel'
    }
  ];

  const renderEditor = () => { 


    if (productLoading) {
      return (
        <Frame>
          <Loading />
          <SkeletonPage>
            <Layout>
              <Layout.Section>
                <Card>
                  <Card.Section>
                    <div style={{ height: '400px' }}>
                      <div style={{ marginBottom: '16px', height: '20px', backgroundColor: '#f0f0f0', borderRadius: '4px' }}></div>
                      <div style={{ marginBottom: '16px', height: '20px', backgroundColor: '#f0f0f0', borderRadius: '4px' }}></div>
                      <div style={{ marginBottom: '16px', height: '20px', backgroundColor: '#f0f0f0', borderRadius: '4px' }}></div>
                      <div style={{ marginBottom: '16px', height: '20px', backgroundColor: '#f0f0f0', borderRadius: '4px' }}></div>
                    </div>
                  </Card.Section>
                </Card>
              </Layout.Section>
            </Layout>
          </SkeletonPage>
        </Frame>
      );
    }

    if (!selectedProduct) {
      return (
        <Card sectioned>
          <div style={{ textAlign: 'center', padding: '4rem' }}>
            <Text variant="headingMd">Please Select Product</Text>
            <Text variant="bodyMd">Choose a product from the left list to edit its mask</Text>
          </div>
        </Card>
      );
    }

     productImage = selectedProduct.image?.src || selectedProduct.images?.[0]?.src;
    
    if (!productImage) {
      return (
        <Card sectioned>
          <div style={{ textAlign: 'center', padding: '4rem' }}>
            <Text variant="headingMd">No Product Image</Text>
            <Text variant="bodyMd">This product has no available images for mask editing</Text>
          </div>
        </Card>
      );
    }

    return (
      <Stack vertical spacing="loose">
        {/* 保存消息 */}
        {saveMessage && (
          <Banner
            title={saveMessage.type === 'success' ? 'Success' : 'Error'}
            status={saveMessage.type === 'success' ? 'success' : 'critical'}
            onDismiss={() => setSaveMessage(null)}
          >
            <p>{saveMessage.content}</p>
          </Banner>
        )}

        <Card sectioned>
          <Stack alignment="center">
            {selectedProduct.image && (
              <Avatar size="large" source={selectedProduct.image.src} />
            )}
            <Stack.Item fill>
              <Text variant="headingMd">{selectedProduct.title}</Text>
              <Text variant="bodyMd">Paint areas on the product image that need to be replaced</Text>
            </Stack.Item>
          </Stack>
        </Card>

        <ImageMaskEditor
          imageUrl={productImage}
          onMaskChange={handleMaskChange}
          initialMaskData={maskData}
          width={600}
          height={400}
        />

        {/* 操作按钮 */}
          <Stack distribution="equalSpacing" alignment="center">
            <ButtonGroup>
              <Button
                primary
                onClick={saveMaskData}
                disabled={!maskData || saving}
                loading={saving}
              >
                Save Mask
              </Button>
              <Button
                onClick={toggleProductEnabled}
                tone={isProductEnabled ? 'critical' : 'success'}
              >
                {isProductEnabled ? 'Disable Product' : 'Enable Product'}
              </Button>
            </ButtonGroup>

            <Button onClick={handleBackToProductSelector}>
              Back to Products
            </Button>
          </Stack>
      </Stack>
    );
  };

  const renderPreview = () => {
    if (!selectedProduct || !maskData || !maskData.paths || maskData.paths.length === 0) {
      return (
        <Card sectioned>
          <div style={{ textAlign: 'center', padding: '4rem' }}>
            <Text variant="headingMd">No Mask Data</Text>
            <Text variant="bodyMd">Please create a mask in the editor first</Text>
          </div>
        </Card>
      );
    }



    // 状态图标组件
    const StatusIcon = ({ status }) => {
      switch (status) {
        case 'completed':
          return <span style={{ color: '#008060', fontSize: '24px' }}>✅</span>;
        case 'processing':
          return <span style={{ color: '#2196F3', fontSize: '24px' }}>⏳</span>;
        case 'failed':
          return <span style={{ color: '#D72C0D', fontSize: '24px' }}>❌</span>;
        default:
          return null;
      }
    };

    // 状态消息组件
    const StatusMessage = ({ status }) => {
      switch (status) {
        case 'completed':
          return <Text variant="bodyMd" tone="success">AI generation completed successfully!</Text>;
        case 'processing':
          return <Text variant="bodyMd" tone="subdued">AI generation in progress...</Text>;
        case 'failed':
          return <Text variant="bodyMd" tone="critical">AI generation failed. Please try again.</Text>;
        default:
          return null;
      }
    };

    return (
      <Stack vertical spacing="loose">
        {/* 如果有任务，显示状态而不是步骤 */}
        {hasTask && taskStatus && isnewGeneration===false ? (
          <Card sectioned>
            <Stack vertical spacing="loose" alignment="center">
              <Stack alignment="center" spacing="tight">
                <StatusIcon status={taskStatus} />
                <Text variant="headingMd">Generation Status</Text>
              </Stack>
              <StatusMessage status={taskStatus} /> 

              {/* 如果是完成状态，显示结果图片 */}
              {taskStatus === 'completed' && generatedResult && (
                <div style={{ textAlign: 'center', marginTop: '20px' }}>
                  <img
                    src={generatedResult.imageUrl}
                    alt="Generated Result"
                    style={{ maxWidth: '100%', maxHeight: '400px', border: '1px solid #ddd', borderRadius: '8px' }}
                  />
                </div>
              )}

              {/* 重新生成按钮 */}
              {(taskStatus === 'completed' || taskStatus === 'failed' || taskStatus === 'processing') && (
                <Button primary onClick={() => {
                  setHasTask(false);
                  setTaskStatus(null);
                  setGeneratedResult(null);
                  setTargetImage(null);
                  setSourceMaskData(null);
                  setisnewGeneration(true);


                }}>
                  Start New Generation
                </Button>
              )}
            </Stack>
          </Card>
        ) : (
          /* 如果没有任务，显示步骤 */
          
          <>
        {
          previewMessage && (
              <Banner
                title={previewMessage.type === 'success' ? 'Success' : 'Error'}
                status={previewMessage.type === 'success' ? 'success' : 'critical'}
                onDismiss={() => setPreviewMessage(null)}
              >
                <p>{previewMessage.content}</p>
              </Banner>
        )
          }
            {/* Step 1: Upload a Photo of Your Room to Place the Product - 只在没有targetImage时显示 */}
            {!targetImage&&!isGenerating && (
                <Stack vertical spacing="loose">
                  <Text variant="headingMd">Step 1: Upload a Photo of Your Room to Place the Product</Text>

                <input
                  type="file"
                  accept="image/*"
                  onChange={async (e) => {
                    const file = e.target.files[0];
                    if (file) {
                      try {
                        // 压缩图片如果需要
                        const compressedFile = await compressImage(file, 1000);

                        const reader = new FileReader();
                        reader.onload = (e) => {
                          setTargetImage(e.target.result);
                          // 重置后续状态
                          setSourceMaskData(null);
                          setGeneratedResult(null);
                        };
                        reader.readAsDataURL(compressedFile);
                      } catch (error) {
                        console.error('Error processing image:', error);
                        setPreviewMessage({ type: 'error', content: 'Failed to process image' });
                      }
                    }
                  }}
                  style={{ marginBottom: '1rem' }}
                />

                {targetImage && (
                  <div style={{ textAlign: 'center' }}>
                    <img
                      src={targetImage}
                      alt="Target"
                      style={{ maxWidth: '200px', maxHeight: '200px', border: '1px solid #ddd' }}
                    />
                  </div>
                )}
              </Stack>
            )}

            {/* Step 2: Select the Product Area */}
            {targetImage&&!isGenerating && (
                <Stack vertical spacing="loose">
                  <Stack alignment="center" distribution="equalSpacing">
                    <Text variant="headingMd">Step 2: Select the Product Area. </Text>

                    <Button
                      onClick={() => {
                        setTargetImage(null);
                        setSourceMaskData(null);
                        setGeneratedResult(null);
                      }}
                      size="small"
                    >
                      Back to Step 1
                    </Button>
                  </Stack>
                  <Text variant="bodyMd">Draw areas on your uploaded image that should be replaced </Text>
                  <Text variant="bodyMd">*Please make sure the selected area’s proportions roughly match the product’s proportions.</Text>

                  <ImageMaskEditor
                    imageUrl={targetImage}
                    onMaskChange={setSourceMaskData}
                    initialMaskData={sourceMaskData}
                    width={600}
                    height={400}
                  />
                </Stack>
            )}

            {/* Step 3: Generate Button */}
            {targetImage && sourceMaskData  && (
              
                <Stack alignment="center">
                  <Button
                    primary
                    onClick={handleGenerateAI}
                    loading={isGenerating}
                    disabled={isGenerating}
                  >
                    {isGenerating ? 'Generating...' : 'Generate AI Image'}
                  </Button>
                </Stack>
            )}
          </>
        )}
      </Stack>
    );
  };

  // Render Usage Information Card
  const renderUsageCard = () => {
    if (!usageInfo) return null;
    console.log('Usage Info:', usageInfo);
    const { currentPlan, currentUsage } = usageInfo;
    const usageCount = usageInfo?.remaining || 0;
    const usedCount = usageInfo?.used || 0;
    const usagePercentage = usageInfo
      ? Math.round((usageInfo.used / (usageInfo.remaining+usageInfo.used)) * 100)
      : 0;

    return (
      <Card sectioned>
        <Stack vertical spacing="tight">
          <Stack alignment="center" distribution="equalSpacing">
            <Text variant="headingMd">Usage Information</Text>
            <Stack spacing="tight">
              <Button
                onClick={SwitchPlan}
                size="small"
              >
                Switch Plan
              </Button>
              <Button
                onClick={ViewReport}
                size="small"
                outline
              >
                View Usage Report
              </Button>
            </Stack>
          </Stack>

          <Stack alignment="center" distribution="equalSpacing">
            <Stack vertical spacing="extraTight">
              <Text variant="bodyMd">Current Plan: <strong>{usageInfo?.plan || 'Free'}</strong></Text>
              <Text variant="bodyMd">
                Remained: <strong>{usageCount}</strong> | 
                Used: <strong>{usedCount}</strong>

              </Text>
            </Stack>

              <Badge status={usagePercentage >= 90 ? 'critical' : usagePercentage >= 80 ? 'warning' : 'success'}>
                {usagePercentage}% used
              </Badge>
            
          </Stack>
        </Stack>
      </Card>
    );
  };

  // 显示SkeletonPage直到页面完全加载
  if (pageLoading) {
    return (
     <Frame>
      
      <div>
      <Loading />
        <SkeletonPage primaryAction>
          <Layout>
            <Layout.Section oneThird>
              <Card>
                <Card.Section>
                  <div style={{ height: '400px' }}>
                    <div style={{ marginBottom: '16px', height: '20px', backgroundColor: '#f0f0f0', borderRadius: '4px' }}></div>
                    <div style={{ marginBottom: '16px', height: '20px', backgroundColor: '#f0f0f0', borderRadius: '4px' }}></div>
                    <div style={{ marginBottom: '16px', height: '20px', backgroundColor: '#f0f0f0', borderRadius: '4px' }}></div>
                  </div>
                </Card.Section>
              </Card>
            </Layout.Section>

          </Layout>
        </SkeletonPage>
      </div>  
      </Frame>
    );
  }

  return (
    <Page
      title={showProductSelector ? "SeeInPlace" : `Configure Product - ${selectedProduct?.title || ''}`}
      subtitle={showProductSelector ? "Manage your products and complete the configurations" : "Create and edit AI replacement masks"}
      primaryAction={!showProductSelector ? {
        content: 'Back to Products',
        onAction: handleBackToProductSelector
      } : undefined}
    >
      <Layout>


        {/* 消息banner - 显示在顶部 */}
        {!metafieldsEnabled && (
          <Layout.Section>
            <Banner
              title="MetaFields Not Enabled"
              status="warning"
              action={{
                content: 'Enable MetaFields',
                onAction: enableMetaFields
              }}
            >
              <p>MetaFields are required to save product configurations. Click to enable them for your shop.</p>
            </Banner>
          </Layout.Section>
        )}

        {!embedBlockEnabled && (
          <Layout.Section>
            <Banner
              title="Embed Block Not Configured"
              status="info"
              action={{
                content: 'Configure Theme',
                onAction: navigateToThemeEditor
              }}
            >
              <p>Add the AR Viewer block to your product pages to display AR content. Click to open the theme editor.</p>
            </Banner>
          </Layout.Section>
        )}

        {error && (
          <Layout.Section>
            <Banner status="critical" title="Error">
              <p>{error}</p>
            </Banner>
          </Layout.Section>
        )}

        {success && (
          <Layout.Section>
            <Banner status="success" title="Success">
              <p>{success}</p>
            </Banner>
          </Layout.Section>
        )}

        {/* Usage Information Card */}
        {usageInfo && (
          <Layout.Section>
            {renderUsageCard()}
          </Layout.Section>
        )}

        {/* 产品选择器 - 只在showProductSelector为true时显示 */}
        {showProductSelector && (
          <Layout.Section>
            <Card>
            <Card.Section>
              <Stack vertical spacing="loose">
                <div>
                  <Text variant="headingMd">Select Product</Text>
                </div>

                <TextField
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={handleSearch}
                  clearButton
                  onClearButtonClick={() => handleSearch('')}
                />
              </Stack>
            </Card.Section>

            {loading ? (
              <Card.Section>
                <div style={{ textAlign: 'center', padding: '2rem' }}>
                  <Spinner size="large" />
                  <p style={{ marginTop: '1rem' }}>Loading products...</p>
                </div>
              </Card.Section>
            ) : (
              <>
                <ResourceList
                  resourceName={{ singular: 'product', plural: 'products' }}
                  items={products}
                  renderItem={(product) => {
                    const { id, title, image, ar_enabled } = product;
                    const media = image ? <Avatar customer size="medium" source={image.src} /> : undefined;

                    return (
                      <ResourceItem
                        id={id}
                        media={media}
                        accessibilityLabel={`Select ${title}`}
                        onClick={() => handleProductSelect(id)}
                      >
                        <Stack distribution="equalSpacing" alignment="center">
                          <Stack.Item fill>
                            <Text variant="bodyMd" fontWeight="semibold">{title}</Text>
                          </Stack.Item>
                          <Stack.Item>
                            <Badge status={ar_enabled ? 'success' : 'info'}>
                              {ar_enabled ? 'AI Enabled' : 'AI disabled'}
                            </Badge>
                          </Stack.Item>
                        </Stack>
                      </ResourceItem>
                    );
                  }}
                />

                {totalPages > 1 && (
                  <Card.Section>
                    <div style={{ display: 'flex', justifyContent: 'center' }}>
                      <Pagination
                        hasPrevious={currentPage > 1}
                        onPrevious={() => handlePageChange(currentPage - 1)}
                        hasNext={currentPage < totalPages}
                        onNext={() => handlePageChange(currentPage + 1)}
                        label={`Page ${currentPage} of ${totalPages}`}
                      />
                    </div>
                  </Card.Section>
                )}
              </>
            )}
          </Card>
        </Layout.Section>
        )}

        {/* Mask Editor - 只在选择了产品后显示 */}
        {!showProductSelector && selectedProduct && (
          <Layout.Section>
            <Card>
              <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab}>
                <Card.Section>
                  {selectedTab === 0 && renderEditor()}
                  {selectedTab === 1 && renderPreview()}
                </Card.Section>
              </Tabs>
            </Card>
          </Layout.Section>
        )}
      </Layout>

      {/* 蒙版预览模态框 */}
      <Modal
        open={previewModalActive}
        onClose={() => setPreviewModalActive(false)}
        title="Mask Preview"
        large
      >
        <Modal.Section>
          {maskPreviewUrl && (
            <div style={{ textAlign: 'center' }}>
              <img 
                src={maskPreviewUrl} 
                alt="Mask Preview" 
                style={{ maxWidth: '100%', border: '1px solid #ddd' }}
              />
              <Text variant="bodyMd" color="subdued">
                White areas will be replaced by AI, black areas remain unchanged
              </Text>
            </div>
          )}
        </Modal.Section>
      </Modal>
    </Page>
  );
}
