// node_modules/@formatjs/intl-pluralrules/locale-data/cs.js
if (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === "function") {
  Intl.PluralRules.__addLocaleData({ "data": { "categories": { "cardinal": ["one", "few", "many", "other"], "ordinal": ["other"] }, "fn": function(n, ord) {
    var s = String(n).split("."), i = s[0], v0 = !s[1];
    if (ord)
      return "other";
    return n == 1 && v0 ? "one" : i >= 2 && i <= 4 && v0 ? "few" : !v0 ? "many" : "other";
  } }, "locale": "cs" });
}
//# sourceMappingURL=@formatjs_intl-pluralrules_locale-data_cs.js.map
