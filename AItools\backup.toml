# fly.toml app configuration file generated for google-index on 2025-07-25T11:14:36+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'google-index'
primary_region = 'lax'

[build]
  [build.args]
    SHOPIFY_API_KEY = "4d9ff006fed6eb64c69496ab15b01a22"

[env]
  PORT = "8081"
  HOST = "https://app.jindex.org"
  SCOPES = "read_content,read_products,unauthenticated_read_content,unauthenticated_read_product_listings"
  SHOPIFY_API_KEY = "4d9ff006fed6eb64c69496ab15b01a22"
  DB_TYPE = "server"

[http_service]
  internal_port = 8081
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '512mb'
  cpu_kind = 'shared'
  cpus = 1
