{"version": 3, "sources": ["../../@shopify/i18next-shopify/dist/es/utils.js", "../../@shopify/i18next-shopify/dist/es/index.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nimport { isValidElement, cloneElement } from 'react';\nvar arr = [];\nvar each = arr.forEach;\n\n// Copied from https://github.com/i18next/i18next-icu/blob/370027c829e240b36b2f6e5d648be35453c9e6d8/src/utils.js\nexport function defaults(obj) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  each.call(args, function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) {\n          obj[prop] = source[prop];\n        }\n      }\n    }\n  });\n  return obj;\n}\n\n/**\n * Replaces all occurrences of the specified text. Returns a new value with the replacements made.\n * This function supports replacing text with React elements and replacing values within\n * nested React elements/arrays.\n *\n * @param {string|object|Array} interpolated - The value to replace occurrences of the specified text in.\n * @param {string|RegExp} pattern - The text or regular expression to search for in the interpolated value.\n * @param {string|object|Array} replacement - The value to replace occurrences of the specified text with.\n * @returns {string|object|Array} A new value with the specified text replaced.\n */\nexport function replaceValue(interpolated, pattern, replacement) {\n  var _interpolated$props;\n  switch (_typeof(interpolated)) {\n    case 'string':\n      {\n        var split = interpolated.split(pattern);\n        // Check if interpolated includes pattern\n        //  && if String.prototype.replace wouldn't work because replacement is an object like a React element.\n        if (split.length !== 1 && _typeof(replacement) === 'object') {\n          // Return array w/ the replacement\n\n          if (!replacement.key && isValidElement(replacement)) {\n            // eslint-disable-next-line no-param-reassign\n            replacement = cloneElement(replacement, {\n              key: pattern.toString()\n            });\n          }\n          return [split[0], replacement, split[1]].flat();\n        }\n\n        // interpolated and replacement are primitives\n        return interpolated.replace(pattern, replacement);\n      }\n    case 'object':\n      if (Array.isArray(interpolated)) {\n        return interpolated.map(function (item) {\n          return replaceValue(item, pattern, replacement);\n        }).flat();\n      }\n\n      // Check if the interpolated object may be a React element w/ children.\n      if (interpolated !== null && interpolated !== void 0 && (_interpolated$props = interpolated.props) !== null && _interpolated$props !== void 0 && _interpolated$props.children) {\n        var newChildren = replaceValue(interpolated.props.children, pattern, replacement);\n        if (newChildren !== interpolated.props.children) {\n          return _objectSpread(_objectSpread({}, interpolated), {}, {\n            props: _objectSpread(_objectSpread({}, interpolated.props), {}, {\n              children: newChildren\n            })\n          });\n        }\n      }\n  }\n\n  // The interpolated element is something else, just return it\n  return interpolated;\n}", "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport * as utils from './utils';\nfunction getDefaults() {\n  return {};\n}\nvar MUSTACHE_FORMAT = /{{?\\s*(\\w+)\\s*}}?/g;\nvar ShopifyFormat = /*#__PURE__*/function () {\n  function ShopifyFormat(options) {\n    _classCallCheck(this, ShopifyFormat);\n    this.type = 'i18nFormat';\n    this.init(null, options);\n  }\n  _createClass(ShopifyFormat, [{\n    key: \"init\",\n    value: function init(i18next, options) {\n      var i18nextOptions = i18next && i18next.options && i18next.options.i18nFormat || {};\n      this.options = utils.defaults(i18nextOptions, options, this.options || {}, getDefaults());\n      this.i18next = i18next;\n    }\n\n    // Implement custom interpolation logic\n    // While i18next and Shopify's format both use the mustache syntax for interpolation,\n    // Shopify uses the `ordinal` interpolation for ordinal pluralization, while i18next uses `count`.\n    // parse(res, options, lng, ns, key, info)\n  }, {\n    key: \"parse\",\n    value: function parse(res, options) {\n      var _this = this;\n      // const hadSuccessfulLookup = info && info.resolved && info.resolved.res;\n\n      if (res === null) {\n        return res;\n      }\n\n      // returnObjects parameter can cause objects to be resolved, rather than a single string\n      // Perform parsing/interpolation on each of it's values\n      if (_typeof(res) === 'object') {\n        var newRes = {};\n        for (var _i = 0, _Object$entries = Object.entries(res); _i < _Object$entries.length; _i++) {\n          var _Object$entries$_i = _slicedToArray(_Object$entries[_i], 2),\n            key = _Object$entries$_i[0],\n            value = _Object$entries$_i[1];\n          newRes[key] = this.parse(value, options);\n        }\n        return newRes;\n      }\n\n      // Interpolations\n      var matches = res.match(MUSTACHE_FORMAT);\n      if (!matches) {\n        return res;\n      }\n      var interpolated = res;\n      matches.forEach(function (match) {\n        var _value;\n        var interpolation_key = match.replace(MUSTACHE_FORMAT, '$1');\n        var value = interpolation_key === 'ordinal' ? options.count || options.ordinal : options[interpolation_key];\n\n        // Cardinal and Ordinal pluralizations should be formatted according to their locale\n        // eg. \"1,234,567th\" instead of \"1234567th\"\n        // However `count` and `ordinal` can also be used as a non-plural variable\n        if ((interpolation_key === 'ordinal' || interpolation_key === 'count') && typeof value === 'number') {\n          value = new Intl.NumberFormat(_this.i18next.resolvedLanguage).format(value);\n        }\n        interpolated = utils.replaceValue(interpolated, match, (_value = value) !== null && _value !== void 0 ? _value : '');\n      });\n      return interpolated;\n    }\n\n    // Add any other locations that should be searched first for an answer to the lookup\n    // Add keys to `finalKeys` in reverse order (e.g., least specific -> most specific)\n    // Useful when defining keys for pluralization or other context cases (e.g., grammatical gender)\n  }, {\n    key: \"addLookupKeys\",\n    value: function addLookupKeys(finalKeys, key, code, ns, options) {\n      var needsPluralHandling = Boolean(options.count !== undefined && typeof options.count !== 'string' || typeof options.ordinal === 'number');\n      if (needsPluralHandling) {\n        if (!this.i18next.translator.pluralResolver.shouldUseIntlApi()) {\n          throw new Error('Error: The application was unable to use the Intl API. This may be due to a missing or incomplete polyfill.');\n        }\n\n        // Shopify uses the \"ordinal\" interpolation for ordinal pluralization (i.e., {{ordinal}}), users will expect to\n        // do lookups with `i18n.t(\"key\", { ordinal: 1 })`.\n        // However, the i18next pluralization system uses the \"count\" option for both cardinal and ordinal pluralization\n        // so users will expect to do lookups with `i18n.t(\"key\", { count: 1, ordinal: true })`.\n        // So we support either, using count if provided.\n        // There is an edge case: if `ordinal` were set explicitly to 0, and `count` is provided, we behave as i18next\n        // does, treating it as cardinal pluralization.\n        var needsOrdinalHandling = Boolean(options.ordinal || options.ordinal === 0 && options.count === undefined);\n        var pluralRule = this.i18next.translator.pluralResolver.getRule(code, _objectSpread(_objectSpread({}, options), {}, {\n          ordinal: needsOrdinalHandling\n        }));\n        if (needsOrdinalHandling) {\n          var ruleName = pluralRule.select(options.count === undefined ? options.ordinal : options.count);\n          var pluralSuffix = \"\".concat(this.i18next.options.keySeparator, \"ordinal\").concat(this.i18next.options.keySeparator).concat(ruleName);\n          finalKeys.push(key + pluralSuffix);\n        } else {\n          var _ruleName = pluralRule.select(options.count);\n\n          // Fallback to \"other\" key\n          if (_ruleName !== 'other') {\n            var otherSubkey = \"\".concat(this.i18next.options.keySeparator, \"other\");\n            finalKeys.push(key + otherSubkey);\n          }\n\n          // Pluralization rule key\n          var _pluralSuffix = \"\".concat(this.i18next.options.keySeparator).concat(_ruleName);\n          finalKeys.push(key + _pluralSuffix);\n\n          // Explicit \"0\" and \"1\" keys\n          if (options.count === 0) {\n            var explicit0Subkey = \"\".concat(this.i18next.options.keySeparator, \"0\");\n            finalKeys.push(key + explicit0Subkey);\n          } else if (options.count === 1) {\n            var explicit1Subkey = \"\".concat(this.i18next.options.keySeparator, \"1\");\n            finalKeys.push(key + explicit1Subkey);\n          }\n        }\n      }\n      return finalKeys;\n    }\n  }]);\n  return ShopifyFormat;\n}();\nShopifyFormat.type = 'i18nFormat';\nexport default ShopifyFormat;"], "mappings": ";;;;;;;;AAMA,mBAA6C;AAN7C,SAAS,QAAQ,GAAG,GAAG;AAAE,MAAI,IAAI,OAAO,KAAK,CAAC;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,IAAI,OAAO,sBAAsB,CAAC;AAAG,UAAM,IAAI,EAAE,OAAO,SAAUA,IAAG;AAAE,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAAY,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AAC9P,SAAS,cAAc,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAI,QAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUA,IAAG;AAAE,sBAAgB,GAAGA,IAAG,EAAEA,EAAC,CAAC;AAAA,IAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUA,IAAG;AAAE,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AACtb,SAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,QAAM,eAAe,GAAG;AAAG,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,EAAG,OAAO;AAAE,QAAI,GAAG,IAAI;AAAA,EAAO;AAAE,SAAO;AAAK;AAC3O,SAAS,eAAe,KAAK;AAAE,MAAI,MAAM,aAAa,KAAK,QAAQ;AAAG,SAAO,QAAQ,GAAG,MAAM,WAAW,MAAM,OAAO,GAAG;AAAG;AAC5H,SAAS,aAAa,OAAO,MAAM;AAAE,MAAI,QAAQ,KAAK,MAAM,YAAY,UAAU;AAAM,WAAO;AAAO,MAAI,OAAO,MAAM,OAAO,WAAW;AAAG,MAAI,SAAS,QAAW;AAAE,QAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAAG,QAAI,QAAQ,GAAG,MAAM;AAAU,aAAO;AAAK,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAG;AAAE,UAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AAAG;AAC5X,SAAS,QAAQ,GAAG;AAAE;AAA2B,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,EAAG,IAAI,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAG,GAAG,QAAQ,CAAC;AAAG;AAE7T,IAAI,MAAM,CAAC;AACX,IAAI,OAAO,IAAI;AAGR,SAAS,SAAS,KAAK;AAC5B,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,SAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACjC;AACA,OAAK,KAAK,MAAM,SAAU,QAAQ;AAChC,QAAI,QAAQ;AACV,eAAS,QAAQ,QAAQ;AACvB,YAAI,IAAI,IAAI,MAAM,QAAW;AAC3B,cAAI,IAAI,IAAI,OAAO,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAYO,SAAS,aAAa,cAAc,SAAS,aAAa;AAC/D,MAAI;AACJ,UAAQ,QAAQ,YAAY,GAAG;AAAA,IAC7B,KAAK,UACH;AACE,UAAI,QAAQ,aAAa,MAAM,OAAO;AAGtC,UAAI,MAAM,WAAW,KAAK,QAAQ,WAAW,MAAM,UAAU;AAG3D,YAAI,CAAC,YAAY,WAAO,6BAAe,WAAW,GAAG;AAEnD,4BAAc,2BAAa,aAAa;AAAA,YACtC,KAAK,QAAQ,SAAS;AAAA,UACxB,CAAC;AAAA,QACH;AACA,eAAO,CAAC,MAAM,CAAC,GAAG,aAAa,MAAM,CAAC,CAAC,EAAE,KAAK;AAAA,MAChD;AAGA,aAAO,aAAa,QAAQ,SAAS,WAAW;AAAA,IAClD;AAAA,IACF,KAAK;AACH,UAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,eAAO,aAAa,IAAI,SAAU,MAAM;AACtC,iBAAO,aAAa,MAAM,SAAS,WAAW;AAAA,QAChD,CAAC,EAAE,KAAK;AAAA,MACV;AAGA,UAAI,iBAAiB,QAAQ,iBAAiB,WAAW,sBAAsB,aAAa,WAAW,QAAQ,wBAAwB,UAAU,oBAAoB,UAAU;AAC7K,YAAI,cAAc,aAAa,aAAa,MAAM,UAAU,SAAS,WAAW;AAChF,YAAI,gBAAgB,aAAa,MAAM,UAAU;AAC/C,iBAAO,cAAc,cAAc,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG;AAAA,YACxD,OAAO,cAAc,cAAc,CAAC,GAAG,aAAa,KAAK,GAAG,CAAC,GAAG;AAAA,cAC9D,UAAU;AAAA,YACZ,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF;AAAA,EACJ;AAGA,SAAO;AACT;;;AClFA,SAASC,SAAQ,GAAG,GAAG;AAAE,MAAI,IAAI,OAAO,KAAK,CAAC;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,IAAI,OAAO,sBAAsB,CAAC;AAAG,UAAM,IAAI,EAAE,OAAO,SAAUC,IAAG;AAAE,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAAY,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AAC9P,SAASC,eAAc,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAIF,SAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUC,IAAG;AAAE,MAAAE,iBAAgB,GAAGF,IAAG,EAAEA,EAAC,CAAC;AAAA,IAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAID,SAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUC,IAAG;AAAE,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AACtb,SAASE,iBAAgB,KAAK,KAAK,OAAO;AAAE,QAAMC,gBAAe,GAAG;AAAG,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,EAAG,OAAO;AAAE,QAAI,GAAG,IAAI;AAAA,EAAO;AAAE,SAAO;AAAK;AAC3O,SAAS,eAAeC,MAAK,GAAG;AAAE,SAAO,gBAAgBA,IAAG,KAAK,sBAAsBA,MAAK,CAAC,KAAK,4BAA4BA,MAAK,CAAC,KAAK,iBAAiB;AAAG;AAC7J,SAAS,mBAAmB;AAAE,QAAM,IAAI,UAAU,2IAA2I;AAAG;AAChM,SAAS,4BAA4B,GAAG,QAAQ;AAAE,MAAI,CAAC;AAAG;AAAQ,MAAI,OAAO,MAAM;AAAU,WAAO,kBAAkB,GAAG,MAAM;AAAG,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,MAAI,MAAM,YAAY,EAAE;AAAa,QAAI,EAAE,YAAY;AAAM,MAAI,MAAM,SAAS,MAAM;AAAO,WAAO,MAAM,KAAK,CAAC;AAAG,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AAAG,WAAO,kBAAkB,GAAG,MAAM;AAAG;AAC/Z,SAAS,kBAAkBA,MAAK,KAAK;AAAE,MAAI,OAAO,QAAQ,MAAMA,KAAI;AAAQ,UAAMA,KAAI;AAAQ,WAAS,IAAI,GAAGC,QAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK;AAAK,IAAAA,MAAK,CAAC,IAAID,KAAI,CAAC;AAAG,SAAOC;AAAM;AAClL,SAAS,sBAAsB,GAAG,GAAG;AAAE,MAAI,IAAI,QAAQ,IAAI,OAAO,eAAe,OAAO,UAAU,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAAG,MAAI,QAAQ,GAAG;AAAE,QAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,MAAI,IAAI;AAAI,QAAI;AAAE,UAAI,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG;AAAE,YAAI,OAAO,CAAC,MAAM;AAAG;AAAQ,YAAI;AAAA,MAAI;AAAO,eAAO,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,WAAW,IAAI,IAAI;AAAG;AAAA,IAAE,SAASL,IAAG;AAAE,UAAI,MAAI,IAAIA;AAAA,IAAG,UAAE;AAAU,UAAI;AAAE,YAAI,CAAC,KAAK,QAAQ,EAAE,QAAQ,MAAM,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,MAAM;AAAI;AAAA,MAAQ,UAAE;AAAU,YAAI;AAAG,gBAAM;AAAA,MAAG;AAAA,IAAE;AAAE,WAAO;AAAA,EAAG;AAAE;AACzhB,SAAS,gBAAgBI,MAAK;AAAE,MAAI,MAAM,QAAQA,IAAG;AAAG,WAAOA;AAAK;AACpE,SAASE,SAAQ,GAAG;AAAE;AAA2B,SAAOA,WAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,EAAG,IAAI,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAG,GAAGD,SAAQ,CAAC;AAAG;AAC7T,SAAS,gBAAgB,UAAU,aAAa;AAAE,MAAI,EAAE,oBAAoB,cAAc;AAAE,UAAM,IAAI,UAAU,mCAAmC;AAAA,EAAG;AAAE;AACxJ,SAAS,kBAAkB,QAAQ,OAAO;AAAE,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,QAAI,aAAa,MAAM,CAAC;AAAG,eAAW,aAAa,WAAW,cAAc;AAAO,eAAW,eAAe;AAAM,QAAI,WAAW;AAAY,iBAAW,WAAW;AAAM,WAAO,eAAe,QAAQH,gBAAe,WAAW,GAAG,GAAG,UAAU;AAAA,EAAG;AAAE;AAC5U,SAAS,aAAa,aAAa,YAAY,aAAa;AAAE,MAAI;AAAY,sBAAkB,YAAY,WAAW,UAAU;AAAG,MAAI;AAAa,sBAAkB,aAAa,WAAW;AAAG,SAAO,eAAe,aAAa,aAAa,EAAE,UAAU,MAAM,CAAC;AAAG,SAAO;AAAa;AAC5R,SAASA,gBAAe,KAAK;AAAE,MAAI,MAAMK,cAAa,KAAK,QAAQ;AAAG,SAAOF,SAAQ,GAAG,MAAM,WAAW,MAAM,OAAO,GAAG;AAAG;AAC5H,SAASE,cAAa,OAAO,MAAM;AAAE,MAAIF,SAAQ,KAAK,MAAM,YAAY,UAAU;AAAM,WAAO;AAAO,MAAI,OAAO,MAAM,OAAO,WAAW;AAAG,MAAI,SAAS,QAAW;AAAE,QAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAAG,QAAIA,SAAQ,GAAG,MAAM;AAAU,aAAO;AAAK,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAG;AAAE,UAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AAAG;AAE5X,SAAS,cAAc;AACrB,SAAO,CAAC;AACV;AACA,IAAI,kBAAkB;AACtB,IAAI,gBAA6B,WAAY;AAC3C,WAASG,eAAc,SAAS;AAC9B,oBAAgB,MAAMA,cAAa;AACnC,SAAK,OAAO;AACZ,SAAK,KAAK,MAAM,OAAO;AAAA,EACzB;AACA,eAAaA,gBAAe,CAAC;AAAA,IAC3B,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,SAAS,SAAS;AACrC,UAAI,iBAAiB,WAAW,QAAQ,WAAW,QAAQ,QAAQ,cAAc,CAAC;AAClF,WAAK,UAAgB,SAAS,gBAAgB,SAAS,KAAK,WAAW,CAAC,GAAG,YAAY,CAAC;AACxF,WAAK,UAAU;AAAA,IACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,MAAM,KAAK,SAAS;AAClC,UAAI,QAAQ;AAGZ,UAAI,QAAQ,MAAM;AAChB,eAAO;AAAA,MACT;AAIA,UAAIH,SAAQ,GAAG,MAAM,UAAU;AAC7B,YAAI,SAAS,CAAC;AACd,iBAAS,KAAK,GAAG,kBAAkB,OAAO,QAAQ,GAAG,GAAG,KAAK,gBAAgB,QAAQ,MAAM;AACzF,cAAI,qBAAqB,eAAe,gBAAgB,EAAE,GAAG,CAAC,GAC5D,MAAM,mBAAmB,CAAC,GAC1B,QAAQ,mBAAmB,CAAC;AAC9B,iBAAO,GAAG,IAAI,KAAK,MAAM,OAAO,OAAO;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AAGA,UAAI,UAAU,IAAI,MAAM,eAAe;AACvC,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AACA,UAAI,eAAe;AACnB,cAAQ,QAAQ,SAAU,OAAO;AAC/B,YAAI;AACJ,YAAI,oBAAoB,MAAM,QAAQ,iBAAiB,IAAI;AAC3D,YAAII,SAAQ,sBAAsB,YAAY,QAAQ,SAAS,QAAQ,UAAU,QAAQ,iBAAiB;AAK1G,aAAK,sBAAsB,aAAa,sBAAsB,YAAY,OAAOA,WAAU,UAAU;AACnG,UAAAA,SAAQ,IAAI,KAAK,aAAa,MAAM,QAAQ,gBAAgB,EAAE,OAAOA,MAAK;AAAA,QAC5E;AACA,uBAAqB,aAAa,cAAc,QAAQ,SAASA,YAAW,QAAQ,WAAW,SAAS,SAAS,EAAE;AAAA,MACrH,CAAC;AACD,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,EAKF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,cAAc,WAAW,KAAK,MAAM,IAAI,SAAS;AAC/D,UAAI,sBAAsB,QAAQ,QAAQ,UAAU,UAAa,OAAO,QAAQ,UAAU,YAAY,OAAO,QAAQ,YAAY,QAAQ;AACzI,UAAI,qBAAqB;AACvB,YAAI,CAAC,KAAK,QAAQ,WAAW,eAAe,iBAAiB,GAAG;AAC9D,gBAAM,IAAI,MAAM,6GAA6G;AAAA,QAC/H;AASA,YAAI,uBAAuB,QAAQ,QAAQ,WAAW,QAAQ,YAAY,KAAK,QAAQ,UAAU,MAAS;AAC1G,YAAI,aAAa,KAAK,QAAQ,WAAW,eAAe,QAAQ,MAAMT,eAAcA,eAAc,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA,UAClH,SAAS;AAAA,QACX,CAAC,CAAC;AACF,YAAI,sBAAsB;AACxB,cAAI,WAAW,WAAW,OAAO,QAAQ,UAAU,SAAY,QAAQ,UAAU,QAAQ,KAAK;AAC9F,cAAI,eAAe,GAAG,OAAO,KAAK,QAAQ,QAAQ,cAAc,SAAS,EAAE,OAAO,KAAK,QAAQ,QAAQ,YAAY,EAAE,OAAO,QAAQ;AACpI,oBAAU,KAAK,MAAM,YAAY;AAAA,QACnC,OAAO;AACL,cAAI,YAAY,WAAW,OAAO,QAAQ,KAAK;AAG/C,cAAI,cAAc,SAAS;AACzB,gBAAI,cAAc,GAAG,OAAO,KAAK,QAAQ,QAAQ,cAAc,OAAO;AACtE,sBAAU,KAAK,MAAM,WAAW;AAAA,UAClC;AAGA,cAAI,gBAAgB,GAAG,OAAO,KAAK,QAAQ,QAAQ,YAAY,EAAE,OAAO,SAAS;AACjF,oBAAU,KAAK,MAAM,aAAa;AAGlC,cAAI,QAAQ,UAAU,GAAG;AACvB,gBAAI,kBAAkB,GAAG,OAAO,KAAK,QAAQ,QAAQ,cAAc,GAAG;AACtE,sBAAU,KAAK,MAAM,eAAe;AAAA,UACtC,WAAW,QAAQ,UAAU,GAAG;AAC9B,gBAAI,kBAAkB,GAAG,OAAO,KAAK,QAAQ,QAAQ,cAAc,GAAG;AACtE,sBAAU,KAAK,MAAM,eAAe;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF,CAAC,CAAC;AACF,SAAOQ;AACT,EAAE;AACF,cAAc,OAAO;AACrB,IAAO,aAAQ;", "names": ["r", "o", "ownKeys", "r", "_objectSpread", "_defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "arr", "arr2", "_typeof", "o", "_toPrimitive", "ShopifyFormat", "value"]}