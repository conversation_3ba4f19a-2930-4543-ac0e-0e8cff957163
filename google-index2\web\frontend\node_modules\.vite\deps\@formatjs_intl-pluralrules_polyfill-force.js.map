{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/CanonicalizeLocaleList.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/CanonicalizeTimeZoneName.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/262.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/CoerceOptionsToObject.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/DefaultNumberOption.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/GetNumberOption.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/GetOption.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/GetOptionsObject.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/GetStringOrBooleanOption.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/IsSanctionedSimpleUnitIdentifier.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/IsValidTimeZoneName.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/IsWellFormedCurrencyCode.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/IsWellFormedUnitIdentifier.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/ApplyUnsignedRoundingMode.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/CollapseNumberRange.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/utils.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/ComputeExponentForMagnitude.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/ToRawPrecision.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/ToRawFixed.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/FormatNumericToString.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/ComputeExponent.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/CurrencyDigits.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/FormatApproximately.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/regex.generated.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/digit-mapping.generated.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/format_to_parts.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/PartitionNumberPattern.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/PartitionNumberRangePattern.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/FormatNumericRange.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/FormatNumericRangeToParts.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/FormatNumericToParts.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/GetUnsignedRoundingMode.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/SetNumberFormatDigitOptions.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/SetNumberFormatUnitOptions.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/InitializeNumberFormat.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/PartitionPattern.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/SupportedLocales.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/data.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/types/date-time.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/types/displaynames.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/types/list.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/types/number.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/types/plural-rules.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/types/relative-time.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/ecma402-abstract/lib/index.js", "../../@formatjs/intl-pluralrules/abstract/InitializePluralRules.js", "../../@formatjs/intl-pluralrules/abstract/GetOperands.js", "../../@formatjs/intl-pluralrules/abstract/ResolvePlural.js", "../../@formatjs/intl-pluralrules/get_internal_slots.js", "../../@formatjs/intl-pluralrules/index.js", "../../@formatjs/intl-pluralrules/polyfill-force.js"], "sourcesContent": ["/**\n * http://ecma-international.org/ecma-402/7.0/index.html#sec-canonicalizelocalelist\n * @param locales\n */\nexport function CanonicalizeLocaleList(locales) {\n    // TODO\n    return Intl.getCanonicalLocales(locales);\n}\n", "/**\n * https://tc39.es/ecma402/#sec-canonicalizetimezonename\n * @param tz\n */\nexport function CanonicalizeTimeZoneName(tz, _a) {\n    var zoneNames = _a.zoneNames, uppercaseLinks = _a.uppercaseLinks;\n    var uppercasedTz = tz.toUpperCase();\n    var uppercasedZones = zoneNames.reduce(function (all, z) {\n        all[z.toUpperCase()] = z;\n        return all;\n    }, {});\n    var ianaTimeZone = uppercaseLinks[uppercasedTz] || uppercasedZones[uppercasedTz];\n    if (ianaTimeZone === 'Etc/UTC' || ianaTimeZone === 'Etc/GMT') {\n        return 'UTC';\n    }\n    return ianaTimeZone;\n}\n", "/**\n * https://tc39.es/ecma262/#sec-tostring\n */\nexport function ToString(o) {\n    // Only symbol is irregular...\n    if (typeof o === 'symbol') {\n        throw TypeError('Cannot convert a Symbol value to a string');\n    }\n    return String(o);\n}\n/**\n * https://tc39.es/ecma262/#sec-tonumber\n * @param val\n */\nexport function ToNumber(val) {\n    if (val === undefined) {\n        return NaN;\n    }\n    if (val === null) {\n        return +0;\n    }\n    if (typeof val === 'boolean') {\n        return val ? 1 : +0;\n    }\n    if (typeof val === 'number') {\n        return val;\n    }\n    if (typeof val === 'symbol' || typeof val === 'bigint') {\n        throw new TypeError('Cannot convert symbol/bigint to number');\n    }\n    return Number(val);\n}\n/**\n * https://tc39.es/ecma262/#sec-tointeger\n * @param n\n */\nfunction ToInteger(n) {\n    var number = ToNumber(n);\n    if (isNaN(number) || SameValue(number, -0)) {\n        return 0;\n    }\n    if (isFinite(number)) {\n        return number;\n    }\n    var integer = Math.floor(Math.abs(number));\n    if (number < 0) {\n        integer = -integer;\n    }\n    if (SameValue(integer, -0)) {\n        return 0;\n    }\n    return integer;\n}\n/**\n * https://tc39.es/ecma262/#sec-timeclip\n * @param time\n */\nexport function TimeClip(time) {\n    if (!isFinite(time)) {\n        return NaN;\n    }\n    if (Math.abs(time) > 8.64 * 1e15) {\n        return NaN;\n    }\n    return ToInteger(time);\n}\n/**\n * https://tc39.es/ecma262/#sec-toobject\n * @param arg\n */\nexport function ToObject(arg) {\n    if (arg == null) {\n        throw new TypeError('undefined/null cannot be converted to object');\n    }\n    return Object(arg);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-samevalue\n * @param x\n * @param y\n */\nexport function SameValue(x, y) {\n    if (Object.is) {\n        return Object.is(x, y);\n    }\n    // SameValue algorithm\n    if (x === y) {\n        // Steps 1-5, 7-10\n        // Steps 6.b-6.e: +0 != -0\n        return x !== 0 || 1 / x === 1 / y;\n    }\n    // Step 6.a: NaN == NaN\n    return x !== x && y !== y;\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-arraycreate\n * @param len\n */\nexport function ArrayCreate(len) {\n    return new Array(len);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-hasownproperty\n * @param o\n * @param prop\n */\nexport function HasOwnProperty(o, prop) {\n    return Object.prototype.hasOwnProperty.call(o, prop);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-type\n * @param x\n */\nexport function Type(x) {\n    if (x === null) {\n        return 'Null';\n    }\n    if (typeof x === 'undefined') {\n        return 'Undefined';\n    }\n    if (typeof x === 'function' || typeof x === 'object') {\n        return 'Object';\n    }\n    if (typeof x === 'number') {\n        return 'Number';\n    }\n    if (typeof x === 'boolean') {\n        return 'Boolean';\n    }\n    if (typeof x === 'string') {\n        return 'String';\n    }\n    if (typeof x === 'symbol') {\n        return 'Symbol';\n    }\n    if (typeof x === 'bigint') {\n        return 'BigInt';\n    }\n}\nvar MS_PER_DAY = 86400000;\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#eqn-modulo\n * @param x\n * @param y\n * @return k of the same sign as y\n */\nfunction mod(x, y) {\n    return x - Math.floor(x / y) * y;\n}\n/**\n * https://tc39.es/ecma262/#eqn-Day\n * @param t\n */\nexport function Day(t) {\n    return Math.floor(t / MS_PER_DAY);\n}\n/**\n * https://tc39.es/ecma262/#sec-week-day\n * @param t\n */\nexport function WeekDay(t) {\n    return mod(Day(t) + 4, 7);\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param y\n */\nexport function DayFromYear(y) {\n    return Date.UTC(y, 0) / MS_PER_DAY;\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param y\n */\nexport function TimeFromYear(y) {\n    return Date.UTC(y, 0);\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param t\n */\nexport function YearFromTime(t) {\n    return new Date(t).getUTCFullYear();\n}\nexport function DaysInYear(y) {\n    if (y % 4 !== 0) {\n        return 365;\n    }\n    if (y % 100 !== 0) {\n        return 366;\n    }\n    if (y % 400 !== 0) {\n        return 365;\n    }\n    return 366;\n}\nexport function DayWithinYear(t) {\n    return Day(t) - DayFromYear(YearFromTime(t));\n}\nexport function InLeapYear(t) {\n    return DaysInYear(YearFromTime(t)) === 365 ? 0 : 1;\n}\n/**\n * https://tc39.es/ecma262/#sec-month-number\n * @param t\n */\nexport function MonthFromTime(t) {\n    var dwy = DayWithinYear(t);\n    var leap = InLeapYear(t);\n    if (dwy >= 0 && dwy < 31) {\n        return 0;\n    }\n    if (dwy < 59 + leap) {\n        return 1;\n    }\n    if (dwy < 90 + leap) {\n        return 2;\n    }\n    if (dwy < 120 + leap) {\n        return 3;\n    }\n    if (dwy < 151 + leap) {\n        return 4;\n    }\n    if (dwy < 181 + leap) {\n        return 5;\n    }\n    if (dwy < 212 + leap) {\n        return 6;\n    }\n    if (dwy < 243 + leap) {\n        return 7;\n    }\n    if (dwy < 273 + leap) {\n        return 8;\n    }\n    if (dwy < 304 + leap) {\n        return 9;\n    }\n    if (dwy < 334 + leap) {\n        return 10;\n    }\n    if (dwy < 365 + leap) {\n        return 11;\n    }\n    throw new Error('Invalid time');\n}\nexport function DateFromTime(t) {\n    var dwy = DayWithinYear(t);\n    var mft = MonthFromTime(t);\n    var leap = InLeapYear(t);\n    if (mft === 0) {\n        return dwy + 1;\n    }\n    if (mft === 1) {\n        return dwy - 30;\n    }\n    if (mft === 2) {\n        return dwy - 58 - leap;\n    }\n    if (mft === 3) {\n        return dwy - 89 - leap;\n    }\n    if (mft === 4) {\n        return dwy - 119 - leap;\n    }\n    if (mft === 5) {\n        return dwy - 150 - leap;\n    }\n    if (mft === 6) {\n        return dwy - 180 - leap;\n    }\n    if (mft === 7) {\n        return dwy - 211 - leap;\n    }\n    if (mft === 8) {\n        return dwy - 242 - leap;\n    }\n    if (mft === 9) {\n        return dwy - 272 - leap;\n    }\n    if (mft === 10) {\n        return dwy - 303 - leap;\n    }\n    if (mft === 11) {\n        return dwy - 333 - leap;\n    }\n    throw new Error('Invalid time');\n}\nvar HOURS_PER_DAY = 24;\nvar MINUTES_PER_HOUR = 60;\nvar SECONDS_PER_MINUTE = 60;\nvar MS_PER_SECOND = 1e3;\nvar MS_PER_MINUTE = MS_PER_SECOND * SECONDS_PER_MINUTE;\nvar MS_PER_HOUR = MS_PER_MINUTE * MINUTES_PER_HOUR;\nexport function HourFromTime(t) {\n    return mod(Math.floor(t / MS_PER_HOUR), HOURS_PER_DAY);\n}\nexport function MinFromTime(t) {\n    return mod(Math.floor(t / MS_PER_MINUTE), MINUTES_PER_HOUR);\n}\nexport function SecFromTime(t) {\n    return mod(Math.floor(t / MS_PER_SECOND), SECONDS_PER_MINUTE);\n}\nfunction IsCallable(fn) {\n    return typeof fn === 'function';\n}\n/**\n * The abstract operation OrdinaryHasInstance implements\n * the default algorithm for determining if an object O\n * inherits from the instance object inheritance path\n * provided by constructor C.\n * @param C class\n * @param O object\n * @param internalSlots internalSlots\n */\nexport function OrdinaryHasInstance(C, O, internalSlots) {\n    if (!IsCallable(C)) {\n        return false;\n    }\n    if (internalSlots === null || internalSlots === void 0 ? void 0 : internalSlots.boundTargetFunction) {\n        var BC = internalSlots === null || internalSlots === void 0 ? void 0 : internalSlots.boundTargetFunction;\n        return O instanceof BC;\n    }\n    if (typeof O !== 'object') {\n        return false;\n    }\n    var P = C.prototype;\n    if (typeof P !== 'object') {\n        throw new TypeError('OrdinaryHasInstance called on an object with an invalid prototype property.');\n    }\n    return Object.prototype.isPrototypeOf.call(P, O);\n}\nexport function msFromTime(t) {\n    return mod(t, MS_PER_SECOND);\n}\n", "import { ToObject } from './262';\n/**\n * https://tc39.es/ecma402/#sec-coerceoptionstoobject\n * @param options\n * @returns\n */\nexport function CoerceOptionsToObject(options) {\n    if (typeof options === 'undefined') {\n        return Object.create(null);\n    }\n    return ToObject(options);\n}\n", "/**\n * https://tc39.es/ecma402/#sec-defaultnumberoption\n * @param val\n * @param min\n * @param max\n * @param fallback\n */\nexport function DefaultNumberOption(inputVal, min, max, fallback) {\n    if (inputVal === undefined) {\n        // @ts-expect-error\n        return fallback;\n    }\n    var val = Number(inputVal);\n    if (isNaN(val) || val < min || val > max) {\n        throw new RangeError(\"\".concat(val, \" is outside of range [\").concat(min, \", \").concat(max, \"]\"));\n    }\n    return Math.floor(val);\n}\n", "/**\n * https://tc39.es/ecma402/#sec-getnumberoption\n * @param options\n * @param property\n * @param min\n * @param max\n * @param fallback\n */\nimport { DefaultNumberOption } from './DefaultNumberOption';\nexport function GetNumberOption(options, property, minimum, maximum, fallback) {\n    var val = options[property];\n    return DefaultNumberOption(val, minimum, maximum, fallback);\n}\n", "import { ToString } from './262';\n/**\n * https://tc39.es/ecma402/#sec-getoption\n * @param opts\n * @param prop\n * @param type\n * @param values\n * @param fallback\n */\nexport function GetOption(opts, prop, type, values, fallback) {\n    if (typeof opts !== 'object') {\n        throw new TypeError('Options must be an object');\n    }\n    var value = opts[prop];\n    if (value !== undefined) {\n        if (type !== 'boolean' && type !== 'string') {\n            throw new TypeError('invalid type');\n        }\n        if (type === 'boolean') {\n            value = Boolean(value);\n        }\n        if (type === 'string') {\n            value = ToString(value);\n        }\n        if (values !== undefined && !values.filter(function (val) { return val == value; }).length) {\n            throw new RangeError(\"\".concat(value, \" is not within \").concat(values.join(', ')));\n        }\n        return value;\n    }\n    return fallback;\n}\n", "/**\n * https://tc39.es/ecma402/#sec-getoptionsobject\n * @param options\n * @returns\n */\nexport function GetOptionsObject(options) {\n    if (typeof options === 'undefined') {\n        return Object.create(null);\n    }\n    if (typeof options === 'object') {\n        return options;\n    }\n    throw new TypeError('Options must be an object');\n}\n", "/**\n * https://tc39.es/ecma402/#sec-getstringorbooleanoption\n * @param opts\n * @param prop\n * @param values\n * @param trueValue\n * @param falsyValue\n * @param fallback\n */\nimport { ToString } from './262';\nexport function GetStringOrBooleanOption(opts, prop, values, trueValue, falsyValue, fallback) {\n    var value = opts[prop];\n    if (value === undefined) {\n        return fallback;\n    }\n    if (value === true) {\n        return trueValue;\n    }\n    var valueBoolean = Boolean(value);\n    if (valueBoolean === false) {\n        return falsyValue;\n    }\n    value = ToString(value);\n    if (value === 'true' || value === 'false') {\n        return fallback;\n    }\n    if ((values || []).indexOf(value) === -1) {\n        throw new RangeError(\"Invalid value \".concat(value));\n    }\n    return value;\n}\n", "/**\n * https://tc39.es/ecma402/#table-sanctioned-simple-unit-identifiers\n */\nexport var SANCTIONED_UNITS = [\n    'angle-degree',\n    'area-acre',\n    'area-hectare',\n    'concentr-percent',\n    'digital-bit',\n    'digital-byte',\n    'digital-gigabit',\n    'digital-gigabyte',\n    'digital-kilobit',\n    'digital-kilobyte',\n    'digital-megabit',\n    'digital-megabyte',\n    'digital-petabyte',\n    'digital-terabit',\n    'digital-terabyte',\n    'duration-day',\n    'duration-hour',\n    'duration-millisecond',\n    'duration-minute',\n    'duration-month',\n    'duration-second',\n    'duration-week',\n    'duration-year',\n    'length-centimeter',\n    'length-foot',\n    'length-inch',\n    'length-kilometer',\n    'length-meter',\n    'length-mile-scandinavian',\n    'length-mile',\n    'length-millimeter',\n    'length-yard',\n    'mass-gram',\n    'mass-kilogram',\n    'mass-ounce',\n    'mass-pound',\n    'mass-stone',\n    'temperature-celsius',\n    'temperature-fahrenheit',\n    'volume-fluid-ounce',\n    'volume-gallon',\n    'volume-liter',\n    'volume-milliliter',\n];\n// In CLDR, the unit name always follows the form `namespace-unit` pattern.\n// For example: `digital-bit` instead of `bit`. This function removes the namespace prefix.\nexport function removeUnitNamespace(unit) {\n    return unit.slice(unit.indexOf('-') + 1);\n}\n/**\n * https://tc39.es/ecma402/#table-sanctioned-simple-unit-identifiers\n */\nexport var SIMPLE_UNITS = SANCTIONED_UNITS.map(removeUnitNamespace);\n/**\n * https://tc39.es/ecma402/#sec-issanctionedsimpleunitidentifier\n */\nexport function IsSanctionedSimpleUnitIdentifier(unitIdentifier) {\n    return SIMPLE_UNITS.indexOf(unitIdentifier) > -1;\n}\n", "/**\n * https://tc39.es/ecma402/#sec-isvalidtimezonename\n * @param tz\n * @param implDetails implementation details\n */\nexport function IsValidTimeZoneName(tz, _a) {\n    var zoneNamesFromData = _a.zoneNamesFromData, uppercaseLinks = _a.uppercaseLinks;\n    var uppercasedTz = tz.toUpperCase();\n    var zoneNames = new Set();\n    var linkNames = new Set();\n    zoneNamesFromData.map(function (z) { return z.toUpperCase(); }).forEach(function (z) { return zoneNames.add(z); });\n    Object.keys(uppercaseLinks).forEach(function (linkName) {\n        linkNames.add(linkName.toUpperCase());\n        zoneNames.add(uppercaseLinks[linkName].toUpperCase());\n    });\n    return zoneNames.has(uppercasedTz) || linkNames.has(uppercasedTz);\n}\n", "/**\n * This follows https://tc39.es/ecma402/#sec-case-sensitivity-and-case-mapping\n * @param str string to convert\n */\nfunction toUpperCase(str) {\n    return str.replace(/([a-z])/g, function (_, c) { return c.toUpperCase(); });\n}\nvar NOT_A_Z_REGEX = /[^A-Z]/;\n/**\n * https://tc39.es/ecma402/#sec-iswellformedcurrencycode\n */\nexport function IsWellFormedCurrencyCode(currency) {\n    currency = toUpperCase(currency);\n    if (currency.length !== 3) {\n        return false;\n    }\n    if (NOT_A_Z_REGEX.test(currency)) {\n        return false;\n    }\n    return true;\n}\n", "import { IsSanctionedSimpleUnitIdentifier } from './IsSanctionedSimpleUnitIdentifier';\n/**\n * This follows https://tc39.es/ecma402/#sec-case-sensitivity-and-case-mapping\n * @param str string to convert\n */\nfunction toLowerCase(str) {\n    return str.replace(/([A-Z])/g, function (_, c) { return c.toLowerCase(); });\n}\n/**\n * https://tc39.es/ecma402/#sec-iswellformedunitidentifier\n * @param unit\n */\nexport function IsWellFormedUnitIdentifier(unit) {\n    unit = toLowerCase(unit);\n    if (IsSanctionedSimpleUnitIdentifier(unit)) {\n        return true;\n    }\n    var units = unit.split('-per-');\n    if (units.length !== 2) {\n        return false;\n    }\n    var numerator = units[0], denominator = units[1];\n    if (!IsSanctionedSimpleUnitIdentifier(numerator) ||\n        !IsSanctionedSimpleUnitIdentifier(denominator)) {\n        return false;\n    }\n    return true;\n}\n", "export function ApplyUnsignedRoundingMode(x, r1, r2, unsignedRoundingMode) {\n    if (x === r1)\n        return r1;\n    if (unsignedRoundingMode === undefined) {\n        throw new Error('unsignedRoundingMode is mandatory');\n    }\n    if (unsignedRoundingMode === 'zero') {\n        return r1;\n    }\n    if (unsignedRoundingMode === 'infinity') {\n        return r2;\n    }\n    var d1 = x - r1;\n    var d2 = r2 - x;\n    if (d1 < d2) {\n        return r1;\n    }\n    if (d2 < d1) {\n        return r2;\n    }\n    if (d1 !== d2) {\n        throw new Error('Unexpected error');\n    }\n    if (unsignedRoundingMode === 'half-zero') {\n        return r1;\n    }\n    if (unsignedRoundingMode === 'half-infinity') {\n        return r2;\n    }\n    if (unsignedRoundingMode !== 'half-even') {\n        throw new Error(\"Unexpected value for unsignedRoundingMode: \".concat(unsignedRoundingMode));\n    }\n    var cardinality = (r1 / (r2 - r1)) % 2;\n    if (cardinality === 0) {\n        return r1;\n    }\n    return r2;\n}\n", "/**\n * https://tc39.es/ecma402/#sec-collapsenumberrange\n */\nexport function CollapseNumberRange(result) {\n    return result;\n}\n", "/**\n * Cannot do Math.log(x) / Math.log(10) bc if IEEE floating point issue\n * @param x number\n */\nexport function getMagnitude(x) {\n    // Cannot count string length via Number.toString because it may use scientific notation\n    // for very small or very large numbers.\n    return Math.floor(Math.log(x) * Math.LOG10E);\n}\nexport function repeat(s, times) {\n    if (typeof s.repeat === 'function') {\n        return s.repeat(times);\n    }\n    var arr = new Array(times);\n    for (var i = 0; i < arr.length; i++) {\n        arr[i] = s;\n    }\n    return arr.join('');\n}\nexport function setInternalSlot(map, pl, field, value) {\n    if (!map.get(pl)) {\n        map.set(pl, Object.create(null));\n    }\n    var slots = map.get(pl);\n    slots[field] = value;\n}\nexport function setMultiInternalSlots(map, pl, props) {\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\n        var k = _a[_i];\n        setInternalSlot(map, pl, k, props[k]);\n    }\n}\nexport function getInternalSlot(map, pl, field) {\n    return getMultiInternalSlots(map, pl, field)[field];\n}\nexport function getMultiInternalSlots(map, pl) {\n    var fields = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        fields[_i - 2] = arguments[_i];\n    }\n    var slots = map.get(pl);\n    if (!slots) {\n        throw new TypeError(\"\".concat(pl, \" InternalSlot has not been initialized\"));\n    }\n    return fields.reduce(function (all, f) {\n        all[f] = slots[f];\n        return all;\n    }, Object.create(null));\n}\nexport function isLiteralPart(patternPart) {\n    return patternPart.type === 'literal';\n}\n/*\n  17 ECMAScript Standard Built-in Objects:\n    Every built-in Function object, including constructors, that is not\n    identified as an anonymous function has a name property whose value\n    is a String.\n\n    Unless otherwise specified, the name property of a built-in Function\n    object, if it exists, has the attributes { [[Writable]]: false,\n    [[Enumerable]]: false, [[Configurable]]: true }.\n*/\nexport function defineProperty(target, name, _a) {\n    var value = _a.value;\n    Object.defineProperty(target, name, {\n        configurable: true,\n        enumerable: false,\n        writable: true,\n        value: value,\n    });\n}\n/**\n * 7.3.5 CreateDataProperty\n * @param target\n * @param name\n * @param value\n */\nexport function createDataProperty(target, name, value) {\n    Object.defineProperty(target, name, {\n        configurable: true,\n        enumerable: true,\n        writable: true,\n        value: value,\n    });\n}\nexport var UNICODE_EXTENSION_SEQUENCE_REGEX = /-u(?:-[0-9a-z]{2,8})+/gi;\nexport function invariant(condition, message, Err) {\n    if (Err === void 0) { Err = Error; }\n    if (!condition) {\n        throw new Err(message);\n    }\n}\n", "/**\n * The abstract operation ComputeExponentForMagnitude computes an exponent by which to scale a\n * number of the given magnitude (power of ten of the most significant digit) according to the\n * locale and the desired notation (scientific, engineering, or compact).\n */\nexport function ComputeExponentForMagnitude(numberFormat, magnitude, _a) {\n    var getInternalSlots = _a.getInternalSlots;\n    var internalSlots = getInternalSlots(numberFormat);\n    var notation = internalSlots.notation, dataLocaleData = internalSlots.dataLocaleData, numberingSystem = internalSlots.numberingSystem;\n    switch (notation) {\n        case 'standard':\n            return 0;\n        case 'scientific':\n            return magnitude;\n        case 'engineering':\n            return Math.floor(magnitude / 3) * 3;\n        default: {\n            // Let exponent be an implementation- and locale-dependent (ILD) integer by which to scale a\n            // number of the given magnitude in compact notation for the current locale.\n            var compactDisplay = internalSlots.compactDisplay, style = internalSlots.style, currencyDisplay = internalSlots.currencyDisplay;\n            var thresholdMap = void 0;\n            if (style === 'currency' && currencyDisplay !== 'name') {\n                var currency = dataLocaleData.numbers.currency[numberingSystem] ||\n                    dataLocaleData.numbers.currency[dataLocaleData.numbers.nu[0]];\n                thresholdMap = currency.short;\n            }\n            else {\n                var decimal = dataLocaleData.numbers.decimal[numberingSystem] ||\n                    dataLocaleData.numbers.decimal[dataLocaleData.numbers.nu[0]];\n                thresholdMap = compactDisplay === 'long' ? decimal.long : decimal.short;\n            }\n            if (!thresholdMap) {\n                return 0;\n            }\n            var num = String(Math.pow(10, magnitude));\n            var thresholds = Object.keys(thresholdMap); // TODO: this can be pre-processed\n            if (num < thresholds[0]) {\n                return 0;\n            }\n            if (num > thresholds[thresholds.length - 1]) {\n                return thresholds[thresholds.length - 1].length - 1;\n            }\n            var i = thresholds.indexOf(num);\n            if (i === -1) {\n                return 0;\n            }\n            // See https://unicode.org/reports/tr35/tr35-numbers.html#Compact_Number_Formats\n            // Special handling if the pattern is precisely `0`.\n            var magnitudeKey = thresholds[i];\n            // TODO: do we need to handle plural here?\n            var compactPattern = thresholdMap[magnitudeKey].other;\n            if (compactPattern === '0') {\n                return 0;\n            }\n            // Example: in zh-TW, `10000000` maps to `0000萬`. So we need to return 8 - 4 = 4 here.\n            return (magnitudeKey.length -\n                thresholdMap[magnitudeKey].other.match(/0+/)[0].length);\n        }\n    }\n}\n", "import { repeat, getMagnitude } from '../utils';\nexport function ToRawPrecision(x, minPrecision, maxPrecision) {\n    var p = maxPrecision;\n    var m;\n    var e;\n    var xFinal;\n    if (x === 0) {\n        m = repeat('0', p);\n        e = 0;\n        xFinal = 0;\n    }\n    else {\n        var xToString = x.toString();\n        // If xToString is formatted as scientific notation, the number is either very small or very\n        // large. If the precision of the formatted string is lower that requested max precision, we\n        // should still infer them from the formatted string, otherwise the formatted result might have\n        // precision loss (e.g. 1e41 will not have 0 in every trailing digits).\n        var xToStringExponentIndex = xToString.indexOf('e');\n        var _a = xToString.split('e'), xToStringMantissa = _a[0], xToStringExponent = _a[1];\n        var xToStringMantissaWithoutDecimalPoint = xToStringMantissa.replace('.', '');\n        if (xToStringExponentIndex >= 0 &&\n            xToStringMantissaWithoutDecimalPoint.length <= p) {\n            e = +xToStringExponent;\n            m =\n                xToStringMantissaWithoutDecimalPoint +\n                    repeat('0', p - xToStringMantissaWithoutDecimalPoint.length);\n            xFinal = x;\n        }\n        else {\n            e = getMagnitude(x);\n            var decimalPlaceOffset = e - p + 1;\n            // n is the integer containing the required precision digits. To derive the formatted string,\n            // we will adjust its decimal place in the logic below.\n            var n = Math.round(adjustDecimalPlace(x, decimalPlaceOffset));\n            // The rounding caused the change of magnitude, so we should increment `e` by 1.\n            if (adjustDecimalPlace(n, p - 1) >= 10) {\n                e = e + 1;\n                // Divide n by 10 to swallow one precision.\n                n = Math.floor(n / 10);\n            }\n            m = n.toString();\n            // Equivalent of n * 10 ** (e - p + 1)\n            xFinal = adjustDecimalPlace(n, p - 1 - e);\n        }\n    }\n    var int;\n    if (e >= p - 1) {\n        m = m + repeat('0', e - p + 1);\n        int = e + 1;\n    }\n    else if (e >= 0) {\n        m = \"\".concat(m.slice(0, e + 1), \".\").concat(m.slice(e + 1));\n        int = e + 1;\n    }\n    else {\n        m = \"0.\".concat(repeat('0', -e - 1)).concat(m);\n        int = 1;\n    }\n    if (m.indexOf('.') >= 0 && maxPrecision > minPrecision) {\n        var cut = maxPrecision - minPrecision;\n        while (cut > 0 && m[m.length - 1] === '0') {\n            m = m.slice(0, -1);\n            cut--;\n        }\n        if (m[m.length - 1] === '.') {\n            m = m.slice(0, -1);\n        }\n    }\n    return { formattedString: m, roundedNumber: xFinal, integerDigitsCount: int };\n    // x / (10 ** magnitude), but try to preserve as much floating point precision as possible.\n    function adjustDecimalPlace(x, magnitude) {\n        return magnitude < 0 ? x * Math.pow(10, -magnitude) : x / Math.pow(10, magnitude);\n    }\n}\n", "import { repeat } from '../utils';\n/**\n * TODO: dedup with intl-pluralrules and support BigInt\n * https://tc39.es/ecma402/#sec-torawfixed\n * @param x a finite non-negative Number or BigInt\n * @param minFraction and integer between 0 and 20\n * @param maxFraction and integer between 0 and 20\n */\nexport function ToRawFixed(x, minFraction, maxFraction) {\n    var f = maxFraction;\n    var n = Math.round(x * Math.pow(10, f));\n    var xFinal = n / Math.pow(10, f);\n    // n is a positive integer, but it is possible to be greater than 1e21.\n    // In such case we will go the slow path.\n    // See also: https://tc39.es/ecma262/#sec-numeric-types-number-tostring\n    var m;\n    if (n < 1e21) {\n        m = n.toString();\n    }\n    else {\n        m = n.toString();\n        var _a = m.split('e'), mantissa = _a[0], exponent = _a[1];\n        m = mantissa.replace('.', '');\n        m = m + repeat('0', Math.max(+exponent - m.length + 1, 0));\n    }\n    var int;\n    if (f !== 0) {\n        var k = m.length;\n        if (k <= f) {\n            var z = repeat('0', f + 1 - k);\n            m = z + m;\n            k = f + 1;\n        }\n        var a = m.slice(0, k - f);\n        var b = m.slice(k - f);\n        m = \"\".concat(a, \".\").concat(b);\n        int = a.length;\n    }\n    else {\n        int = m.length;\n    }\n    var cut = maxFraction - minFraction;\n    while (cut > 0 && m[m.length - 1] === '0') {\n        m = m.slice(0, -1);\n        cut--;\n    }\n    if (m[m.length - 1] === '.') {\n        m = m.slice(0, -1);\n    }\n    return { formattedString: m, roundedNumber: xFinal, integerDigitsCount: int };\n}\n", "import { SameValue } from '../262';\nimport { ToRawPrecision } from './ToRawPrecision';\nimport { repeat } from '../utils';\nimport { ToRawFixed } from './ToRawFixed';\n/**\n * https://tc39.es/ecma402/#sec-formatnumberstring\n */\nexport function FormatNumericToString(intlObject, x) {\n    var isNegative = x < 0 || SameValue(x, -0);\n    if (isNegative) {\n        x = -x;\n    }\n    var result;\n    var rourndingType = intlObject.roundingType;\n    switch (rourndingType) {\n        case 'significantDigits':\n            result = ToRawPrecision(x, intlObject.minimumSignificantDigits, intlObject.maximumSignificantDigits);\n            break;\n        case 'fractionDigits':\n            result = ToRawFixed(x, intlObject.minimumFractionDigits, intlObject.maximumFractionDigits);\n            break;\n        default:\n            result = ToRawPrecision(x, 1, 2);\n            if (result.integerDigitsCount > 1) {\n                result = ToRawFixed(x, 0, 0);\n            }\n            break;\n    }\n    x = result.roundedNumber;\n    var string = result.formattedString;\n    var int = result.integerDigitsCount;\n    var minInteger = intlObject.minimumIntegerDigits;\n    if (int < minInteger) {\n        var forwardZeros = repeat('0', minInteger - int);\n        string = forwardZeros + string;\n    }\n    if (isNegative) {\n        x = -x;\n    }\n    return { roundedNumber: x, formattedString: string };\n}\n", "import { getMagnitude } from '../utils';\nimport { ComputeExponentForMagnitude } from './ComputeExponentForMagnitude';\nimport { FormatNumericToString } from './FormatNumericToString';\n/**\n * The abstract operation ComputeExponent computes an exponent (power of ten) by which to scale x\n * according to the number formatting settings. It handles cases such as 999 rounding up to 1000,\n * requiring a different exponent.\n *\n * NOT IN SPEC: it returns [exponent, magnitude].\n */\nexport function ComputeExponent(numberFormat, x, _a) {\n    var getInternalSlots = _a.getInternalSlots;\n    if (x === 0) {\n        return [0, 0];\n    }\n    if (x < 0) {\n        x = -x;\n    }\n    var magnitude = getMagnitude(x);\n    var exponent = ComputeExponentForMagnitude(numberFormat, magnitude, {\n        getInternalSlots: getInternalSlots,\n    });\n    // Preserve more precision by doing multiplication when exponent is negative.\n    x = exponent < 0 ? x * Math.pow(10, -exponent) : x / Math.pow(10, exponent);\n    var formatNumberResult = FormatNumericToString(getInternalSlots(numberFormat), x);\n    if (formatNumberResult.roundedNumber === 0) {\n        return [exponent, magnitude];\n    }\n    var newMagnitude = getMagnitude(formatNumberResult.roundedNumber);\n    if (newMagnitude === magnitude - exponent) {\n        return [exponent, magnitude];\n    }\n    return [\n        ComputeExponentForMagnitude(numberFormat, magnitude + 1, {\n            getInternalSlots: getInternalSlots,\n        }),\n        magnitude + 1,\n    ];\n}\n", "import { HasOwnProperty } from '../262';\n/**\n * https://tc39.es/ecma402/#sec-currencydigits\n */\nexport function CurrencyDigits(c, _a) {\n    var currencyDigitsData = _a.currencyDigitsData;\n    return HasOwnProperty(currencyDigitsData, c)\n        ? currencyDigitsData[c]\n        : 2;\n}\n", "/**\n * https://tc39.es/ecma402/#sec-formatapproximately\n */\nexport function FormatApproximately(numberFormat, result, _a) {\n    var getInternalSlots = _a.getInternalSlots;\n    var internalSlots = getInternalSlots(numberFormat);\n    var symbols = internalSlots.dataLocaleData.numbers.symbols[internalSlots.numberingSystem];\n    var approximatelySign = symbols.approximatelySign;\n    result.push({ type: 'approximatelySign', value: approximatelySign });\n    return result;\n}\n", "// @generated from regex-gen.ts\nexport var S_UNICODE_REGEX = /[\\$\\+<->\\^`\\|~\\xA2-\\xA6\\xA8\\xA9\\xAC\\xAE-\\xB1\\xB4\\xB8\\xD7\\xF7\\u02C2-\\u02C5\\u02D2-\\u02DF\\u02E5-\\u02EB\\u02ED\\u02EF-\\u02FF\\u0375\\u0384\\u0385\\u03F6\\u0482\\u058D-\\u058F\\u0606-\\u0608\\u060B\\u060E\\u060F\\u06DE\\u06E9\\u06FD\\u06FE\\u07F6\\u07FE\\u07FF\\u09F2\\u09F3\\u09FA\\u09FB\\u0AF1\\u0B70\\u0BF3-\\u0BFA\\u0C7F\\u0D4F\\u0D79\\u0E3F\\u0F01-\\u0F03\\u0F13\\u0F15-\\u0F17\\u0F1A-\\u0F1F\\u0F34\\u0F36\\u0F38\\u0FBE-\\u0FC5\\u0FC7-\\u0FCC\\u0FCE\\u0FCF\\u0FD5-\\u0FD8\\u109E\\u109F\\u1390-\\u1399\\u166D\\u17DB\\u1940\\u19DE-\\u19FF\\u1B61-\\u1B6A\\u1B74-\\u1B7C\\u1FBD\\u1FBF-\\u1FC1\\u1FCD-\\u1FCF\\u1FDD-\\u1FDF\\u1FED-\\u1FEF\\u1FFD\\u1FFE\\u2044\\u2052\\u207A-\\u207C\\u208A-\\u208C\\u20A0-\\u20BF\\u2100\\u2101\\u2103-\\u2106\\u2108\\u2109\\u2114\\u2116-\\u2118\\u211E-\\u2123\\u2125\\u2127\\u2129\\u212E\\u213A\\u213B\\u2140-\\u2144\\u214A-\\u214D\\u214F\\u218A\\u218B\\u2190-\\u2307\\u230C-\\u2328\\u232B-\\u2426\\u2440-\\u244A\\u249C-\\u24E9\\u2500-\\u2767\\u2794-\\u27C4\\u27C7-\\u27E5\\u27F0-\\u2982\\u2999-\\u29D7\\u29DC-\\u29FB\\u29FE-\\u2B73\\u2B76-\\u2B95\\u2B97-\\u2BFF\\u2CE5-\\u2CEA\\u2E50\\u2E51\\u2E80-\\u2E99\\u2E9B-\\u2EF3\\u2F00-\\u2FD5\\u2FF0-\\u2FFB\\u3004\\u3012\\u3013\\u3020\\u3036\\u3037\\u303E\\u303F\\u309B\\u309C\\u3190\\u3191\\u3196-\\u319F\\u31C0-\\u31E3\\u3200-\\u321E\\u322A-\\u3247\\u3250\\u3260-\\u327F\\u328A-\\u32B0\\u32C0-\\u33FF\\u4DC0-\\u4DFF\\uA490-\\uA4C6\\uA700-\\uA716\\uA720\\uA721\\uA789\\uA78A\\uA828-\\uA82B\\uA836-\\uA839\\uAA77-\\uAA79\\uAB5B\\uAB6A\\uAB6B\\uFB29\\uFBB2-\\uFBC1\\uFDFC\\uFDFD\\uFE62\\uFE64-\\uFE66\\uFE69\\uFF04\\uFF0B\\uFF1C-\\uFF1E\\uFF3E\\uFF40\\uFF5C\\uFF5E\\uFFE0-\\uFFE6\\uFFE8-\\uFFEE\\uFFFC\\uFFFD]|\\uD800[\\uDD37-\\uDD3F\\uDD79-\\uDD89\\uDD8C-\\uDD8E\\uDD90-\\uDD9C\\uDDA0\\uDDD0-\\uDDFC]|\\uD802[\\uDC77\\uDC78\\uDEC8]|\\uD805\\uDF3F|\\uD807[\\uDFD5-\\uDFF1]|\\uD81A[\\uDF3C-\\uDF3F\\uDF45]|\\uD82F\\uDC9C|\\uD834[\\uDC00-\\uDCF5\\uDD00-\\uDD26\\uDD29-\\uDD64\\uDD6A-\\uDD6C\\uDD83\\uDD84\\uDD8C-\\uDDA9\\uDDAE-\\uDDE8\\uDE00-\\uDE41\\uDE45\\uDF00-\\uDF56]|\\uD835[\\uDEC1\\uDEDB\\uDEFB\\uDF15\\uDF35\\uDF4F\\uDF6F\\uDF89\\uDFA9\\uDFC3]|\\uD836[\\uDC00-\\uDDFF\\uDE37-\\uDE3A\\uDE6D-\\uDE74\\uDE76-\\uDE83\\uDE85\\uDE86]|\\uD838[\\uDD4F\\uDEFF]|\\uD83B[\\uDCAC\\uDCB0\\uDD2E\\uDEF0\\uDEF1]|\\uD83C[\\uDC00-\\uDC2B\\uDC30-\\uDC93\\uDCA0-\\uDCAE\\uDCB1-\\uDCBF\\uDCC1-\\uDCCF\\uDCD1-\\uDCF5\\uDD0D-\\uDDAD\\uDDE6-\\uDE02\\uDE10-\\uDE3B\\uDE40-\\uDE48\\uDE50\\uDE51\\uDE60-\\uDE65\\uDF00-\\uDFFF]|\\uD83D[\\uDC00-\\uDED7\\uDEE0-\\uDEEC\\uDEF0-\\uDEFC\\uDF00-\\uDF73\\uDF80-\\uDFD8\\uDFE0-\\uDFEB]|\\uD83E[\\uDC00-\\uDC0B\\uDC10-\\uDC47\\uDC50-\\uDC59\\uDC60-\\uDC87\\uDC90-\\uDCAD\\uDCB0\\uDCB1\\uDD00-\\uDD78\\uDD7A-\\uDDCB\\uDDCD-\\uDE53\\uDE60-\\uDE6D\\uDE70-\\uDE74\\uDE78-\\uDE7A\\uDE80-\\uDE86\\uDE90-\\uDEA8\\uDEB0-\\uDEB6\\uDEC0-\\uDEC2\\uDED0-\\uDED6\\uDF00-\\uDF92\\uDF94-\\uDFCA]/;\n", "export var digitMapping = {\n    \"adlm\": [\n        \"𞥐\",\n        \"𞥑\",\n        \"𞥒\",\n        \"𞥓\",\n        \"𞥔\",\n        \"𞥕\",\n        \"𞥖\",\n        \"𞥗\",\n        \"𞥘\",\n        \"𞥙\"\n    ],\n    \"ahom\": [\n        \"𑜰\",\n        \"𑜱\",\n        \"𑜲\",\n        \"𑜳\",\n        \"𑜴\",\n        \"𑜵\",\n        \"𑜶\",\n        \"𑜷\",\n        \"𑜸\",\n        \"𑜹\"\n    ],\n    \"arab\": [\n        \"٠\",\n        \"١\",\n        \"٢\",\n        \"٣\",\n        \"٤\",\n        \"٥\",\n        \"٦\",\n        \"٧\",\n        \"٨\",\n        \"٩\"\n    ],\n    \"arabext\": [\n        \"۰\",\n        \"۱\",\n        \"۲\",\n        \"۳\",\n        \"۴\",\n        \"۵\",\n        \"۶\",\n        \"۷\",\n        \"۸\",\n        \"۹\"\n    ],\n    \"bali\": [\n        \"᭐\",\n        \"᭑\",\n        \"᭒\",\n        \"᭓\",\n        \"᭔\",\n        \"᭕\",\n        \"᭖\",\n        \"᭗\",\n        \"᭘\",\n        \"᭙\"\n    ],\n    \"beng\": [\n        \"০\",\n        \"১\",\n        \"২\",\n        \"৩\",\n        \"৪\",\n        \"৫\",\n        \"৬\",\n        \"৭\",\n        \"৮\",\n        \"৯\"\n    ],\n    \"bhks\": [\n        \"𑱐\",\n        \"𑱑\",\n        \"𑱒\",\n        \"𑱓\",\n        \"𑱔\",\n        \"𑱕\",\n        \"𑱖\",\n        \"𑱗\",\n        \"𑱘\",\n        \"𑱙\"\n    ],\n    \"brah\": [\n        \"𑁦\",\n        \"𑁧\",\n        \"𑁨\",\n        \"𑁩\",\n        \"𑁪\",\n        \"𑁫\",\n        \"𑁬\",\n        \"𑁭\",\n        \"𑁮\",\n        \"𑁯\"\n    ],\n    \"cakm\": [\n        \"𑄶\",\n        \"𑄷\",\n        \"𑄸\",\n        \"𑄹\",\n        \"𑄺\",\n        \"𑄻\",\n        \"𑄼\",\n        \"𑄽\",\n        \"𑄾\",\n        \"𑄿\"\n    ],\n    \"cham\": [\n        \"꩐\",\n        \"꩑\",\n        \"꩒\",\n        \"꩓\",\n        \"꩔\",\n        \"꩕\",\n        \"꩖\",\n        \"꩗\",\n        \"꩘\",\n        \"꩙\"\n    ],\n    \"deva\": [\n        \"०\",\n        \"१\",\n        \"२\",\n        \"३\",\n        \"४\",\n        \"५\",\n        \"६\",\n        \"७\",\n        \"८\",\n        \"९\"\n    ],\n    \"diak\": [\n        \"𑥐\",\n        \"𑥑\",\n        \"𑥒\",\n        \"𑥓\",\n        \"𑥔\",\n        \"𑥕\",\n        \"𑥖\",\n        \"𑥗\",\n        \"𑥘\",\n        \"𑥙\"\n    ],\n    \"fullwide\": [\n        \"０\",\n        \"１\",\n        \"２\",\n        \"３\",\n        \"４\",\n        \"５\",\n        \"６\",\n        \"７\",\n        \"８\",\n        \"９\"\n    ],\n    \"gong\": [\n        \"𑶠\",\n        \"𑶡\",\n        \"𑶢\",\n        \"𑶣\",\n        \"𑶤\",\n        \"𑶥\",\n        \"𑶦\",\n        \"𑶧\",\n        \"𑶨\",\n        \"𑶩\"\n    ],\n    \"gonm\": [\n        \"𑵐\",\n        \"𑵑\",\n        \"𑵒\",\n        \"𑵓\",\n        \"𑵔\",\n        \"𑵕\",\n        \"𑵖\",\n        \"𑵗\",\n        \"𑵘\",\n        \"𑵙\"\n    ],\n    \"gujr\": [\n        \"૦\",\n        \"૧\",\n        \"૨\",\n        \"૩\",\n        \"૪\",\n        \"૫\",\n        \"૬\",\n        \"૭\",\n        \"૮\",\n        \"૯\"\n    ],\n    \"guru\": [\n        \"੦\",\n        \"੧\",\n        \"੨\",\n        \"੩\",\n        \"੪\",\n        \"੫\",\n        \"੬\",\n        \"੭\",\n        \"੮\",\n        \"੯\"\n    ],\n    \"hanidec\": [\n        \"〇\",\n        \"一\",\n        \"二\",\n        \"三\",\n        \"四\",\n        \"五\",\n        \"六\",\n        \"七\",\n        \"八\",\n        \"九\"\n    ],\n    \"hmng\": [\n        \"𖭐\",\n        \"𖭑\",\n        \"𖭒\",\n        \"𖭓\",\n        \"𖭔\",\n        \"𖭕\",\n        \"𖭖\",\n        \"𖭗\",\n        \"𖭘\",\n        \"𖭙\"\n    ],\n    \"hmnp\": [\n        \"𞅀\",\n        \"𞅁\",\n        \"𞅂\",\n        \"𞅃\",\n        \"𞅄\",\n        \"𞅅\",\n        \"𞅆\",\n        \"𞅇\",\n        \"𞅈\",\n        \"𞅉\"\n    ],\n    \"java\": [\n        \"꧐\",\n        \"꧑\",\n        \"꧒\",\n        \"꧓\",\n        \"꧔\",\n        \"꧕\",\n        \"꧖\",\n        \"꧗\",\n        \"꧘\",\n        \"꧙\"\n    ],\n    \"kali\": [\n        \"꤀\",\n        \"꤁\",\n        \"꤂\",\n        \"꤃\",\n        \"꤄\",\n        \"꤅\",\n        \"꤆\",\n        \"꤇\",\n        \"꤈\",\n        \"꤉\"\n    ],\n    \"khmr\": [\n        \"០\",\n        \"១\",\n        \"២\",\n        \"៣\",\n        \"៤\",\n        \"៥\",\n        \"៦\",\n        \"៧\",\n        \"៨\",\n        \"៩\"\n    ],\n    \"knda\": [\n        \"೦\",\n        \"೧\",\n        \"೨\",\n        \"೩\",\n        \"೪\",\n        \"೫\",\n        \"೬\",\n        \"೭\",\n        \"೮\",\n        \"೯\"\n    ],\n    \"lana\": [\n        \"᪀\",\n        \"᪁\",\n        \"᪂\",\n        \"᪃\",\n        \"᪄\",\n        \"᪅\",\n        \"᪆\",\n        \"᪇\",\n        \"᪈\",\n        \"᪉\"\n    ],\n    \"lanatham\": [\n        \"᪐\",\n        \"᪑\",\n        \"᪒\",\n        \"᪓\",\n        \"᪔\",\n        \"᪕\",\n        \"᪖\",\n        \"᪗\",\n        \"᪘\",\n        \"᪙\"\n    ],\n    \"laoo\": [\n        \"໐\",\n        \"໑\",\n        \"໒\",\n        \"໓\",\n        \"໔\",\n        \"໕\",\n        \"໖\",\n        \"໗\",\n        \"໘\",\n        \"໙\"\n    ],\n    \"lepc\": [\n        \"᪐\",\n        \"᪑\",\n        \"᪒\",\n        \"᪓\",\n        \"᪔\",\n        \"᪕\",\n        \"᪖\",\n        \"᪗\",\n        \"᪘\",\n        \"᪙\"\n    ],\n    \"limb\": [\n        \"᥆\",\n        \"᥇\",\n        \"᥈\",\n        \"᥉\",\n        \"᥊\",\n        \"᥋\",\n        \"᥌\",\n        \"᥍\",\n        \"᥎\",\n        \"᥏\"\n    ],\n    \"mathbold\": [\n        \"𝟎\",\n        \"𝟏\",\n        \"𝟐\",\n        \"𝟑\",\n        \"𝟒\",\n        \"𝟓\",\n        \"𝟔\",\n        \"𝟕\",\n        \"𝟖\",\n        \"𝟗\"\n    ],\n    \"mathdbl\": [\n        \"𝟘\",\n        \"𝟙\",\n        \"𝟚\",\n        \"𝟛\",\n        \"𝟜\",\n        \"𝟝\",\n        \"𝟞\",\n        \"𝟟\",\n        \"𝟠\",\n        \"𝟡\"\n    ],\n    \"mathmono\": [\n        \"𝟶\",\n        \"𝟷\",\n        \"𝟸\",\n        \"𝟹\",\n        \"𝟺\",\n        \"𝟻\",\n        \"𝟼\",\n        \"𝟽\",\n        \"𝟾\",\n        \"𝟿\"\n    ],\n    \"mathsanb\": [\n        \"𝟬\",\n        \"𝟭\",\n        \"𝟮\",\n        \"𝟯\",\n        \"𝟰\",\n        \"𝟱\",\n        \"𝟲\",\n        \"𝟳\",\n        \"𝟴\",\n        \"𝟵\"\n    ],\n    \"mathsans\": [\n        \"𝟢\",\n        \"𝟣\",\n        \"𝟤\",\n        \"𝟥\",\n        \"𝟦\",\n        \"𝟧\",\n        \"𝟨\",\n        \"𝟩\",\n        \"𝟪\",\n        \"𝟫\"\n    ],\n    \"mlym\": [\n        \"൦\",\n        \"൧\",\n        \"൨\",\n        \"൩\",\n        \"൪\",\n        \"൫\",\n        \"൬\",\n        \"൭\",\n        \"൮\",\n        \"൯\"\n    ],\n    \"modi\": [\n        \"𑙐\",\n        \"𑙑\",\n        \"𑙒\",\n        \"𑙓\",\n        \"𑙔\",\n        \"𑙕\",\n        \"𑙖\",\n        \"𑙗\",\n        \"𑙘\",\n        \"𑙙\"\n    ],\n    \"mong\": [\n        \"᠐\",\n        \"᠑\",\n        \"᠒\",\n        \"᠓\",\n        \"᠔\",\n        \"᠕\",\n        \"᠖\",\n        \"᠗\",\n        \"᠘\",\n        \"᠙\"\n    ],\n    \"mroo\": [\n        \"𖩠\",\n        \"𖩡\",\n        \"𖩢\",\n        \"𖩣\",\n        \"𖩤\",\n        \"𖩥\",\n        \"𖩦\",\n        \"𖩧\",\n        \"𖩨\",\n        \"𖩩\"\n    ],\n    \"mtei\": [\n        \"꯰\",\n        \"꯱\",\n        \"꯲\",\n        \"꯳\",\n        \"꯴\",\n        \"꯵\",\n        \"꯶\",\n        \"꯷\",\n        \"꯸\",\n        \"꯹\"\n    ],\n    \"mymr\": [\n        \"၀\",\n        \"၁\",\n        \"၂\",\n        \"၃\",\n        \"၄\",\n        \"၅\",\n        \"၆\",\n        \"၇\",\n        \"၈\",\n        \"၉\"\n    ],\n    \"mymrshan\": [\n        \"႐\",\n        \"႑\",\n        \"႒\",\n        \"႓\",\n        \"႔\",\n        \"႕\",\n        \"႖\",\n        \"႗\",\n        \"႘\",\n        \"႙\"\n    ],\n    \"mymrtlng\": [\n        \"꧰\",\n        \"꧱\",\n        \"꧲\",\n        \"꧳\",\n        \"꧴\",\n        \"꧵\",\n        \"꧶\",\n        \"꧷\",\n        \"꧸\",\n        \"꧹\"\n    ],\n    \"newa\": [\n        \"𑑐\",\n        \"𑑑\",\n        \"𑑒\",\n        \"𑑓\",\n        \"𑑔\",\n        \"𑑕\",\n        \"𑑖\",\n        \"𑑗\",\n        \"𑑘\",\n        \"𑑙\"\n    ],\n    \"nkoo\": [\n        \"߀\",\n        \"߁\",\n        \"߂\",\n        \"߃\",\n        \"߄\",\n        \"߅\",\n        \"߆\",\n        \"߇\",\n        \"߈\",\n        \"߉\"\n    ],\n    \"olck\": [\n        \"᱐\",\n        \"᱑\",\n        \"᱒\",\n        \"᱓\",\n        \"᱔\",\n        \"᱕\",\n        \"᱖\",\n        \"᱗\",\n        \"᱘\",\n        \"᱙\"\n    ],\n    \"orya\": [\n        \"୦\",\n        \"୧\",\n        \"୨\",\n        \"୩\",\n        \"୪\",\n        \"୫\",\n        \"୬\",\n        \"୭\",\n        \"୮\",\n        \"୯\"\n    ],\n    \"osma\": [\n        \"𐒠\",\n        \"𐒡\",\n        \"𐒢\",\n        \"𐒣\",\n        \"𐒤\",\n        \"𐒥\",\n        \"𐒦\",\n        \"𐒧\",\n        \"𐒨\",\n        \"𐒩\"\n    ],\n    \"rohg\": [\n        \"𐴰\",\n        \"𐴱\",\n        \"𐴲\",\n        \"𐴳\",\n        \"𐴴\",\n        \"𐴵\",\n        \"𐴶\",\n        \"𐴷\",\n        \"𐴸\",\n        \"𐴹\"\n    ],\n    \"saur\": [\n        \"꣐\",\n        \"꣑\",\n        \"꣒\",\n        \"꣓\",\n        \"꣔\",\n        \"꣕\",\n        \"꣖\",\n        \"꣗\",\n        \"꣘\",\n        \"꣙\"\n    ],\n    \"segment\": [\n        \"🯰\",\n        \"🯱\",\n        \"🯲\",\n        \"🯳\",\n        \"🯴\",\n        \"🯵\",\n        \"🯶\",\n        \"🯷\",\n        \"🯸\",\n        \"🯹\"\n    ],\n    \"shrd\": [\n        \"𑇐\",\n        \"𑇑\",\n        \"𑇒\",\n        \"𑇓\",\n        \"𑇔\",\n        \"𑇕\",\n        \"𑇖\",\n        \"𑇗\",\n        \"𑇘\",\n        \"𑇙\"\n    ],\n    \"sind\": [\n        \"𑋰\",\n        \"𑋱\",\n        \"𑋲\",\n        \"𑋳\",\n        \"𑋴\",\n        \"𑋵\",\n        \"𑋶\",\n        \"𑋷\",\n        \"𑋸\",\n        \"𑋹\"\n    ],\n    \"sinh\": [\n        \"෦\",\n        \"෧\",\n        \"෨\",\n        \"෩\",\n        \"෪\",\n        \"෫\",\n        \"෬\",\n        \"෭\",\n        \"෮\",\n        \"෯\"\n    ],\n    \"sora\": [\n        \"𑃰\",\n        \"𑃱\",\n        \"𑃲\",\n        \"𑃳\",\n        \"𑃴\",\n        \"𑃵\",\n        \"𑃶\",\n        \"𑃷\",\n        \"𑃸\",\n        \"𑃹\"\n    ],\n    \"sund\": [\n        \"᮰\",\n        \"᮱\",\n        \"᮲\",\n        \"᮳\",\n        \"᮴\",\n        \"᮵\",\n        \"᮶\",\n        \"᮷\",\n        \"᮸\",\n        \"᮹\"\n    ],\n    \"takr\": [\n        \"𑛀\",\n        \"𑛁\",\n        \"𑛂\",\n        \"𑛃\",\n        \"𑛄\",\n        \"𑛅\",\n        \"𑛆\",\n        \"𑛇\",\n        \"𑛈\",\n        \"𑛉\"\n    ],\n    \"talu\": [\n        \"᧐\",\n        \"᧑\",\n        \"᧒\",\n        \"᧓\",\n        \"᧔\",\n        \"᧕\",\n        \"᧖\",\n        \"᧗\",\n        \"᧘\",\n        \"᧙\"\n    ],\n    \"tamldec\": [\n        \"௦\",\n        \"௧\",\n        \"௨\",\n        \"௩\",\n        \"௪\",\n        \"௫\",\n        \"௬\",\n        \"௭\",\n        \"௮\",\n        \"௯\"\n    ],\n    \"telu\": [\n        \"౦\",\n        \"౧\",\n        \"౨\",\n        \"౩\",\n        \"౪\",\n        \"౫\",\n        \"౬\",\n        \"౭\",\n        \"౮\",\n        \"౯\"\n    ],\n    \"thai\": [\n        \"๐\",\n        \"๑\",\n        \"๒\",\n        \"๓\",\n        \"๔\",\n        \"๕\",\n        \"๖\",\n        \"๗\",\n        \"๘\",\n        \"๙\"\n    ],\n    \"tibt\": [\n        \"༠\",\n        \"༡\",\n        \"༢\",\n        \"༣\",\n        \"༤\",\n        \"༥\",\n        \"༦\",\n        \"༧\",\n        \"༨\",\n        \"༩\"\n    ],\n    \"tirh\": [\n        \"𑓐\",\n        \"𑓑\",\n        \"𑓒\",\n        \"𑓓\",\n        \"𑓔\",\n        \"𑓕\",\n        \"𑓖\",\n        \"𑓗\",\n        \"𑓘\",\n        \"𑓙\"\n    ],\n    \"vaii\": [\n        \"ᘠ\",\n        \"ᘡ\",\n        \"ᘢ\",\n        \"ᘣ\",\n        \"ᘤ\",\n        \"ᘥ\",\n        \"ᘦ\",\n        \"ᘧ\",\n        \"ᘨ\",\n        \"ᘩ\"\n    ],\n    \"wara\": [\n        \"𑣠\",\n        \"𑣡\",\n        \"𑣢\",\n        \"𑣣\",\n        \"𑣤\",\n        \"𑣥\",\n        \"𑣦\",\n        \"𑣧\",\n        \"𑣨\",\n        \"𑣩\"\n    ],\n    \"wcho\": [\n        \"𞋰\",\n        \"𞋱\",\n        \"𞋲\",\n        \"𞋳\",\n        \"𞋴\",\n        \"𞋵\",\n        \"𞋶\",\n        \"𞋷\",\n        \"𞋸\",\n        \"𞋹\"\n    ]\n};\n", "import { S_UNICODE_REGEX } from '../regex.generated';\nimport { ToRawFixed } from './ToRawFixed';\nimport { digitMapping } from './digit-mapping.generated';\n// This is from: unicode-12.1.0/General_Category/Symbol/regex.js\n// IE11 does not support unicode flag, otherwise this is just /\\p{S}/u.\n// /^\\p{S}/u\nvar CARET_S_UNICODE_REGEX = new RegExp(\"^\".concat(S_UNICODE_REGEX.source));\n// /\\p{S}$/u\nvar S_DOLLAR_UNICODE_REGEX = new RegExp(\"\".concat(S_UNICODE_REGEX.source, \"$\"));\nvar CLDR_NUMBER_PATTERN = /[#0](?:[\\.,][#0]+)*/g;\nexport default function formatToParts(numberResult, data, pl, options) {\n    var sign = numberResult.sign, exponent = numberResult.exponent, magnitude = numberResult.magnitude;\n    var notation = options.notation, style = options.style, numberingSystem = options.numberingSystem;\n    var defaultNumberingSystem = data.numbers.nu[0];\n    // #region Part 1: partition and interpolate the CLDR number pattern.\n    // ----------------------------------------------------------\n    var compactNumberPattern = null;\n    if (notation === 'compact' && magnitude) {\n        compactNumberPattern = getCompactDisplayPattern(numberResult, pl, data, style, options.compactDisplay, options.currencyDisplay, numberingSystem);\n    }\n    // This is used multiple times\n    var nonNameCurrencyPart;\n    if (style === 'currency' && options.currencyDisplay !== 'name') {\n        var byCurrencyDisplay = data.currencies[options.currency];\n        if (byCurrencyDisplay) {\n            switch (options.currencyDisplay) {\n                case 'code':\n                    nonNameCurrencyPart = options.currency;\n                    break;\n                case 'symbol':\n                    nonNameCurrencyPart = byCurrencyDisplay.symbol;\n                    break;\n                default:\n                    nonNameCurrencyPart = byCurrencyDisplay.narrow;\n                    break;\n            }\n        }\n        else {\n            // Fallback for unknown currency\n            nonNameCurrencyPart = options.currency;\n        }\n    }\n    var numberPattern;\n    if (!compactNumberPattern) {\n        // Note: if the style is unit, or is currency and the currency display is name,\n        // its unit parts will be interpolated in part 2. So here we can fallback to decimal.\n        if (style === 'decimal' ||\n            style === 'unit' ||\n            (style === 'currency' && options.currencyDisplay === 'name')) {\n            // Shortcut for decimal\n            var decimalData = data.numbers.decimal[numberingSystem] ||\n                data.numbers.decimal[defaultNumberingSystem];\n            numberPattern = getPatternForSign(decimalData.standard, sign);\n        }\n        else if (style === 'currency') {\n            var currencyData = data.numbers.currency[numberingSystem] ||\n                data.numbers.currency[defaultNumberingSystem];\n            // We replace number pattern part with `0` for easier postprocessing.\n            numberPattern = getPatternForSign(currencyData[options.currencySign], sign);\n        }\n        else {\n            // percent\n            var percentPattern = data.numbers.percent[numberingSystem] ||\n                data.numbers.percent[defaultNumberingSystem];\n            numberPattern = getPatternForSign(percentPattern, sign);\n        }\n    }\n    else {\n        numberPattern = compactNumberPattern;\n    }\n    // Extract the decimal number pattern string. It looks like \"#,##0,00\", which will later be\n    // used to infer decimal group sizes.\n    var decimalNumberPattern = CLDR_NUMBER_PATTERN.exec(numberPattern)[0];\n    // Now we start to substitute patterns\n    // 1. replace strings like `0` and `#,##0.00` with `{0}`\n    // 2. unquote characters (invariant: the quoted characters does not contain the special tokens)\n    numberPattern = numberPattern\n        .replace(CLDR_NUMBER_PATTERN, '{0}')\n        .replace(/'(.)'/g, '$1');\n    // Handle currency spacing (both compact and non-compact).\n    if (style === 'currency' && options.currencyDisplay !== 'name') {\n        var currencyData = data.numbers.currency[numberingSystem] ||\n            data.numbers.currency[defaultNumberingSystem];\n        // See `currencySpacing` substitution rule in TR-35.\n        // Here we always assume the currencyMatch is \"[:^S:]\" and surroundingMatch is \"[:digit:]\".\n        //\n        // Example 1: for pattern \"#,##0.00¤\" with symbol \"US$\", we replace \"¤\" with the symbol,\n        // but insert an extra non-break space before the symbol, because \"[:^S:]\" matches \"U\" in\n        // \"US$\" and \"[:digit:]\" matches the latn numbering system digits.\n        //\n        // Example 2: for pattern \"¤#,##0.00\" with symbol \"US$\", there is no spacing between symbol\n        // and number, because `$` does not match \"[:^S:]\".\n        //\n        // Implementation note: here we do the best effort to infer the insertion.\n        // We also assume that `beforeInsertBetween` and `afterInsertBetween` will never be `;`.\n        var afterCurrency = currencyData.currencySpacing.afterInsertBetween;\n        if (afterCurrency && !S_DOLLAR_UNICODE_REGEX.test(nonNameCurrencyPart)) {\n            numberPattern = numberPattern.replace('¤{0}', \"\\u00A4\".concat(afterCurrency, \"{0}\"));\n        }\n        var beforeCurrency = currencyData.currencySpacing.beforeInsertBetween;\n        if (beforeCurrency && !CARET_S_UNICODE_REGEX.test(nonNameCurrencyPart)) {\n            numberPattern = numberPattern.replace('{0}¤', \"{0}\".concat(beforeCurrency, \"\\u00A4\"));\n        }\n    }\n    // The following tokens are special: `{0}`, `¤`, `%`, `-`, `+`, `{c:...}.\n    var numberPatternParts = numberPattern.split(/({c:[^}]+}|\\{0\\}|[¤%\\-\\+])/g);\n    var numberParts = [];\n    var symbols = data.numbers.symbols[numberingSystem] ||\n        data.numbers.symbols[defaultNumberingSystem];\n    for (var _i = 0, numberPatternParts_1 = numberPatternParts; _i < numberPatternParts_1.length; _i++) {\n        var part = numberPatternParts_1[_i];\n        if (!part) {\n            continue;\n        }\n        switch (part) {\n            case '{0}': {\n                // We only need to handle scientific and engineering notation here.\n                numberParts.push.apply(numberParts, paritionNumberIntoParts(symbols, numberResult, notation, exponent, numberingSystem, \n                // If compact number pattern exists, do not insert group separators.\n                !compactNumberPattern && Boolean(options.useGrouping), decimalNumberPattern, style));\n                break;\n            }\n            case '-':\n                numberParts.push({ type: 'minusSign', value: symbols.minusSign });\n                break;\n            case '+':\n                numberParts.push({ type: 'plusSign', value: symbols.plusSign });\n                break;\n            case '%':\n                numberParts.push({ type: 'percentSign', value: symbols.percentSign });\n                break;\n            case '¤':\n                // Computed above when handling currency spacing.\n                numberParts.push({ type: 'currency', value: nonNameCurrencyPart });\n                break;\n            default:\n                if (/^\\{c:/.test(part)) {\n                    numberParts.push({\n                        type: 'compact',\n                        value: part.substring(3, part.length - 1),\n                    });\n                }\n                else {\n                    // literal\n                    numberParts.push({ type: 'literal', value: part });\n                }\n                break;\n        }\n    }\n    // #endregion\n    // #region Part 2: interpolate unit pattern if necessary.\n    // ----------------------------------------------\n    switch (style) {\n        case 'currency': {\n            // `currencyDisplay: 'name'` has similar pattern handling as units.\n            if (options.currencyDisplay === 'name') {\n                var unitPattern = (data.numbers.currency[numberingSystem] ||\n                    data.numbers.currency[defaultNumberingSystem]).unitPattern;\n                // Select plural\n                var unitName = void 0;\n                var currencyNameData = data.currencies[options.currency];\n                if (currencyNameData) {\n                    unitName = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), currencyNameData.displayName);\n                }\n                else {\n                    // Fallback for unknown currency\n                    unitName = options.currency;\n                }\n                // Do {0} and {1} substitution\n                var unitPatternParts = unitPattern.split(/(\\{[01]\\})/g);\n                var result = [];\n                for (var _a = 0, unitPatternParts_1 = unitPatternParts; _a < unitPatternParts_1.length; _a++) {\n                    var part = unitPatternParts_1[_a];\n                    switch (part) {\n                        case '{0}':\n                            result.push.apply(result, numberParts);\n                            break;\n                        case '{1}':\n                            result.push({ type: 'currency', value: unitName });\n                            break;\n                        default:\n                            if (part) {\n                                result.push({ type: 'literal', value: part });\n                            }\n                            break;\n                    }\n                }\n                return result;\n            }\n            else {\n                return numberParts;\n            }\n        }\n        case 'unit': {\n            var unit = options.unit, unitDisplay = options.unitDisplay;\n            var unitData = data.units.simple[unit];\n            var unitPattern = void 0;\n            if (unitData) {\n                // Simple unit pattern\n                unitPattern = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), data.units.simple[unit][unitDisplay]);\n            }\n            else {\n                // See: http://unicode.org/reports/tr35/tr35-general.html#perUnitPatterns\n                // If cannot find unit in the simple pattern, it must be \"per\" compound pattern.\n                // Implementation note: we are not following TR-35 here because we need to format to parts!\n                var _b = unit.split('-per-'), numeratorUnit = _b[0], denominatorUnit = _b[1];\n                unitData = data.units.simple[numeratorUnit];\n                var numeratorUnitPattern = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), data.units.simple[numeratorUnit][unitDisplay]);\n                var perUnitPattern = data.units.simple[denominatorUnit].perUnit[unitDisplay];\n                if (perUnitPattern) {\n                    // perUnitPattern exists, combine it with numeratorUnitPattern\n                    unitPattern = perUnitPattern.replace('{0}', numeratorUnitPattern);\n                }\n                else {\n                    // get compoundUnit pattern (e.g. \"{0} per {1}\"), repalce {0} with numerator pattern and {1} with\n                    // the denominator pattern in singular form.\n                    var perPattern = data.units.compound.per[unitDisplay];\n                    var denominatorPattern = selectPlural(pl, 1, data.units.simple[denominatorUnit][unitDisplay]);\n                    unitPattern = unitPattern = perPattern\n                        .replace('{0}', numeratorUnitPattern)\n                        .replace('{1}', denominatorPattern.replace('{0}', ''));\n                }\n            }\n            var result = [];\n            // We need spacing around \"{0}\" because they are not treated as \"unit\" parts, but \"literal\".\n            for (var _c = 0, _d = unitPattern.split(/(\\s*\\{0\\}\\s*)/); _c < _d.length; _c++) {\n                var part = _d[_c];\n                var interpolateMatch = /^(\\s*)\\{0\\}(\\s*)$/.exec(part);\n                if (interpolateMatch) {\n                    // Space before \"{0}\"\n                    if (interpolateMatch[1]) {\n                        result.push({ type: 'literal', value: interpolateMatch[1] });\n                    }\n                    // \"{0}\" itself\n                    result.push.apply(result, numberParts);\n                    // Space after \"{0}\"\n                    if (interpolateMatch[2]) {\n                        result.push({ type: 'literal', value: interpolateMatch[2] });\n                    }\n                }\n                else if (part) {\n                    result.push({ type: 'unit', value: part });\n                }\n            }\n            return result;\n        }\n        default:\n            return numberParts;\n    }\n    // #endregion\n}\n// A subset of https://tc39.es/ecma402/#sec-partitionnotationsubpattern\n// Plus the exponent parts handling.\nfunction paritionNumberIntoParts(symbols, numberResult, notation, exponent, numberingSystem, useGrouping, \n/**\n * This is the decimal number pattern without signs or symbols.\n * It is used to infer the group size when `useGrouping` is true.\n *\n * A typical value looks like \"#,##0.00\" (primary group size is 3).\n * Some locales like Hindi has secondary group size of 2 (e.g. \"#,##,##0.00\").\n */\ndecimalNumberPattern, style) {\n    var result = [];\n    // eslint-disable-next-line prefer-const\n    var n = numberResult.formattedString, x = numberResult.roundedNumber;\n    if (isNaN(x)) {\n        return [{ type: 'nan', value: n }];\n    }\n    else if (!isFinite(x)) {\n        return [{ type: 'infinity', value: n }];\n    }\n    var digitReplacementTable = digitMapping[numberingSystem];\n    if (digitReplacementTable) {\n        n = n.replace(/\\d/g, function (digit) { return digitReplacementTable[+digit] || digit; });\n    }\n    // TODO: Else use an implementation dependent algorithm to map n to the appropriate\n    // representation of n in the given numbering system.\n    var decimalSepIndex = n.indexOf('.');\n    var integer;\n    var fraction;\n    if (decimalSepIndex > 0) {\n        integer = n.slice(0, decimalSepIndex);\n        fraction = n.slice(decimalSepIndex + 1);\n    }\n    else {\n        integer = n;\n    }\n    // #region Grouping integer digits\n    // The weird compact and x >= 10000 check is to ensure consistency with Node.js and Chrome.\n    // Note that `de` does not have compact form for thousands, but Node.js does not insert grouping separator\n    // unless the rounded number is greater than 10000:\n    //   NumberFormat('de', {notation: 'compact', compactDisplay: 'short'}).format(1234) //=> \"1234\"\n    //   NumberFormat('de').format(1234) //=> \"1.234\"\n    if (useGrouping && (notation !== 'compact' || x >= 10000)) {\n        // a. Let groupSepSymbol be the implementation-, locale-, and numbering system-dependent (ILND) String representing the grouping separator.\n        // For currency we should use `currencyGroup` instead of generic `group`\n        var groupSepSymbol = style === 'currency' && symbols.currencyGroup != null\n            ? symbols.currencyGroup\n            : symbols.group;\n        var groups = [];\n        // > There may be two different grouping sizes: The primary grouping size used for the least\n        // > significant integer group, and the secondary grouping size used for more significant groups.\n        // > If a pattern contains multiple grouping separators, the interval between the last one and the\n        // > end of the integer defines the primary grouping size, and the interval between the last two\n        // > defines the secondary grouping size. All others are ignored.\n        var integerNumberPattern = decimalNumberPattern.split('.')[0];\n        var patternGroups = integerNumberPattern.split(',');\n        var primaryGroupingSize = 3;\n        var secondaryGroupingSize = 3;\n        if (patternGroups.length > 1) {\n            primaryGroupingSize = patternGroups[patternGroups.length - 1].length;\n        }\n        if (patternGroups.length > 2) {\n            secondaryGroupingSize = patternGroups[patternGroups.length - 2].length;\n        }\n        var i = integer.length - primaryGroupingSize;\n        if (i > 0) {\n            // Slice the least significant integer group\n            groups.push(integer.slice(i, i + primaryGroupingSize));\n            // Then iteratively push the more signicant groups\n            // TODO: handle surrogate pairs in some numbering system digits\n            for (i -= secondaryGroupingSize; i > 0; i -= secondaryGroupingSize) {\n                groups.push(integer.slice(i, i + secondaryGroupingSize));\n            }\n            groups.push(integer.slice(0, i + secondaryGroupingSize));\n        }\n        else {\n            groups.push(integer);\n        }\n        while (groups.length > 0) {\n            var integerGroup = groups.pop();\n            result.push({ type: 'integer', value: integerGroup });\n            if (groups.length > 0) {\n                result.push({ type: 'group', value: groupSepSymbol });\n            }\n        }\n    }\n    else {\n        result.push({ type: 'integer', value: integer });\n    }\n    // #endregion\n    if (fraction !== undefined) {\n        var decimalSepSymbol = style === 'currency' && symbols.currencyDecimal != null\n            ? symbols.currencyDecimal\n            : symbols.decimal;\n        result.push({ type: 'decimal', value: decimalSepSymbol }, { type: 'fraction', value: fraction });\n    }\n    if ((notation === 'scientific' || notation === 'engineering') &&\n        isFinite(x)) {\n        result.push({ type: 'exponentSeparator', value: symbols.exponential });\n        if (exponent < 0) {\n            result.push({ type: 'exponentMinusSign', value: symbols.minusSign });\n            exponent = -exponent;\n        }\n        var exponentResult = ToRawFixed(exponent, 0, 0);\n        result.push({\n            type: 'exponentInteger',\n            value: exponentResult.formattedString,\n        });\n    }\n    return result;\n}\nfunction getPatternForSign(pattern, sign) {\n    if (pattern.indexOf(';') < 0) {\n        pattern = \"\".concat(pattern, \";-\").concat(pattern);\n    }\n    var _a = pattern.split(';'), zeroPattern = _a[0], negativePattern = _a[1];\n    switch (sign) {\n        case 0:\n            return zeroPattern;\n        case -1:\n            return negativePattern;\n        default:\n            return negativePattern.indexOf('-') >= 0\n                ? negativePattern.replace(/-/g, '+')\n                : \"+\".concat(zeroPattern);\n    }\n}\n// Find the CLDR pattern for compact notation based on the magnitude of data and style.\n//\n// Example return value: \"¤ {c:laki}000;¤{c:laki} -0\" (`sw` locale):\n// - Notice the `{c:...}` token that wraps the compact literal.\n// - The consecutive zeros are normalized to single zero to match CLDR_NUMBER_PATTERN.\n//\n// Returning null means the compact display pattern cannot be found.\nfunction getCompactDisplayPattern(numberResult, pl, data, style, compactDisplay, currencyDisplay, numberingSystem) {\n    var _a;\n    var roundedNumber = numberResult.roundedNumber, sign = numberResult.sign, magnitude = numberResult.magnitude;\n    var magnitudeKey = String(Math.pow(10, magnitude));\n    var defaultNumberingSystem = data.numbers.nu[0];\n    var pattern;\n    if (style === 'currency' && currencyDisplay !== 'name') {\n        var byNumberingSystem = data.numbers.currency;\n        var currencyData = byNumberingSystem[numberingSystem] ||\n            byNumberingSystem[defaultNumberingSystem];\n        // NOTE: compact notation ignores currencySign!\n        var compactPluralRules = (_a = currencyData.short) === null || _a === void 0 ? void 0 : _a[magnitudeKey];\n        if (!compactPluralRules) {\n            return null;\n        }\n        pattern = selectPlural(pl, roundedNumber, compactPluralRules);\n    }\n    else {\n        var byNumberingSystem = data.numbers.decimal;\n        var byCompactDisplay = byNumberingSystem[numberingSystem] ||\n            byNumberingSystem[defaultNumberingSystem];\n        var compactPlaralRule = byCompactDisplay[compactDisplay][magnitudeKey];\n        if (!compactPlaralRule) {\n            return null;\n        }\n        pattern = selectPlural(pl, roundedNumber, compactPlaralRule);\n    }\n    // See https://unicode.org/reports/tr35/tr35-numbers.html#Compact_Number_Formats\n    // > If the value is precisely “0”, either explicit or defaulted, then the normal number format\n    // > pattern for that sort of object is supplied.\n    if (pattern === '0') {\n        return null;\n    }\n    pattern = getPatternForSign(pattern, sign)\n        // Extract compact literal from the pattern\n        .replace(/([^\\s;\\-\\+\\d¤]+)/g, '{c:$1}')\n        // We replace one or more zeros with a single zero so it matches `CLDR_NUMBER_PATTERN`.\n        .replace(/0+/, '0');\n    return pattern;\n}\nfunction selectPlural(pl, x, rules) {\n    return rules[pl.select(x)] || rules.other;\n}\n", "import { FormatNumericToString } from './FormatNumericToString';\nimport { SameValue } from '../262';\nimport { ComputeExponent } from './ComputeExponent';\nimport formatToParts from './format_to_parts';\n/**\n * https://tc39.es/ecma402/#sec-formatnumberstring\n */\nexport function PartitionNumberPattern(numberFormat, x, _a) {\n    var _b;\n    var getInternalSlots = _a.getInternalSlots;\n    var internalSlots = getInternalSlots(numberFormat);\n    var pl = internalSlots.pl, dataLocaleData = internalSlots.dataLocaleData, numberingSystem = internalSlots.numberingSystem;\n    var symbols = dataLocaleData.numbers.symbols[numberingSystem] ||\n        dataLocaleData.numbers.symbols[dataLocaleData.numbers.nu[0]];\n    var magnitude = 0;\n    var exponent = 0;\n    var n;\n    if (isNaN(x)) {\n        n = symbols.nan;\n    }\n    else if (x == Number.POSITIVE_INFINITY || x == Number.NEGATIVE_INFINITY) {\n        n = symbols.infinity;\n    }\n    else {\n        if (!SameValue(x, -0)) {\n            if (!isFinite(x)) {\n                throw new Error('Input must be a mathematical value');\n            }\n            if (internalSlots.style == 'percent') {\n                x *= 100;\n            }\n            ;\n            _b = ComputeExponent(numberFormat, x, {\n                getInternalSlots: getInternalSlots,\n            }), exponent = _b[0], magnitude = _b[1];\n            // Preserve more precision by doing multiplication when exponent is negative.\n            x = exponent < 0 ? x * Math.pow(10, -exponent) : x / Math.pow(10, exponent);\n        }\n        var formatNumberResult = FormatNumericToString(internalSlots, x);\n        n = formatNumberResult.formattedString;\n        x = formatNumberResult.roundedNumber;\n    }\n    // Based on https://tc39.es/ecma402/#sec-getnumberformatpattern\n    // We need to do this before `x` is rounded.\n    var sign;\n    var signDisplay = internalSlots.signDisplay;\n    switch (signDisplay) {\n        case 'never':\n            sign = 0;\n            break;\n        case 'auto':\n            if (SameValue(x, 0) || x > 0 || isNaN(x)) {\n                sign = 0;\n            }\n            else {\n                sign = -1;\n            }\n            break;\n        case 'always':\n            if (SameValue(x, 0) || x > 0 || isNaN(x)) {\n                sign = 1;\n            }\n            else {\n                sign = -1;\n            }\n            break;\n        default:\n            // x === 0 -> x is 0 or x is -0\n            if (x === 0 || isNaN(x)) {\n                sign = 0;\n            }\n            else if (x > 0) {\n                sign = 1;\n            }\n            else {\n                sign = -1;\n            }\n    }\n    return formatToParts({ roundedNumber: x, formattedString: n, exponent: exponent, magnitude: magnitude, sign: sign }, internalSlots.dataLocaleData, pl, internalSlots);\n}\n", "import { PartitionNumberPattern } from './PartitionNumberPattern';\nimport { CollapseNumberRange } from './CollapseNumberRange';\nimport { FormatApproximately } from './FormatApproximately';\n/**\n * https://tc39.es/ecma402/#sec-partitionnumberrangepattern\n */\nexport function PartitionNumberRangePattern(numberFormat, x, y, _a) {\n    var getInternalSlots = _a.getInternalSlots;\n    if (isNaN(x) || isNaN(y)) {\n        throw new RangeError('Input must be a number');\n    }\n    var result = [];\n    var xResult = PartitionNumberPattern(numberFormat, x, { getInternalSlots: getInternalSlots });\n    var yResult = PartitionNumberPattern(numberFormat, y, { getInternalSlots: getInternalSlots });\n    if (xResult === yResult) {\n        return FormatApproximately(numberFormat, xResult, { getInternalSlots: getInternalSlots });\n    }\n    for (var _i = 0, xResult_1 = xResult; _i < xResult_1.length; _i++) {\n        var r = xResult_1[_i];\n        r.source = 'startRange';\n    }\n    result = result.concat(xResult);\n    var internalSlots = getInternalSlots(numberFormat);\n    var symbols = internalSlots.dataLocaleData.numbers.symbols[internalSlots.numberingSystem];\n    result.push({ type: 'literal', value: symbols.rangeSign, source: 'shared' });\n    for (var _b = 0, yResult_1 = yResult; _b < yResult_1.length; _b++) {\n        var r = yResult_1[_b];\n        r.source = 'endRange';\n    }\n    result = result.concat(yResult);\n    return CollapseNumberRange(result);\n}\n", "import { PartitionNumberRangePattern } from './PartitionNumberRangePattern';\n/**\n * https://tc39.es/ecma402/#sec-formatnumericrange\n */\nexport function FormatNumericRange(numberFormat, x, y, _a) {\n    var getInternalSlots = _a.getInternalSlots;\n    var parts = PartitionNumberRangePattern(numberFormat, x, y, {\n        getInternalSlots: getInternalSlots,\n    });\n    return parts.map(function (part) { return part.value; }).join('');\n}\n", "import { PartitionNumberRangePattern } from './PartitionNumberRangePattern';\n/**\n * https://tc39.es/ecma402/#sec-formatnumericrangetoparts\n */\nexport function FormatNumericRangeToParts(numberFormat, x, y, _a) {\n    var getInternalSlots = _a.getInternalSlots;\n    var parts = PartitionNumberRangePattern(numberFormat, x, y, {\n        getInternalSlots: getInternalSlots,\n    });\n    return parts.map(function (part, index) { return ({\n        type: part.type,\n        value: part.value,\n        source: part.source,\n        result: index.toString(),\n    }); });\n}\n", "import { PartitionNumberPattern } from './PartitionNumberPattern';\nimport { ArrayCreate } from '../262';\nexport function FormatNumericToParts(nf, x, implDetails) {\n    var parts = PartitionNumberPattern(nf, x, implDetails);\n    var result = ArrayCreate(0);\n    for (var _i = 0, parts_1 = parts; _i < parts_1.length; _i++) {\n        var part = parts_1[_i];\n        result.push({\n            type: part.type,\n            value: part.value,\n        });\n    }\n    return result;\n}\n", "var negativeMapping = {\n    ceil: 'zero',\n    floor: 'infinity',\n    expand: 'infinity',\n    trunc: 'zero',\n    halfCeil: 'half-zero',\n    halfFloor: 'half-infinity',\n    halfExpand: 'half-infinity',\n    halfTrunc: 'half-zero',\n    halfEven: 'half-even',\n};\nvar positiveMapping = {\n    ceil: 'infinity',\n    floor: 'zero',\n    expand: 'infinity',\n    trunc: 'zero',\n    halfCeil: 'half-infinity',\n    halfFloor: 'half-zero',\n    halfExpand: 'half-infinity',\n    halfTrunc: 'half-zero',\n    halfEven: 'half-even',\n};\nexport function GetUnsignedRoundingMode(roundingMode, isNegative) {\n    if (isNegative) {\n        return negativeMapping[roundingMode];\n    }\n    return positiveMapping[roundingMode];\n}\n", "import { DefaultNumberOption } from '../DefaultNumberOption';\nimport { GetNumberOption } from '../GetNumberOption';\nimport { GetOption } from '../GetOption';\n/**\n * https://tc39.es/ecma402/#sec-setnfdigitoptions\n */\nexport function SetNumberFormatDigitOptions(internalSlots, opts, mnfdDefault, mxfdDefault, notation) {\n    var mnid = GetNumberOption(opts, 'minimumIntegerDigits', 1, 21, 1);\n    var mnfd = opts.minimumFractionDigits;\n    var mxfd = opts.maximumFractionDigits;\n    var mnsd = opts.minimumSignificantDigits;\n    var mxsd = opts.maximumSignificantDigits;\n    internalSlots.minimumIntegerDigits = mnid;\n    var roundingPriority = GetOption(opts, 'roundingPriority', 'string', ['auto', 'morePrecision', 'lessPrecision'], 'auto');\n    var hasSd = mnsd !== undefined || mxsd !== undefined;\n    var hasFd = mnfd !== undefined || mxfd !== undefined;\n    var needSd = true;\n    var needFd = true;\n    if (roundingPriority === 'auto') {\n        needSd = hasSd;\n        if (hasSd || (!hasFd && notation === 'compact')) {\n            needFd = false;\n        }\n    }\n    if (needSd) {\n        if (hasSd) {\n            mnsd = DefaultNumberOption(mnsd, 1, 21, 1);\n            mxsd = DefaultNumberOption(mxsd, mnsd, 21, 21);\n            internalSlots.minimumSignificantDigits = mnsd;\n            internalSlots.maximumSignificantDigits = mxsd;\n        }\n        else {\n            internalSlots.minimumSignificantDigits = 1;\n            internalSlots.maximumSignificantDigits = 21;\n        }\n    }\n    if (needFd) {\n        if (hasFd) {\n            mnfd = DefaultNumberOption(mnfd, 0, 20, undefined);\n            mxfd = DefaultNumberOption(mxfd, 0, 20, undefined);\n            if (mnfd === undefined) {\n                // @ts-expect-error\n                mnfd = Math.min(mnfdDefault, mxfd);\n            }\n            else if (mxfd === undefined) {\n                mxfd = Math.max(mxfdDefault, mnfd);\n            }\n            else if (mnfd > mxfd) {\n                throw new RangeError(\"Invalid range, \".concat(mnfd, \" > \").concat(mxfd));\n            }\n            internalSlots.minimumFractionDigits = mnfd;\n            internalSlots.maximumFractionDigits = mxfd;\n        }\n        else {\n            internalSlots.minimumFractionDigits = mnfdDefault;\n            internalSlots.maximumFractionDigits = mxfdDefault;\n        }\n    }\n    if (needSd || needFd) {\n        if (roundingPriority === 'morePrecision') {\n            internalSlots.roundingType = 'morePrecision';\n        }\n        else if (roundingPriority === 'lessPrecision') {\n            internalSlots.roundingType = 'lessPrecision';\n        }\n        else if (hasSd) {\n            internalSlots.roundingType = 'significantDigits';\n        }\n        else {\n            internalSlots.roundingType = 'fractionDigits';\n        }\n    }\n    else {\n        internalSlots.roundingType = 'morePrecision';\n        internalSlots.minimumFractionDigits = 0;\n        internalSlots.maximumFractionDigits = 0;\n        internalSlots.minimumSignificantDigits = 1;\n        internalSlots.maximumSignificantDigits = 2;\n    }\n}\n", "import { GetOption } from '../GetOption';\nimport { IsWellFormedCurrencyCode } from '../IsWellFormedCurrencyCode';\nimport { IsWellFormedUnitIdentifier } from '../IsWellFormedUnitIdentifier';\n/**\n * https://tc39.es/ecma402/#sec-setnumberformatunitoptions\n */\nexport function SetNumberFormatUnitOptions(nf, options, _a) {\n    if (options === void 0) { options = Object.create(null); }\n    var getInternalSlots = _a.getInternalSlots;\n    var internalSlots = getInternalSlots(nf);\n    var style = GetOption(options, 'style', 'string', ['decimal', 'percent', 'currency', 'unit'], 'decimal');\n    internalSlots.style = style;\n    var currency = GetOption(options, 'currency', 'string', undefined, undefined);\n    if (currency !== undefined && !IsWellFormedCurrencyCode(currency)) {\n        throw RangeError('Malformed currency code');\n    }\n    if (style === 'currency' && currency === undefined) {\n        throw TypeError('currency cannot be undefined');\n    }\n    var currencyDisplay = GetOption(options, 'currencyDisplay', 'string', ['code', 'symbol', 'narrowSymbol', 'name'], 'symbol');\n    var currencySign = GetOption(options, 'currencySign', 'string', ['standard', 'accounting'], 'standard');\n    var unit = GetOption(options, 'unit', 'string', undefined, undefined);\n    if (unit !== undefined && !IsWellFormedUnitIdentifier(unit)) {\n        throw RangeError('Invalid unit argument for Intl.NumberFormat()');\n    }\n    if (style === 'unit' && unit === undefined) {\n        throw TypeError('unit cannot be undefined');\n    }\n    var unitDisplay = GetOption(options, 'unitDisplay', 'string', ['short', 'narrow', 'long'], 'short');\n    if (style === 'currency') {\n        internalSlots.currency = currency.toUpperCase();\n        internalSlots.currencyDisplay = currencyDisplay;\n        internalSlots.currencySign = currencySign;\n    }\n    if (style === 'unit') {\n        internalSlots.unit = unit;\n        internalSlots.unitDisplay = unitDisplay;\n    }\n}\n", "import { ResolveLocale } from '@formatjs/intl-localematcher';\nimport { CanonicalizeLocaleList } from '../CanonicalizeLocaleList';\nimport { CoerceOptionsToObject } from '../CoerceOptionsToObject';\nimport { GetNumberOption } from '../GetNumberOption';\nimport { GetOption } from '../GetOption';\nimport { GetStringOrBooleanOption } from '../GetStringOrBooleanOption';\nimport { invariant } from '../utils';\nimport { CurrencyDigits } from './CurrencyDigits';\nimport { SetNumberFormatDigitOptions } from './SetNumberFormatDigitOptions';\nimport { SetNumberFormatUnitOptions } from './SetNumberFormatUnitOptions';\nvar VALID_ROUND_INCREMENT_VALUES = [\n    1, 2, 5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000,\n];\n/**\n * https://tc39.es/ecma402/#sec-initializenumberformat\n */\nexport function InitializeNumberFormat(nf, locales, opts, _a) {\n    var getInternalSlots = _a.getInternalSlots, localeData = _a.localeData, availableLocales = _a.availableLocales, numberingSystemNames = _a.numberingSystemNames, getDefaultLocale = _a.getDefaultLocale, currencyDigitsData = _a.currencyDigitsData;\n    // @ts-ignore\n    var requestedLocales = CanonicalizeLocaleList(locales);\n    var options = CoerceOptionsToObject(opts);\n    var opt = Object.create(null);\n    var matcher = GetOption(options, 'localeMatcher', 'string', ['lookup', 'best fit'], 'best fit');\n    opt.localeMatcher = matcher;\n    var numberingSystem = GetOption(options, 'numberingSystem', 'string', undefined, undefined);\n    if (numberingSystem !== undefined &&\n        numberingSystemNames.indexOf(numberingSystem) < 0) {\n        // 8.a. If numberingSystem does not match the Unicode Locale Identifier type nonterminal,\n        // throw a RangeError exception.\n        throw RangeError(\"Invalid numberingSystems: \".concat(numberingSystem));\n    }\n    opt.nu = numberingSystem;\n    var r = ResolveLocale(Array.from(availableLocales), requestedLocales, opt, \n    // [[RelevantExtensionKeys]] slot, which is a constant\n    ['nu'], localeData, getDefaultLocale);\n    var dataLocaleData = localeData[r.dataLocale];\n    invariant(!!dataLocaleData, \"Missing locale data for \".concat(r.dataLocale));\n    var internalSlots = getInternalSlots(nf);\n    internalSlots.locale = r.locale;\n    internalSlots.dataLocale = r.dataLocale;\n    internalSlots.numberingSystem = r.nu;\n    internalSlots.dataLocaleData = dataLocaleData;\n    SetNumberFormatUnitOptions(nf, options, { getInternalSlots: getInternalSlots });\n    var style = internalSlots.style;\n    var mnfdDefault;\n    var mxfdDefault;\n    if (style === 'currency') {\n        var currency = internalSlots.currency;\n        var cDigits = CurrencyDigits(currency, { currencyDigitsData: currencyDigitsData });\n        mnfdDefault = cDigits;\n        mxfdDefault = cDigits;\n    }\n    else {\n        mnfdDefault = 0;\n        mxfdDefault = style === 'percent' ? 0 : 3;\n    }\n    var notation = GetOption(options, 'notation', 'string', ['standard', 'scientific', 'engineering', 'compact'], 'standard');\n    internalSlots.notation = notation;\n    SetNumberFormatDigitOptions(internalSlots, options, mnfdDefault, mxfdDefault, notation);\n    var roundingIncrement = GetNumberOption(options, 'roundingIncrement', 1, 5000, 1);\n    if (VALID_ROUND_INCREMENT_VALUES.indexOf(roundingIncrement) === -1) {\n        throw new RangeError(\"Invalid rounding increment value: \".concat(roundingIncrement, \".\\nValid values are \").concat(VALID_ROUND_INCREMENT_VALUES, \".\"));\n    }\n    if (roundingIncrement !== 1 &&\n        internalSlots.roundingType !== 'fractionDigits') {\n        throw new TypeError(\"For roundingIncrement > 1 only fractionDigits is a valid roundingType\");\n    }\n    if (roundingIncrement !== 1 &&\n        internalSlots.maximumFractionDigits !== internalSlots.minimumFractionDigits) {\n        throw new RangeError('With roundingIncrement > 1, maximumFractionDigits and minimumFractionDigits must be equal.');\n    }\n    internalSlots.roundingIncrement = roundingIncrement;\n    var trailingZeroDisplay = GetOption(options, 'trailingZeroDisplay', 'string', ['auto', 'stripIfInteger'], 'auto');\n    internalSlots.trailingZeroDisplay = trailingZeroDisplay;\n    var compactDisplay = GetOption(options, 'compactDisplay', 'string', ['short', 'long'], 'short');\n    var defaultUseGrouping = 'auto';\n    if (notation === 'compact') {\n        internalSlots.compactDisplay = compactDisplay;\n        defaultUseGrouping = 'min2';\n    }\n    internalSlots.useGrouping = GetStringOrBooleanOption(options, 'useGrouping', ['min2', 'auto', 'always'], 'always', false, defaultUseGrouping);\n    internalSlots.signDisplay = GetOption(options, 'signDisplay', 'string', ['auto', 'never', 'always', 'exceptZero', 'negative'], 'auto');\n    internalSlots.roundingMode = GetOption(options, 'roundingMode', 'string', [\n        'ceil',\n        'floor',\n        'expand',\n        'trunc',\n        'halfCeil',\n        'halfFloor',\n        'halfExpand',\n        'halfTrunc',\n        'halfEven',\n    ], 'halfExpand');\n    return nf;\n}\n", "import { invariant } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-partitionpattern\n * @param pattern\n */\nexport function PartitionPattern(pattern) {\n    var result = [];\n    var beginIndex = pattern.indexOf('{');\n    var endIndex = 0;\n    var nextIndex = 0;\n    var length = pattern.length;\n    while (beginIndex < pattern.length && beginIndex > -1) {\n        endIndex = pattern.indexOf('}', beginIndex);\n        invariant(endIndex > beginIndex, \"Invalid pattern \".concat(pattern));\n        if (beginIndex > nextIndex) {\n            result.push({\n                type: 'literal',\n                value: pattern.substring(nextIndex, beginIndex),\n            });\n        }\n        result.push({\n            type: pattern.substring(beginIndex + 1, endIndex),\n            value: undefined,\n        });\n        nextIndex = endIndex + 1;\n        beginIndex = pattern.indexOf('{', nextIndex);\n    }\n    if (nextIndex < length) {\n        result.push({\n            type: 'literal',\n            value: pattern.substring(nextIndex, length),\n        });\n    }\n    return result;\n}\n", "import { LookupSupportedLocales } from '@formatjs/intl-localematcher';\nimport { ToObject } from './262';\nimport { GetOption } from './GetOption';\n/**\n * https://tc39.es/ecma402/#sec-supportedlocales\n * @param availableLocales\n * @param requestedLocales\n * @param options\n */\nexport function SupportedLocales(availableLocales, requestedLocales, options) {\n    var matcher = 'best fit';\n    if (options !== undefined) {\n        options = ToObject(options);\n        matcher = GetOption(options, 'localeMatcher', 'string', ['lookup', 'best fit'], 'best fit');\n    }\n    if (matcher === 'best fit') {\n        return LookupSupportedLocales(Array.from(availableLocales), requestedLocales);\n    }\n    return LookupSupportedLocales(Array.from(availableLocales), requestedLocales);\n}\n", "import { __extends } from \"tslib\";\nvar MissingLocaleDataError = /** @class */ (function (_super) {\n    __extends(MissingLocaleDataError, _super);\n    function MissingLocaleDataError() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = 'MISSING_LOCALE_DATA';\n        return _this;\n    }\n    return MissingLocaleDataError;\n}(Error));\nexport function isMissingLocaleDataError(e) {\n    return e.type === 'MISSING_LOCALE_DATA';\n}\n", "export var RangePatternType;\n(function (RangePatternType) {\n    RangePatternType[\"startRange\"] = \"startRange\";\n    RangePatternType[\"shared\"] = \"shared\";\n    RangePatternType[\"endRange\"] = \"endRange\";\n})(RangePatternType || (RangePatternType = {}));\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export * from './CanonicalizeLocaleList';\nexport * from './CanonicalizeTimeZoneName';\nexport * from './CoerceOptionsToObject';\nexport * from './GetNumberOption';\nexport * from './GetOption';\nexport * from './GetOptionsObject';\nexport * from './GetStringOrBooleanOption';\nexport * from './IsSanctionedSimpleUnitIdentifier';\nexport * from './IsValidTimeZoneName';\nexport * from './IsWellFormedCurrencyCode';\nexport * from './IsWellFormedUnitIdentifier';\nexport * from './NumberFormat/ApplyUnsignedRoundingMode';\nexport * from './NumberFormat/CollapseNumberRange';\nexport * from './NumberFormat/ComputeExponent';\nexport * from './NumberFormat/ComputeExponentForMagnitude';\nexport * from './NumberFormat/CurrencyDigits';\nexport * from './NumberFormat/FormatApproximately';\nexport * from './NumberFormat/FormatNumericRange';\nexport * from './NumberFormat/FormatNumericRangeToParts';\nexport * from './NumberFormat/FormatNumericToParts';\nexport * from './NumberFormat/FormatNumericToString';\nexport * from './NumberFormat/GetUnsignedRoundingMode';\nexport * from './NumberFormat/InitializeNumberFormat';\nexport * from './NumberFormat/PartitionNumberPattern';\nexport * from './NumberFormat/PartitionNumberRangePattern';\nexport * from './NumberFormat/SetNumberFormatDigitOptions';\nexport * from './NumberFormat/SetNumberFormatUnitOptions';\nexport * from './NumberFormat/ToRawFixed';\nexport * from './NumberFormat/ToRawPrecision';\nexport { default as _formatToParts } from './NumberFormat/format_to_parts';\nexport * from './PartitionPattern';\nexport * from './SupportedLocales';\nexport { createDataProperty, defineProperty, getInternalSlot, getMagnitude, getMultiInternalSlots, isLiteralPart, setInternalSlot, setMultiInternalSlots, } from './utils';\nexport * from './262';\nexport { isMissingLocaleDataError } from './data';\nexport * from './types/date-time';\nexport * from './types/displaynames';\nexport * from './types/list';\nexport * from './types/number';\nexport * from './types/plural-rules';\nexport * from './types/relative-time';\nexport { invariant } from './utils';\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.InitializePluralRules = void 0;\nvar ecma402_abstract_1 = require(\"@formatjs/ecma402-abstract\");\nvar intl_localematcher_1 = require(\"@formatjs/intl-localematcher\");\nfunction InitializePluralRules(pl, locales, options, _a) {\n    var availableLocales = _a.availableLocales, relevantExtensionKeys = _a.relevantExtensionKeys, localeData = _a.localeData, getDefaultLocale = _a.getDefaultLocale, getInternalSlots = _a.getInternalSlots;\n    var requestedLocales = (0, ecma402_abstract_1.CanonicalizeLocaleList)(locales);\n    var opt = Object.create(null);\n    var opts = (0, ecma402_abstract_1.CoerceOptionsToObject)(options);\n    var internalSlots = getInternalSlots(pl);\n    internalSlots.initializedPluralRules = true;\n    var matcher = (0, ecma402_abstract_1.GetOption)(opts, 'localeMatcher', 'string', ['best fit', 'lookup'], 'best fit');\n    opt.localeMatcher = matcher;\n    internalSlots.type = (0, ecma402_abstract_1.GetOption)(opts, 'type', 'string', ['cardinal', 'ordinal'], 'cardinal');\n    (0, ecma402_abstract_1.SetNumberFormatDigitOptions)(internalSlots, opts, 0, 3, 'standard');\n    var r = (0, intl_localematcher_1.ResolveLocale)(availableLocales, requestedLocales, opt, relevantExtensionKeys, localeData, getDefaultLocale);\n    internalSlots.locale = r.locale;\n    return pl;\n}\nexports.InitializePluralRules = InitializePluralRules;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.GetOperands = void 0;\nvar ecma402_abstract_1 = require(\"@formatjs/ecma402-abstract\");\n/**\n * http://ecma-international.org/ecma-402/7.0/index.html#sec-getoperands\n * @param s\n */\nfunction GetOperands(s) {\n    (0, ecma402_abstract_1.invariant)(typeof s === 'string', \"GetOperands should have been called with a string\");\n    var n = (0, ecma402_abstract_1.ToNumber)(s);\n    (0, ecma402_abstract_1.invariant)(isFinite(n), 'n should be finite');\n    var dp = s.indexOf('.');\n    var iv;\n    var f;\n    var v;\n    var fv = '';\n    if (dp === -1) {\n        iv = n;\n        f = 0;\n        v = 0;\n    }\n    else {\n        iv = s.slice(0, dp);\n        fv = s.slice(dp, s.length);\n        f = (0, ecma402_abstract_1.ToNumber)(fv);\n        v = fv.length;\n    }\n    var i = Math.abs((0, ecma402_abstract_1.ToNumber)(iv));\n    var w;\n    var t;\n    if (f !== 0) {\n        var ft = fv.replace(/0+$/, '');\n        w = ft.length;\n        t = (0, ecma402_abstract_1.ToNumber)(ft);\n    }\n    else {\n        w = 0;\n        t = 0;\n    }\n    return {\n        Number: n,\n        IntegerDigits: i,\n        NumberOfFractionDigits: v,\n        NumberOfFractionDigitsWithoutTrailing: w,\n        FractionDigits: f,\n        FractionDigitsWithoutTrailing: t,\n    };\n}\nexports.GetOperands = GetOperands;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ResolvePlural = void 0;\nvar ecma402_abstract_1 = require(\"@formatjs/ecma402-abstract\");\nvar GetOperands_1 = require(\"./GetOperands\");\n/**\n * http://ecma-international.org/ecma-402/7.0/index.html#sec-resolveplural\n * @param pl\n * @param n\n * @param PluralRuleSelect Has to pass in bc it's implementation-specific\n */\nfunction ResolvePlural(pl, n, _a) {\n    var getInternalSlots = _a.getInternalSlots, PluralRuleSelect = _a.PluralRuleSelect;\n    var internalSlots = getInternalSlots(pl);\n    (0, ecma402_abstract_1.invariant)((0, ecma402_abstract_1.Type)(internalSlots) === 'Object', 'pl has to be an object');\n    (0, ecma402_abstract_1.invariant)('initializedPluralRules' in internalSlots, 'pluralrules must be initialized');\n    (0, ecma402_abstract_1.invariant)((0, ecma402_abstract_1.Type)(n) === 'Number', 'n must be a number');\n    if (!isFinite(n)) {\n        return 'other';\n    }\n    var locale = internalSlots.locale, type = internalSlots.type;\n    var res = (0, ecma402_abstract_1.FormatNumericToString)(internalSlots, n);\n    var s = res.formattedString;\n    var operands = (0, GetOperands_1.GetOperands)(s);\n    return PluralRuleSelect(locale, type, n, operands);\n}\nexports.ResolvePlural = ResolvePlural;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar internalSlotMap = new WeakMap();\nfunction getInternalSlots(x) {\n    var internalSlots = internalSlotMap.get(x);\n    if (!internalSlots) {\n        internalSlots = Object.create(null);\n        internalSlotMap.set(x, internalSlots);\n    }\n    return internalSlots;\n}\nexports.default = getInternalSlots;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PluralRules = void 0;\nvar tslib_1 = require(\"tslib\");\nvar ecma402_abstract_1 = require(\"@formatjs/ecma402-abstract\");\nvar InitializePluralRules_1 = require(\"./abstract/InitializePluralRules\");\nvar ResolvePlural_1 = require(\"./abstract/ResolvePlural\");\nvar get_internal_slots_1 = tslib_1.__importDefault(require(\"./get_internal_slots\"));\nfunction validateInstance(instance, method) {\n    if (!(instance instanceof PluralRules)) {\n        throw new TypeError(\"Method Intl.PluralRules.prototype.\".concat(method, \" called on incompatible receiver \").concat(String(instance)));\n    }\n}\n/**\n * http://ecma-international.org/ecma-402/7.0/index.html#sec-pluralruleselect\n * @param locale\n * @param type\n * @param _n\n * @param param3\n */\nfunction PluralRuleSelect(locale, type, _n, _a) {\n    var IntegerDigits = _a.IntegerDigits, NumberOfFractionDigits = _a.NumberOfFractionDigits, FractionDigits = _a.FractionDigits;\n    return PluralRules.localeData[locale].fn(NumberOfFractionDigits\n        ? \"\".concat(IntegerDigits, \".\").concat(FractionDigits)\n        : IntegerDigits, type === 'ordinal');\n}\nvar PluralRules = /** @class */ (function () {\n    function PluralRules(locales, options) {\n        // test262/test/intl402/RelativeTimeFormat/constructor/constructor/newtarget-undefined.js\n        // Cannot use `new.target` bc of IE11 & TS transpiles it to something else\n        var newTarget = this && this instanceof PluralRules ? this.constructor : void 0;\n        if (!newTarget) {\n            throw new TypeError(\"Intl.PluralRules must be called with 'new'\");\n        }\n        return (0, InitializePluralRules_1.InitializePluralRules)(this, locales, options, {\n            availableLocales: PluralRules.availableLocales,\n            relevantExtensionKeys: PluralRules.relevantExtensionKeys,\n            localeData: PluralRules.localeData,\n            getDefaultLocale: PluralRules.getDefaultLocale,\n            getInternalSlots: get_internal_slots_1.default,\n        });\n    }\n    PluralRules.prototype.resolvedOptions = function () {\n        validateInstance(this, 'resolvedOptions');\n        var opts = Object.create(null);\n        var internalSlots = (0, get_internal_slots_1.default)(this);\n        opts.locale = internalSlots.locale;\n        opts.type = internalSlots.type;\n        [\n            'minimumIntegerDigits',\n            'minimumFractionDigits',\n            'maximumFractionDigits',\n            'minimumSignificantDigits',\n            'maximumSignificantDigits',\n        ].forEach(function (field) {\n            var val = internalSlots[field];\n            if (val !== undefined) {\n                opts[field] = val;\n            }\n        });\n        opts.pluralCategories = tslib_1.__spreadArray([], PluralRules.localeData[opts.locale].categories[opts.type], true);\n        return opts;\n    };\n    PluralRules.prototype.select = function (val) {\n        var pr = this;\n        validateInstance(pr, 'select');\n        var n = (0, ecma402_abstract_1.ToNumber)(val);\n        return (0, ResolvePlural_1.ResolvePlural)(pr, n, { getInternalSlots: get_internal_slots_1.default, PluralRuleSelect: PluralRuleSelect });\n    };\n    PluralRules.prototype.toString = function () {\n        return '[object Intl.PluralRules]';\n    };\n    PluralRules.supportedLocalesOf = function (locales, options) {\n        return (0, ecma402_abstract_1.SupportedLocales)(PluralRules.availableLocales, (0, ecma402_abstract_1.CanonicalizeLocaleList)(locales), options);\n    };\n    PluralRules.__addLocaleData = function () {\n        var data = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            data[_i] = arguments[_i];\n        }\n        for (var _a = 0, data_1 = data; _a < data_1.length; _a++) {\n            var _b = data_1[_a], d = _b.data, locale = _b.locale;\n            PluralRules.localeData[locale] = d;\n            PluralRules.availableLocales.add(locale);\n            if (!PluralRules.__defaultLocale) {\n                PluralRules.__defaultLocale = locale;\n            }\n        }\n    };\n    PluralRules.getDefaultLocale = function () {\n        return PluralRules.__defaultLocale;\n    };\n    PluralRules.localeData = {};\n    PluralRules.availableLocales = new Set();\n    PluralRules.__defaultLocale = '';\n    PluralRules.relevantExtensionKeys = [];\n    PluralRules.polyfilled = true;\n    return PluralRules;\n}());\nexports.PluralRules = PluralRules;\ntry {\n    // IE11 does not have Symbol\n    if (typeof Symbol !== 'undefined') {\n        Object.defineProperty(PluralRules.prototype, Symbol.toStringTag, {\n            value: 'Intl.PluralRules',\n            writable: false,\n            enumerable: false,\n            configurable: true,\n        });\n    }\n    try {\n        // https://github.com/tc39/test262/blob/master/test/intl402/PluralRules/length.js\n        Object.defineProperty(PluralRules, 'length', {\n            value: 0,\n            writable: false,\n            enumerable: false,\n            configurable: true,\n        });\n    }\n    catch (error) {\n        // IE 11 sets Function.prototype.length to be non-configurable which will cause the\n        // above Object.defineProperty to throw an error.\n    }\n    // https://github.com/tc39/test262/blob/master/test/intl402/RelativeTimeFormat/constructor/length.js\n    Object.defineProperty(PluralRules.prototype.constructor, 'length', {\n        value: 0,\n        writable: false,\n        enumerable: false,\n        configurable: true,\n    });\n    // https://github.com/tc39/test262/blob/master/test/intl402/RelativeTimeFormat/constructor/supportedLocalesOf/length.js\n    Object.defineProperty(PluralRules.supportedLocalesOf, 'length', {\n        value: 1,\n        writable: false,\n        enumerable: false,\n        configurable: true,\n    });\n}\ncatch (ex) {\n    // Meta fixes for test262\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar _1 = require(\"./\");\nObject.defineProperty(Intl, 'PluralRules', {\n    value: _1.PluralRules,\n    writable: true,\n    enumerable: false,\n    configurable: true,\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAIO,SAAS,uBAAuB,SAAS;AAE5C,SAAO,KAAK,oBAAoB,OAAO;AAC3C;AAPA;AAAA;AAAA;AAAA;;;ACIO,SAAS,yBAAyB,IAAI,IAAI;AAC7C,MAAI,YAAY,GAAG,WAAW,iBAAiB,GAAG;AAClD,MAAI,eAAe,GAAG,YAAY;AAClC,MAAI,kBAAkB,UAAU,OAAO,SAAU,KAAK,GAAG;AACrD,QAAI,EAAE,YAAY,CAAC,IAAI;AACvB,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,MAAI,eAAe,eAAe,YAAY,KAAK,gBAAgB,YAAY;AAC/E,MAAI,iBAAiB,aAAa,iBAAiB,WAAW;AAC1D,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAhBA;AAAA;AAAA;AAAA;;;ACGO,SAAS,SAAS,GAAG;AAExB,MAAI,OAAO,MAAM,UAAU;AACvB,UAAM,UAAU,2CAA2C;AAAA,EAC/D;AACA,SAAO,OAAO,CAAC;AACnB;AAKO,SAAS,SAAS,KAAK;AAC1B,MAAI,QAAQ,QAAW;AACnB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,MAAM;AACd,WAAO;AAAA,EACX;AACA,MAAI,OAAO,QAAQ,WAAW;AAC1B,WAAO,MAAM,IAAI;AAAA,EACrB;AACA,MAAI,OAAO,QAAQ,UAAU;AACzB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;AACpD,UAAM,IAAI,UAAU,wCAAwC;AAAA,EAChE;AACA,SAAO,OAAO,GAAG;AACrB;AAKA,SAAS,UAAU,GAAG;AAClB,MAAI,SAAS,SAAS,CAAC;AACvB,MAAI,MAAM,MAAM,KAAK,UAAU,QAAQ,EAAE,GAAG;AACxC,WAAO;AAAA,EACX;AACA,MAAI,SAAS,MAAM,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC;AACzC,MAAI,SAAS,GAAG;AACZ,cAAU,CAAC;AAAA,EACf;AACA,MAAI,UAAU,SAAS,EAAE,GAAG;AACxB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAKO,SAAS,SAAS,MAAM;AAC3B,MAAI,CAAC,SAAS,IAAI,GAAG;AACjB,WAAO;AAAA,EACX;AACA,MAAI,KAAK,IAAI,IAAI,IAAI,OAAO,MAAM;AAC9B,WAAO;AAAA,EACX;AACA,SAAO,UAAU,IAAI;AACzB;AAKO,SAAS,SAAS,KAAK;AAC1B,MAAI,OAAO,MAAM;AACb,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACtE;AACA,SAAO,OAAO,GAAG;AACrB;AAMO,SAAS,UAAU,GAAG,GAAG;AAC5B,MAAI,OAAO,IAAI;AACX,WAAO,OAAO,GAAG,GAAG,CAAC;AAAA,EACzB;AAEA,MAAI,MAAM,GAAG;AAGT,WAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,EACpC;AAEA,SAAO,MAAM,KAAK,MAAM;AAC5B;AAKO,SAAS,YAAY,KAAK;AAC7B,SAAO,IAAI,MAAM,GAAG;AACxB;AAMO,SAAS,eAAe,GAAG,MAAM;AACpC,SAAO,OAAO,UAAU,eAAe,KAAK,GAAG,IAAI;AACvD;AAKO,SAAS,KAAK,GAAG;AACpB,MAAI,MAAM,MAAM;AACZ,WAAO;AAAA,EACX;AACA,MAAI,OAAO,MAAM,aAAa;AAC1B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,MAAM,cAAc,OAAO,MAAM,UAAU;AAClD,WAAO;AAAA,EACX;AACA,MAAI,OAAO,MAAM,UAAU;AACvB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,MAAM,WAAW;AACxB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,MAAM,UAAU;AACvB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,MAAM,UAAU;AACvB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,MAAM,UAAU;AACvB,WAAO;AAAA,EACX;AACJ;AAQA,SAAS,IAAI,GAAG,GAAG;AACf,SAAO,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI;AACnC;AAKO,SAAS,IAAI,GAAG;AACnB,SAAO,KAAK,MAAM,IAAI,UAAU;AACpC;AAKO,SAAS,QAAQ,GAAG;AACvB,SAAO,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC;AAC5B;AAKO,SAAS,YAAY,GAAG;AAC3B,SAAO,KAAK,IAAI,GAAG,CAAC,IAAI;AAC5B;AAKO,SAAS,aAAa,GAAG;AAC5B,SAAO,KAAK,IAAI,GAAG,CAAC;AACxB;AAKO,SAAS,aAAa,GAAG;AAC5B,SAAO,IAAI,KAAK,CAAC,EAAE,eAAe;AACtC;AACO,SAAS,WAAW,GAAG;AAC1B,MAAI,IAAI,MAAM,GAAG;AACb,WAAO;AAAA,EACX;AACA,MAAI,IAAI,QAAQ,GAAG;AACf,WAAO;AAAA,EACX;AACA,MAAI,IAAI,QAAQ,GAAG;AACf,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACO,SAAS,cAAc,GAAG;AAC7B,SAAO,IAAI,CAAC,IAAI,YAAY,aAAa,CAAC,CAAC;AAC/C;AACO,SAAS,WAAW,GAAG;AAC1B,SAAO,WAAW,aAAa,CAAC,CAAC,MAAM,MAAM,IAAI;AACrD;AAKO,SAAS,cAAc,GAAG;AAC7B,MAAI,MAAM,cAAc,CAAC;AACzB,MAAI,OAAO,WAAW,CAAC;AACvB,MAAI,OAAO,KAAK,MAAM,IAAI;AACtB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,KAAK,MAAM;AACjB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,KAAK,MAAM;AACjB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,MAAM,MAAM;AAClB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,MAAM,MAAM;AAClB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,MAAM,MAAM;AAClB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,MAAM,MAAM;AAClB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,MAAM,MAAM;AAClB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,MAAM,MAAM;AAClB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,MAAM,MAAM;AAClB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,MAAM,MAAM;AAClB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,MAAM,MAAM;AAClB,WAAO;AAAA,EACX;AACA,QAAM,IAAI,MAAM,cAAc;AAClC;AACO,SAAS,aAAa,GAAG;AAC5B,MAAI,MAAM,cAAc,CAAC;AACzB,MAAI,MAAM,cAAc,CAAC;AACzB,MAAI,OAAO,WAAW,CAAC;AACvB,MAAI,QAAQ,GAAG;AACX,WAAO,MAAM;AAAA,EACjB;AACA,MAAI,QAAQ,GAAG;AACX,WAAO,MAAM;AAAA,EACjB;AACA,MAAI,QAAQ,GAAG;AACX,WAAO,MAAM,KAAK;AAAA,EACtB;AACA,MAAI,QAAQ,GAAG;AACX,WAAO,MAAM,KAAK;AAAA,EACtB;AACA,MAAI,QAAQ,GAAG;AACX,WAAO,MAAM,MAAM;AAAA,EACvB;AACA,MAAI,QAAQ,GAAG;AACX,WAAO,MAAM,MAAM;AAAA,EACvB;AACA,MAAI,QAAQ,GAAG;AACX,WAAO,MAAM,MAAM;AAAA,EACvB;AACA,MAAI,QAAQ,GAAG;AACX,WAAO,MAAM,MAAM;AAAA,EACvB;AACA,MAAI,QAAQ,GAAG;AACX,WAAO,MAAM,MAAM;AAAA,EACvB;AACA,MAAI,QAAQ,GAAG;AACX,WAAO,MAAM,MAAM;AAAA,EACvB;AACA,MAAI,QAAQ,IAAI;AACZ,WAAO,MAAM,MAAM;AAAA,EACvB;AACA,MAAI,QAAQ,IAAI;AACZ,WAAO,MAAM,MAAM;AAAA,EACvB;AACA,QAAM,IAAI,MAAM,cAAc;AAClC;AAOO,SAAS,aAAa,GAAG;AAC5B,SAAO,IAAI,KAAK,MAAM,IAAI,WAAW,GAAG,aAAa;AACzD;AACO,SAAS,YAAY,GAAG;AAC3B,SAAO,IAAI,KAAK,MAAM,IAAI,aAAa,GAAG,gBAAgB;AAC9D;AACO,SAAS,YAAY,GAAG;AAC3B,SAAO,IAAI,KAAK,MAAM,IAAI,aAAa,GAAG,kBAAkB;AAChE;AACA,SAAS,WAAW,IAAI;AACpB,SAAO,OAAO,OAAO;AACzB;AAUO,SAAS,oBAAoB,GAAG,GAAG,eAAe;AACrD,MAAI,CAAC,WAAW,CAAC,GAAG;AAChB,WAAO;AAAA,EACX;AACA,MAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,qBAAqB;AACjG,QAAI,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc;AACrF,WAAO,aAAa;AAAA,EACxB;AACA,MAAI,OAAO,MAAM,UAAU;AACvB,WAAO;AAAA,EACX;AACA,MAAI,IAAI,EAAE;AACV,MAAI,OAAO,MAAM,UAAU;AACvB,UAAM,IAAI,UAAU,6EAA6E;AAAA,EACrG;AACA,SAAO,OAAO,UAAU,cAAc,KAAK,GAAG,CAAC;AACnD;AACO,SAAS,WAAW,GAAG;AAC1B,SAAO,IAAI,GAAG,aAAa;AAC/B;AA/UA,IA2II,YAsJA,eACA,kBACA,oBACA,eACA,eACA;AAtSJ;AAAA;AA2IA,IAAI,aAAa;AAsJjB,IAAI,gBAAgB;AACpB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB,gBAAgB;AACpC,IAAI,cAAc,gBAAgB;AAAA;AAAA;;;AChS3B,SAAS,sBAAsB,SAAS;AAC3C,MAAI,OAAO,YAAY,aAAa;AAChC,WAAO,uBAAO,OAAO,IAAI;AAAA,EAC7B;AACA,SAAO,SAAS,OAAO;AAC3B;AAXA;AAAA;AAAA;AAAA;AAAA;;;ACOO,SAAS,oBAAoB,UAAU,KAAK,KAAK,UAAU;AAC9D,MAAI,aAAa,QAAW;AAExB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,OAAO,QAAQ;AACzB,MAAI,MAAM,GAAG,KAAK,MAAM,OAAO,MAAM,KAAK;AACtC,UAAM,IAAI,WAAW,GAAG,OAAO,KAAK,wBAAwB,EAAE,OAAO,KAAK,IAAI,EAAE,OAAO,KAAK,GAAG,CAAC;AAAA,EACpG;AACA,SAAO,KAAK,MAAM,GAAG;AACzB;AAjBA;AAAA;AAAA;AAAA;;;ACSO,SAAS,gBAAgB,SAAS,UAAU,SAAS,SAAS,UAAU;AAC3E,MAAI,MAAM,QAAQ,QAAQ;AAC1B,SAAO,oBAAoB,KAAK,SAAS,SAAS,QAAQ;AAC9D;AAZA;AAAA;AAQA;AAAA;AAAA;;;ACCO,SAAS,UAAU,MAAM,MAAM,MAAM,QAAQ,UAAU;AAC1D,MAAI,OAAO,SAAS,UAAU;AAC1B,UAAM,IAAI,UAAU,2BAA2B;AAAA,EACnD;AACA,MAAI,QAAQ,KAAK,IAAI;AACrB,MAAI,UAAU,QAAW;AACrB,QAAI,SAAS,aAAa,SAAS,UAAU;AACzC,YAAM,IAAI,UAAU,cAAc;AAAA,IACtC;AACA,QAAI,SAAS,WAAW;AACpB,cAAQ,QAAQ,KAAK;AAAA,IACzB;AACA,QAAI,SAAS,UAAU;AACnB,cAAQ,SAAS,KAAK;AAAA,IAC1B;AACA,QAAI,WAAW,UAAa,CAAC,OAAO,OAAO,SAAU,KAAK;AAAE,aAAO,OAAO;AAAA,IAAO,CAAC,EAAE,QAAQ;AACxF,YAAM,IAAI,WAAW,GAAG,OAAO,OAAO,iBAAiB,EAAE,OAAO,OAAO,KAAK,IAAI,CAAC,CAAC;AAAA,IACtF;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AA9BA;AAAA;AAAA;AAAA;AAAA;;;ACKO,SAAS,iBAAiB,SAAS;AACtC,MAAI,OAAO,YAAY,aAAa;AAChC,WAAO,uBAAO,OAAO,IAAI;AAAA,EAC7B;AACA,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO;AAAA,EACX;AACA,QAAM,IAAI,UAAU,2BAA2B;AACnD;AAbA;AAAA;AAAA;AAAA;;;ACUO,SAAS,yBAAyB,MAAM,MAAM,QAAQ,WAAW,YAAY,UAAU;AAC1F,MAAI,QAAQ,KAAK,IAAI;AACrB,MAAI,UAAU,QAAW;AACrB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,MAAM;AAChB,WAAO;AAAA,EACX;AACA,MAAI,eAAe,QAAQ,KAAK;AAChC,MAAI,iBAAiB,OAAO;AACxB,WAAO;AAAA,EACX;AACA,UAAQ,SAAS,KAAK;AACtB,MAAI,UAAU,UAAU,UAAU,SAAS;AACvC,WAAO;AAAA,EACX;AACA,OAAK,UAAU,CAAC,GAAG,QAAQ,KAAK,MAAM,IAAI;AACtC,UAAM,IAAI,WAAW,iBAAiB,OAAO,KAAK,CAAC;AAAA,EACvD;AACA,SAAO;AACX;AA9BA;AAAA;AASA;AAAA;AAAA;;;ACyCO,SAAS,oBAAoB,MAAM;AACtC,SAAO,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI,CAAC;AAC3C;AAQO,SAAS,iCAAiC,gBAAgB;AAC7D,SAAO,aAAa,QAAQ,cAAc,IAAI;AAClD;AA9DA,IAGW,kBAqDA;AAxDX;AAAA;AAGO,IAAI,mBAAmB;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AASO,IAAI,eAAe,iBAAiB,IAAI,mBAAmB;AAAA;AAAA;;;ACnD3D,SAAS,oBAAoB,IAAI,IAAI;AACxC,MAAI,oBAAoB,GAAG,mBAAmB,iBAAiB,GAAG;AAClE,MAAI,eAAe,GAAG,YAAY;AAClC,MAAI,YAAY,oBAAI,IAAI;AACxB,MAAI,YAAY,oBAAI,IAAI;AACxB,oBAAkB,IAAI,SAAU,GAAG;AAAE,WAAO,EAAE,YAAY;AAAA,EAAG,CAAC,EAAE,QAAQ,SAAU,GAAG;AAAE,WAAO,UAAU,IAAI,CAAC;AAAA,EAAG,CAAC;AACjH,SAAO,KAAK,cAAc,EAAE,QAAQ,SAAU,UAAU;AACpD,cAAU,IAAI,SAAS,YAAY,CAAC;AACpC,cAAU,IAAI,eAAe,QAAQ,EAAE,YAAY,CAAC;AAAA,EACxD,CAAC;AACD,SAAO,UAAU,IAAI,YAAY,KAAK,UAAU,IAAI,YAAY;AACpE;AAhBA;AAAA;AAAA;AAAA;;;ACIA,SAAS,YAAY,KAAK;AACtB,SAAO,IAAI,QAAQ,YAAY,SAAU,GAAG,GAAG;AAAE,WAAO,EAAE,YAAY;AAAA,EAAG,CAAC;AAC9E;AAKO,SAAS,yBAAyB,UAAU;AAC/C,aAAW,YAAY,QAAQ;AAC/B,MAAI,SAAS,WAAW,GAAG;AACvB,WAAO;AAAA,EACX;AACA,MAAI,cAAc,KAAK,QAAQ,GAAG;AAC9B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AApBA,IAOI;AAPJ;AAAA;AAOA,IAAI,gBAAgB;AAAA;AAAA;;;ACFpB,SAAS,YAAY,KAAK;AACtB,SAAO,IAAI,QAAQ,YAAY,SAAU,GAAG,GAAG;AAAE,WAAO,EAAE,YAAY;AAAA,EAAG,CAAC;AAC9E;AAKO,SAAS,2BAA2B,MAAM;AAC7C,SAAO,YAAY,IAAI;AACvB,MAAI,iCAAiC,IAAI,GAAG;AACxC,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,KAAK,MAAM,OAAO;AAC9B,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO;AAAA,EACX;AACA,MAAI,YAAY,MAAM,CAAC,GAAG,cAAc,MAAM,CAAC;AAC/C,MAAI,CAAC,iCAAiC,SAAS,KAC3C,CAAC,iCAAiC,WAAW,GAAG;AAChD,WAAO;AAAA,EACX;AACA,SAAO;AACX;AA3BA;AAAA;AAAA;AAAA;AAAA;;;ACAO,SAAS,0BAA0B,GAAG,IAAI,IAAI,sBAAsB;AACvE,MAAI,MAAM;AACN,WAAO;AACX,MAAI,yBAAyB,QAAW;AACpC,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACvD;AACA,MAAI,yBAAyB,QAAQ;AACjC,WAAO;AAAA,EACX;AACA,MAAI,yBAAyB,YAAY;AACrC,WAAO;AAAA,EACX;AACA,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,IAAI;AACT,WAAO;AAAA,EACX;AACA,MAAI,KAAK,IAAI;AACT,WAAO;AAAA,EACX;AACA,MAAI,OAAO,IAAI;AACX,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACtC;AACA,MAAI,yBAAyB,aAAa;AACtC,WAAO;AAAA,EACX;AACA,MAAI,yBAAyB,iBAAiB;AAC1C,WAAO;AAAA,EACX;AACA,MAAI,yBAAyB,aAAa;AACtC,UAAM,IAAI,MAAM,8CAA8C,OAAO,oBAAoB,CAAC;AAAA,EAC9F;AACA,MAAI,cAAe,MAAM,KAAK,MAAO;AACrC,MAAI,gBAAgB,GAAG;AACnB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AArCA;AAAA;AAAA;AAAA;;;ACGO,SAAS,oBAAoB,QAAQ;AACxC,SAAO;AACX;AALA;AAAA;AAAA;AAAA;;;ACIO,SAAS,aAAa,GAAG;AAG5B,SAAO,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM;AAC/C;AACO,SAAS,OAAO,GAAG,OAAO;AAC7B,MAAI,OAAO,EAAE,WAAW,YAAY;AAChC,WAAO,EAAE,OAAO,KAAK;AAAA,EACzB;AACA,MAAI,MAAM,IAAI,MAAM,KAAK;AACzB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,QAAI,CAAC,IAAI;AAAA,EACb;AACA,SAAO,IAAI,KAAK,EAAE;AACtB;AACO,SAAS,gBAAgB,KAAK,IAAI,OAAO,OAAO;AACnD,MAAI,CAAC,IAAI,IAAI,EAAE,GAAG;AACd,QAAI,IAAI,IAAI,uBAAO,OAAO,IAAI,CAAC;AAAA,EACnC;AACA,MAAI,QAAQ,IAAI,IAAI,EAAE;AACtB,QAAM,KAAK,IAAI;AACnB;AACO,SAAS,sBAAsB,KAAK,IAAI,OAAO;AAClD,WAAS,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC5D,QAAI,IAAI,GAAG,EAAE;AACb,oBAAgB,KAAK,IAAI,GAAG,MAAM,CAAC,CAAC;AAAA,EACxC;AACJ;AACO,SAAS,gBAAgB,KAAK,IAAI,OAAO;AAC5C,SAAO,sBAAsB,KAAK,IAAI,KAAK,EAAE,KAAK;AACtD;AACO,SAAS,sBAAsB,KAAK,IAAI;AAC3C,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAO,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EACjC;AACA,MAAI,QAAQ,IAAI,IAAI,EAAE;AACtB,MAAI,CAAC,OAAO;AACR,UAAM,IAAI,UAAU,GAAG,OAAO,IAAI,wCAAwC,CAAC;AAAA,EAC/E;AACA,SAAO,OAAO,OAAO,SAAU,KAAK,GAAG;AACnC,QAAI,CAAC,IAAI,MAAM,CAAC;AAChB,WAAO;AAAA,EACX,GAAG,uBAAO,OAAO,IAAI,CAAC;AAC1B;AACO,SAAS,cAAc,aAAa;AACvC,SAAO,YAAY,SAAS;AAChC;AAWO,SAAS,eAAe,QAAQ,MAAM,IAAI;AAC7C,MAAI,QAAQ,GAAG;AACf,SAAO,eAAe,QAAQ,MAAM;AAAA,IAChC,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,UAAU;AAAA,IACV;AAAA,EACJ,CAAC;AACL;AAOO,SAAS,mBAAmB,QAAQ,MAAM,OAAO;AACpD,SAAO,eAAe,QAAQ,MAAM;AAAA,IAChC,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,UAAU;AAAA,IACV;AAAA,EACJ,CAAC;AACL;AAEO,SAAS,UAAU,WAAW,SAAS,KAAK;AAC/C,MAAI,QAAQ,QAAQ;AAAE,UAAM;AAAA,EAAO;AACnC,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,IAAI,OAAO;AAAA,EACzB;AACJ;AA3FA;AAAA;AAAA;AAAA;;;ACKO,SAAS,4BAA4B,cAAc,WAAW,IAAI;AACrE,MAAI,mBAAmB,GAAG;AAC1B,MAAI,gBAAgB,iBAAiB,YAAY;AACjD,MAAI,WAAW,cAAc,UAAU,iBAAiB,cAAc,gBAAgB,kBAAkB,cAAc;AACtH,UAAQ,UAAU;AAAA,IACd,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO,KAAK,MAAM,YAAY,CAAC,IAAI;AAAA,IACvC,SAAS;AAGL,UAAI,iBAAiB,cAAc,gBAAgB,QAAQ,cAAc,OAAO,kBAAkB,cAAc;AAChH,UAAI,eAAe;AACnB,UAAI,UAAU,cAAc,oBAAoB,QAAQ;AACpD,YAAI,WAAW,eAAe,QAAQ,SAAS,eAAe,KAC1D,eAAe,QAAQ,SAAS,eAAe,QAAQ,GAAG,CAAC,CAAC;AAChE,uBAAe,SAAS;AAAA,MAC5B,OACK;AACD,YAAI,UAAU,eAAe,QAAQ,QAAQ,eAAe,KACxD,eAAe,QAAQ,QAAQ,eAAe,QAAQ,GAAG,CAAC,CAAC;AAC/D,uBAAe,mBAAmB,SAAS,QAAQ,OAAO,QAAQ;AAAA,MACtE;AACA,UAAI,CAAC,cAAc;AACf,eAAO;AAAA,MACX;AACA,UAAI,MAAM,OAAO,KAAK,IAAI,IAAI,SAAS,CAAC;AACxC,UAAI,aAAa,OAAO,KAAK,YAAY;AACzC,UAAI,MAAM,WAAW,CAAC,GAAG;AACrB,eAAO;AAAA,MACX;AACA,UAAI,MAAM,WAAW,WAAW,SAAS,CAAC,GAAG;AACzC,eAAO,WAAW,WAAW,SAAS,CAAC,EAAE,SAAS;AAAA,MACtD;AACA,UAAI,IAAI,WAAW,QAAQ,GAAG;AAC9B,UAAI,MAAM,IAAI;AACV,eAAO;AAAA,MACX;AAGA,UAAI,eAAe,WAAW,CAAC;AAE/B,UAAI,iBAAiB,aAAa,YAAY,EAAE;AAChD,UAAI,mBAAmB,KAAK;AACxB,eAAO;AAAA,MACX;AAEA,aAAQ,aAAa,SACjB,aAAa,YAAY,EAAE,MAAM,MAAM,IAAI,EAAE,CAAC,EAAE;AAAA,IACxD;AAAA,EACJ;AACJ;AA3DA;AAAA;AAAA;AAAA;;;ACCO,SAAS,eAAe,GAAG,cAAc,cAAc;AAC1D,MAAI,IAAI;AACR,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM,GAAG;AACT,QAAI,OAAO,KAAK,CAAC;AACjB,QAAI;AACJ,aAAS;AAAA,EACb,OACK;AACD,QAAI,YAAY,EAAE,SAAS;AAK3B,QAAI,yBAAyB,UAAU,QAAQ,GAAG;AAClD,QAAI,KAAK,UAAU,MAAM,GAAG,GAAG,oBAAoB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC;AAClF,QAAI,uCAAuC,kBAAkB,QAAQ,KAAK,EAAE;AAC5E,QAAI,0BAA0B,KAC1B,qCAAqC,UAAU,GAAG;AAClD,UAAI,CAAC;AACL,UACI,uCACI,OAAO,KAAK,IAAI,qCAAqC,MAAM;AACnE,eAAS;AAAA,IACb,OACK;AACD,UAAI,aAAa,CAAC;AAClB,UAAI,qBAAqB,IAAI,IAAI;AAGjC,UAAI,IAAI,KAAK,MAAM,mBAAmB,GAAG,kBAAkB,CAAC;AAE5D,UAAI,mBAAmB,GAAG,IAAI,CAAC,KAAK,IAAI;AACpC,YAAI,IAAI;AAER,YAAI,KAAK,MAAM,IAAI,EAAE;AAAA,MACzB;AACA,UAAI,EAAE,SAAS;AAEf,eAAS,mBAAmB,GAAG,IAAI,IAAI,CAAC;AAAA,IAC5C;AAAA,EACJ;AACA,MAAI;AACJ,MAAI,KAAK,IAAI,GAAG;AACZ,QAAI,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC;AAC7B,UAAM,IAAI;AAAA,EACd,WACS,KAAK,GAAG;AACb,QAAI,GAAG,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC;AAC3D,UAAM,IAAI;AAAA,EACd,OACK;AACD,QAAI,KAAK,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;AAC7C,UAAM;AAAA,EACV;AACA,MAAI,EAAE,QAAQ,GAAG,KAAK,KAAK,eAAe,cAAc;AACpD,QAAI,MAAM,eAAe;AACzB,WAAO,MAAM,KAAK,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK;AACvC,UAAI,EAAE,MAAM,GAAG,EAAE;AACjB;AAAA,IACJ;AACA,QAAI,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK;AACzB,UAAI,EAAE,MAAM,GAAG,EAAE;AAAA,IACrB;AAAA,EACJ;AACA,SAAO,EAAE,iBAAiB,GAAG,eAAe,QAAQ,oBAAoB,IAAI;AAE5E,WAAS,mBAAmBA,IAAG,WAAW;AACtC,WAAO,YAAY,IAAIA,KAAI,KAAK,IAAI,IAAI,CAAC,SAAS,IAAIA,KAAI,KAAK,IAAI,IAAI,SAAS;AAAA,EACpF;AACJ;AAzEA;AAAA;AAAA;AAAA;AAAA;;;ACQO,SAAS,WAAW,GAAG,aAAa,aAAa;AACpD,MAAI,IAAI;AACR,MAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AACtC,MAAI,SAAS,IAAI,KAAK,IAAI,IAAI,CAAC;AAI/B,MAAI;AACJ,MAAI,IAAI,MAAM;AACV,QAAI,EAAE,SAAS;AAAA,EACnB,OACK;AACD,QAAI,EAAE,SAAS;AACf,QAAI,KAAK,EAAE,MAAM,GAAG,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACxD,QAAI,SAAS,QAAQ,KAAK,EAAE;AAC5B,QAAI,IAAI,OAAO,KAAK,KAAK,IAAI,CAAC,WAAW,EAAE,SAAS,GAAG,CAAC,CAAC;AAAA,EAC7D;AACA,MAAI;AACJ,MAAI,MAAM,GAAG;AACT,QAAI,IAAI,EAAE;AACV,QAAI,KAAK,GAAG;AACR,UAAI,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC;AAC7B,UAAI,IAAI;AACR,UAAI,IAAI;AAAA,IACZ;AACA,QAAI,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;AACxB,QAAI,IAAI,EAAE,MAAM,IAAI,CAAC;AACrB,QAAI,GAAG,OAAO,GAAG,GAAG,EAAE,OAAO,CAAC;AAC9B,UAAM,EAAE;AAAA,EACZ,OACK;AACD,UAAM,EAAE;AAAA,EACZ;AACA,MAAI,MAAM,cAAc;AACxB,SAAO,MAAM,KAAK,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK;AACvC,QAAI,EAAE,MAAM,GAAG,EAAE;AACjB;AAAA,EACJ;AACA,MAAI,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK;AACzB,QAAI,EAAE,MAAM,GAAG,EAAE;AAAA,EACrB;AACA,SAAO,EAAE,iBAAiB,GAAG,eAAe,QAAQ,oBAAoB,IAAI;AAChF;AAlDA;AAAA;AAAA;AAAA;AAAA;;;ACOO,SAAS,sBAAsB,YAAY,GAAG;AACjD,MAAI,aAAa,IAAI,KAAK,UAAU,GAAG,EAAE;AACzC,MAAI,YAAY;AACZ,QAAI,CAAC;AAAA,EACT;AACA,MAAI;AACJ,MAAI,gBAAgB,WAAW;AAC/B,UAAQ,eAAe;AAAA,IACnB,KAAK;AACD,eAAS,eAAe,GAAG,WAAW,0BAA0B,WAAW,wBAAwB;AACnG;AAAA,IACJ,KAAK;AACD,eAAS,WAAW,GAAG,WAAW,uBAAuB,WAAW,qBAAqB;AACzF;AAAA,IACJ;AACI,eAAS,eAAe,GAAG,GAAG,CAAC;AAC/B,UAAI,OAAO,qBAAqB,GAAG;AAC/B,iBAAS,WAAW,GAAG,GAAG,CAAC;AAAA,MAC/B;AACA;AAAA,EACR;AACA,MAAI,OAAO;AACX,MAAI,SAAS,OAAO;AACpB,MAAI,MAAM,OAAO;AACjB,MAAI,aAAa,WAAW;AAC5B,MAAI,MAAM,YAAY;AAClB,QAAI,eAAe,OAAO,KAAK,aAAa,GAAG;AAC/C,aAAS,eAAe;AAAA,EAC5B;AACA,MAAI,YAAY;AACZ,QAAI,CAAC;AAAA,EACT;AACA,SAAO,EAAE,eAAe,GAAG,iBAAiB,OAAO;AACvD;AAxCA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;ACOO,SAAS,gBAAgB,cAAc,GAAG,IAAI;AACjD,MAAI,mBAAmB,GAAG;AAC1B,MAAI,MAAM,GAAG;AACT,WAAO,CAAC,GAAG,CAAC;AAAA,EAChB;AACA,MAAI,IAAI,GAAG;AACP,QAAI,CAAC;AAAA,EACT;AACA,MAAI,YAAY,aAAa,CAAC;AAC9B,MAAI,WAAW,4BAA4B,cAAc,WAAW;AAAA,IAChE;AAAA,EACJ,CAAC;AAED,MAAI,WAAW,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,IAAI,IAAI,QAAQ;AAC1E,MAAI,qBAAqB,sBAAsB,iBAAiB,YAAY,GAAG,CAAC;AAChF,MAAI,mBAAmB,kBAAkB,GAAG;AACxC,WAAO,CAAC,UAAU,SAAS;AAAA,EAC/B;AACA,MAAI,eAAe,aAAa,mBAAmB,aAAa;AAChE,MAAI,iBAAiB,YAAY,UAAU;AACvC,WAAO,CAAC,UAAU,SAAS;AAAA,EAC/B;AACA,SAAO;AAAA,IACH,4BAA4B,cAAc,YAAY,GAAG;AAAA,MACrD;AAAA,IACJ,CAAC;AAAA,IACD,YAAY;AAAA,EAChB;AACJ;AAtCA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACEO,SAAS,eAAe,GAAG,IAAI;AAClC,MAAI,qBAAqB,GAAG;AAC5B,SAAO,eAAe,oBAAoB,CAAC,IACrC,mBAAmB,CAAC,IACpB;AACV;AATA;AAAA;AAAA;AAAA;AAAA;;;ACGO,SAAS,oBAAoB,cAAc,QAAQ,IAAI;AAC1D,MAAI,mBAAmB,GAAG;AAC1B,MAAI,gBAAgB,iBAAiB,YAAY;AACjD,MAAI,UAAU,cAAc,eAAe,QAAQ,QAAQ,cAAc,eAAe;AACxF,MAAI,oBAAoB,QAAQ;AAChC,SAAO,KAAK,EAAE,MAAM,qBAAqB,OAAO,kBAAkB,CAAC;AACnE,SAAO;AACX;AAVA;AAAA;AAAA;AAAA;;;ACAA,IACW;AADX;AAAA;AACO,IAAI,kBAAkB;AAAA;AAAA;;;ACD7B,IAAW;AAAX;AAAA;AAAO,IAAI,eAAe;AAAA,MACtB,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,WAAW;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,WAAW;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,WAAW;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,YAAY;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,WAAW;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,WAAW;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;ACnwBe,SAAR,cAA+B,cAAc,MAAM,IAAI,SAAS;AACnE,MAAI,OAAO,aAAa,MAAM,WAAW,aAAa,UAAU,YAAY,aAAa;AACzF,MAAI,WAAW,QAAQ,UAAU,QAAQ,QAAQ,OAAO,kBAAkB,QAAQ;AAClF,MAAI,yBAAyB,KAAK,QAAQ,GAAG,CAAC;AAG9C,MAAI,uBAAuB;AAC3B,MAAI,aAAa,aAAa,WAAW;AACrC,2BAAuB,yBAAyB,cAAc,IAAI,MAAM,OAAO,QAAQ,gBAAgB,QAAQ,iBAAiB,eAAe;AAAA,EACnJ;AAEA,MAAI;AACJ,MAAI,UAAU,cAAc,QAAQ,oBAAoB,QAAQ;AAC5D,QAAI,oBAAoB,KAAK,WAAW,QAAQ,QAAQ;AACxD,QAAI,mBAAmB;AACnB,cAAQ,QAAQ,iBAAiB;AAAA,QAC7B,KAAK;AACD,gCAAsB,QAAQ;AAC9B;AAAA,QACJ,KAAK;AACD,gCAAsB,kBAAkB;AACxC;AAAA,QACJ;AACI,gCAAsB,kBAAkB;AACxC;AAAA,MACR;AAAA,IACJ,OACK;AAED,4BAAsB,QAAQ;AAAA,IAClC;AAAA,EACJ;AACA,MAAI;AACJ,MAAI,CAAC,sBAAsB;AAGvB,QAAI,UAAU,aACV,UAAU,UACT,UAAU,cAAc,QAAQ,oBAAoB,QAAS;AAE9D,UAAI,cAAc,KAAK,QAAQ,QAAQ,eAAe,KAClD,KAAK,QAAQ,QAAQ,sBAAsB;AAC/C,sBAAgB,kBAAkB,YAAY,UAAU,IAAI;AAAA,IAChE,WACS,UAAU,YAAY;AAC3B,UAAI,eAAe,KAAK,QAAQ,SAAS,eAAe,KACpD,KAAK,QAAQ,SAAS,sBAAsB;AAEhD,sBAAgB,kBAAkB,aAAa,QAAQ,YAAY,GAAG,IAAI;AAAA,IAC9E,OACK;AAED,UAAI,iBAAiB,KAAK,QAAQ,QAAQ,eAAe,KACrD,KAAK,QAAQ,QAAQ,sBAAsB;AAC/C,sBAAgB,kBAAkB,gBAAgB,IAAI;AAAA,IAC1D;AAAA,EACJ,OACK;AACD,oBAAgB;AAAA,EACpB;AAGA,MAAI,uBAAuB,oBAAoB,KAAK,aAAa,EAAE,CAAC;AAIpE,kBAAgB,cACX,QAAQ,qBAAqB,KAAK,EAClC,QAAQ,UAAU,IAAI;AAE3B,MAAI,UAAU,cAAc,QAAQ,oBAAoB,QAAQ;AAC5D,QAAI,eAAe,KAAK,QAAQ,SAAS,eAAe,KACpD,KAAK,QAAQ,SAAS,sBAAsB;AAahD,QAAI,gBAAgB,aAAa,gBAAgB;AACjD,QAAI,iBAAiB,CAAC,uBAAuB,KAAK,mBAAmB,GAAG;AACpE,sBAAgB,cAAc,QAAQ,QAAQ,IAAS,OAAO,eAAe,KAAK,CAAC;AAAA,IACvF;AACA,QAAI,iBAAiB,aAAa,gBAAgB;AAClD,QAAI,kBAAkB,CAAC,sBAAsB,KAAK,mBAAmB,GAAG;AACpE,sBAAgB,cAAc,QAAQ,QAAQ,MAAM,OAAO,gBAAgB,GAAQ,CAAC;AAAA,IACxF;AAAA,EACJ;AAEA,MAAI,qBAAqB,cAAc,MAAM,6BAA6B;AAC1E,MAAI,cAAc,CAAC;AACnB,MAAI,UAAU,KAAK,QAAQ,QAAQ,eAAe,KAC9C,KAAK,QAAQ,QAAQ,sBAAsB;AAC/C,WAAS,KAAK,GAAG,uBAAuB,oBAAoB,KAAK,qBAAqB,QAAQ,MAAM;AAChG,QAAI,OAAO,qBAAqB,EAAE;AAClC,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,YAAQ,MAAM;AAAA,MACV,KAAK,OAAO;AAER,oBAAY,KAAK,MAAM,aAAa;AAAA,UAAwB;AAAA,UAAS;AAAA,UAAc;AAAA,UAAU;AAAA,UAAU;AAAA;AAAA,UAEvG,CAAC,wBAAwB,QAAQ,QAAQ,WAAW;AAAA,UAAG;AAAA,UAAsB;AAAA,QAAK,CAAC;AACnF;AAAA,MACJ;AAAA,MACA,KAAK;AACD,oBAAY,KAAK,EAAE,MAAM,aAAa,OAAO,QAAQ,UAAU,CAAC;AAChE;AAAA,MACJ,KAAK;AACD,oBAAY,KAAK,EAAE,MAAM,YAAY,OAAO,QAAQ,SAAS,CAAC;AAC9D;AAAA,MACJ,KAAK;AACD,oBAAY,KAAK,EAAE,MAAM,eAAe,OAAO,QAAQ,YAAY,CAAC;AACpE;AAAA,MACJ,KAAK;AAED,oBAAY,KAAK,EAAE,MAAM,YAAY,OAAO,oBAAoB,CAAC;AACjE;AAAA,MACJ;AACI,YAAI,QAAQ,KAAK,IAAI,GAAG;AACpB,sBAAY,KAAK;AAAA,YACb,MAAM;AAAA,YACN,OAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AAAA,UAC5C,CAAC;AAAA,QACL,OACK;AAED,sBAAY,KAAK,EAAE,MAAM,WAAW,OAAO,KAAK,CAAC;AAAA,QACrD;AACA;AAAA,IACR;AAAA,EACJ;AAIA,UAAQ,OAAO;AAAA,IACX,KAAK,YAAY;AAEb,UAAI,QAAQ,oBAAoB,QAAQ;AACpC,YAAI,eAAe,KAAK,QAAQ,SAAS,eAAe,KACpD,KAAK,QAAQ,SAAS,sBAAsB,GAAG;AAEnD,YAAI,WAAW;AACf,YAAI,mBAAmB,KAAK,WAAW,QAAQ,QAAQ;AACvD,YAAI,kBAAkB;AAClB,qBAAW,aAAa,IAAI,aAAa,gBAAgB,KAAK,IAAI,IAAI,QAAQ,GAAG,iBAAiB,WAAW;AAAA,QACjH,OACK;AAED,qBAAW,QAAQ;AAAA,QACvB;AAEA,YAAI,mBAAmB,YAAY,MAAM,aAAa;AACtD,YAAI,SAAS,CAAC;AACd,iBAAS,KAAK,GAAG,qBAAqB,kBAAkB,KAAK,mBAAmB,QAAQ,MAAM;AAC1F,cAAI,OAAO,mBAAmB,EAAE;AAChC,kBAAQ,MAAM;AAAA,YACV,KAAK;AACD,qBAAO,KAAK,MAAM,QAAQ,WAAW;AACrC;AAAA,YACJ,KAAK;AACD,qBAAO,KAAK,EAAE,MAAM,YAAY,OAAO,SAAS,CAAC;AACjD;AAAA,YACJ;AACI,kBAAI,MAAM;AACN,uBAAO,KAAK,EAAE,MAAM,WAAW,OAAO,KAAK,CAAC;AAAA,cAChD;AACA;AAAA,UACR;AAAA,QACJ;AACA,eAAO;AAAA,MACX,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,IACA,KAAK,QAAQ;AACT,UAAI,OAAO,QAAQ,MAAM,cAAc,QAAQ;AAC/C,UAAI,WAAW,KAAK,MAAM,OAAO,IAAI;AACrC,UAAI,cAAc;AAClB,UAAI,UAAU;AAEV,sBAAc,aAAa,IAAI,aAAa,gBAAgB,KAAK,IAAI,IAAI,QAAQ,GAAG,KAAK,MAAM,OAAO,IAAI,EAAE,WAAW,CAAC;AAAA,MAC5H,OACK;AAID,YAAI,KAAK,KAAK,MAAM,OAAO,GAAG,gBAAgB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC;AAC3E,mBAAW,KAAK,MAAM,OAAO,aAAa;AAC1C,YAAI,uBAAuB,aAAa,IAAI,aAAa,gBAAgB,KAAK,IAAI,IAAI,QAAQ,GAAG,KAAK,MAAM,OAAO,aAAa,EAAE,WAAW,CAAC;AAC9I,YAAI,iBAAiB,KAAK,MAAM,OAAO,eAAe,EAAE,QAAQ,WAAW;AAC3E,YAAI,gBAAgB;AAEhB,wBAAc,eAAe,QAAQ,OAAO,oBAAoB;AAAA,QACpE,OACK;AAGD,cAAI,aAAa,KAAK,MAAM,SAAS,IAAI,WAAW;AACpD,cAAI,qBAAqB,aAAa,IAAI,GAAG,KAAK,MAAM,OAAO,eAAe,EAAE,WAAW,CAAC;AAC5F,wBAAc,cAAc,WACvB,QAAQ,OAAO,oBAAoB,EACnC,QAAQ,OAAO,mBAAmB,QAAQ,OAAO,EAAE,CAAC;AAAA,QAC7D;AAAA,MACJ;AACA,UAAI,SAAS,CAAC;AAEd,eAAS,KAAK,GAAG,KAAK,YAAY,MAAM,eAAe,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC5E,YAAI,OAAO,GAAG,EAAE;AAChB,YAAI,mBAAmB,oBAAoB,KAAK,IAAI;AACpD,YAAI,kBAAkB;AAElB,cAAI,iBAAiB,CAAC,GAAG;AACrB,mBAAO,KAAK,EAAE,MAAM,WAAW,OAAO,iBAAiB,CAAC,EAAE,CAAC;AAAA,UAC/D;AAEA,iBAAO,KAAK,MAAM,QAAQ,WAAW;AAErC,cAAI,iBAAiB,CAAC,GAAG;AACrB,mBAAO,KAAK,EAAE,MAAM,WAAW,OAAO,iBAAiB,CAAC,EAAE,CAAC;AAAA,UAC/D;AAAA,QACJ,WACS,MAAM;AACX,iBAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,KAAK,CAAC;AAAA,QAC7C;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,IACA;AACI,aAAO;AAAA,EACf;AAEJ;AAGA,SAAS,wBAAwB,SAAS,cAAc,UAAU,UAAU,iBAAiB,aAQ7F,sBAAsB,OAAO;AACzB,MAAI,SAAS,CAAC;AAEd,MAAI,IAAI,aAAa,iBAAiB,IAAI,aAAa;AACvD,MAAI,MAAM,CAAC,GAAG;AACV,WAAO,CAAC,EAAE,MAAM,OAAO,OAAO,EAAE,CAAC;AAAA,EACrC,WACS,CAAC,SAAS,CAAC,GAAG;AACnB,WAAO,CAAC,EAAE,MAAM,YAAY,OAAO,EAAE,CAAC;AAAA,EAC1C;AACA,MAAI,wBAAwB,aAAa,eAAe;AACxD,MAAI,uBAAuB;AACvB,QAAI,EAAE,QAAQ,OAAO,SAAU,OAAO;AAAE,aAAO,sBAAsB,CAAC,KAAK,KAAK;AAAA,IAAO,CAAC;AAAA,EAC5F;AAGA,MAAI,kBAAkB,EAAE,QAAQ,GAAG;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI,kBAAkB,GAAG;AACrB,cAAU,EAAE,MAAM,GAAG,eAAe;AACpC,eAAW,EAAE,MAAM,kBAAkB,CAAC;AAAA,EAC1C,OACK;AACD,cAAU;AAAA,EACd;AAOA,MAAI,gBAAgB,aAAa,aAAa,KAAK,MAAQ;AAGvD,QAAI,iBAAiB,UAAU,cAAc,QAAQ,iBAAiB,OAChE,QAAQ,gBACR,QAAQ;AACd,QAAI,SAAS,CAAC;AAMd,QAAI,uBAAuB,qBAAqB,MAAM,GAAG,EAAE,CAAC;AAC5D,QAAI,gBAAgB,qBAAqB,MAAM,GAAG;AAClD,QAAI,sBAAsB;AAC1B,QAAI,wBAAwB;AAC5B,QAAI,cAAc,SAAS,GAAG;AAC1B,4BAAsB,cAAc,cAAc,SAAS,CAAC,EAAE;AAAA,IAClE;AACA,QAAI,cAAc,SAAS,GAAG;AAC1B,8BAAwB,cAAc,cAAc,SAAS,CAAC,EAAE;AAAA,IACpE;AACA,QAAI,IAAI,QAAQ,SAAS;AACzB,QAAI,IAAI,GAAG;AAEP,aAAO,KAAK,QAAQ,MAAM,GAAG,IAAI,mBAAmB,CAAC;AAGrD,WAAK,KAAK,uBAAuB,IAAI,GAAG,KAAK,uBAAuB;AAChE,eAAO,KAAK,QAAQ,MAAM,GAAG,IAAI,qBAAqB,CAAC;AAAA,MAC3D;AACA,aAAO,KAAK,QAAQ,MAAM,GAAG,IAAI,qBAAqB,CAAC;AAAA,IAC3D,OACK;AACD,aAAO,KAAK,OAAO;AAAA,IACvB;AACA,WAAO,OAAO,SAAS,GAAG;AACtB,UAAI,eAAe,OAAO,IAAI;AAC9B,aAAO,KAAK,EAAE,MAAM,WAAW,OAAO,aAAa,CAAC;AACpD,UAAI,OAAO,SAAS,GAAG;AACnB,eAAO,KAAK,EAAE,MAAM,SAAS,OAAO,eAAe,CAAC;AAAA,MACxD;AAAA,IACJ;AAAA,EACJ,OACK;AACD,WAAO,KAAK,EAAE,MAAM,WAAW,OAAO,QAAQ,CAAC;AAAA,EACnD;AAEA,MAAI,aAAa,QAAW;AACxB,QAAI,mBAAmB,UAAU,cAAc,QAAQ,mBAAmB,OACpE,QAAQ,kBACR,QAAQ;AACd,WAAO,KAAK,EAAE,MAAM,WAAW,OAAO,iBAAiB,GAAG,EAAE,MAAM,YAAY,OAAO,SAAS,CAAC;AAAA,EACnG;AACA,OAAK,aAAa,gBAAgB,aAAa,kBAC3C,SAAS,CAAC,GAAG;AACb,WAAO,KAAK,EAAE,MAAM,qBAAqB,OAAO,QAAQ,YAAY,CAAC;AACrE,QAAI,WAAW,GAAG;AACd,aAAO,KAAK,EAAE,MAAM,qBAAqB,OAAO,QAAQ,UAAU,CAAC;AACnE,iBAAW,CAAC;AAAA,IAChB;AACA,QAAI,iBAAiB,WAAW,UAAU,GAAG,CAAC;AAC9C,WAAO,KAAK;AAAA,MACR,MAAM;AAAA,MACN,OAAO,eAAe;AAAA,IAC1B,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,SAAS,MAAM;AACtC,MAAI,QAAQ,QAAQ,GAAG,IAAI,GAAG;AAC1B,cAAU,GAAG,OAAO,SAAS,IAAI,EAAE,OAAO,OAAO;AAAA,EACrD;AACA,MAAI,KAAK,QAAQ,MAAM,GAAG,GAAG,cAAc,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC;AACxE,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,aAAO,gBAAgB,QAAQ,GAAG,KAAK,IACjC,gBAAgB,QAAQ,MAAM,GAAG,IACjC,IAAI,OAAO,WAAW;AAAA,EACpC;AACJ;AAQA,SAAS,yBAAyB,cAAc,IAAI,MAAM,OAAO,gBAAgB,iBAAiB,iBAAiB;AAC/G,MAAI;AACJ,MAAI,gBAAgB,aAAa,eAAe,OAAO,aAAa,MAAM,YAAY,aAAa;AACnG,MAAI,eAAe,OAAO,KAAK,IAAI,IAAI,SAAS,CAAC;AACjD,MAAI,yBAAyB,KAAK,QAAQ,GAAG,CAAC;AAC9C,MAAI;AACJ,MAAI,UAAU,cAAc,oBAAoB,QAAQ;AACpD,QAAI,oBAAoB,KAAK,QAAQ;AACrC,QAAI,eAAe,kBAAkB,eAAe,KAChD,kBAAkB,sBAAsB;AAE5C,QAAI,sBAAsB,KAAK,aAAa,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AACvG,QAAI,CAAC,oBAAoB;AACrB,aAAO;AAAA,IACX;AACA,cAAU,aAAa,IAAI,eAAe,kBAAkB;AAAA,EAChE,OACK;AACD,QAAI,oBAAoB,KAAK,QAAQ;AACrC,QAAI,mBAAmB,kBAAkB,eAAe,KACpD,kBAAkB,sBAAsB;AAC5C,QAAI,oBAAoB,iBAAiB,cAAc,EAAE,YAAY;AACrE,QAAI,CAAC,mBAAmB;AACpB,aAAO;AAAA,IACX;AACA,cAAU,aAAa,IAAI,eAAe,iBAAiB;AAAA,EAC/D;AAIA,MAAI,YAAY,KAAK;AACjB,WAAO;AAAA,EACX;AACA,YAAU,kBAAkB,SAAS,IAAI,EAEpC,QAAQ,qBAAqB,QAAQ,EAErC,QAAQ,MAAM,GAAG;AACtB,SAAO;AACX;AACA,SAAS,aAAa,IAAI,GAAG,OAAO;AAChC,SAAO,MAAM,GAAG,OAAO,CAAC,CAAC,KAAK,MAAM;AACxC;AA3aA,IAMI,uBAEA,wBACA;AATJ;AAAA;AAAA;AACA;AACA;AAIA,IAAI,wBAAwB,IAAI,OAAO,IAAI,OAAO,gBAAgB,MAAM,CAAC;AAEzE,IAAI,yBAAyB,IAAI,OAAO,GAAG,OAAO,gBAAgB,QAAQ,GAAG,CAAC;AAC9E,IAAI,sBAAsB;AAAA;AAAA;;;ACFnB,SAAS,uBAAuB,cAAc,GAAG,IAAI;AACxD,MAAI;AACJ,MAAI,mBAAmB,GAAG;AAC1B,MAAI,gBAAgB,iBAAiB,YAAY;AACjD,MAAI,KAAK,cAAc,IAAI,iBAAiB,cAAc,gBAAgB,kBAAkB,cAAc;AAC1G,MAAI,UAAU,eAAe,QAAQ,QAAQ,eAAe,KACxD,eAAe,QAAQ,QAAQ,eAAe,QAAQ,GAAG,CAAC,CAAC;AAC/D,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,MAAI;AACJ,MAAI,MAAM,CAAC,GAAG;AACV,QAAI,QAAQ;AAAA,EAChB,WACS,KAAK,OAAO,qBAAqB,KAAK,OAAO,mBAAmB;AACrE,QAAI,QAAQ;AAAA,EAChB,OACK;AACD,QAAI,CAAC,UAAU,GAAG,EAAE,GAAG;AACnB,UAAI,CAAC,SAAS,CAAC,GAAG;AACd,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACxD;AACA,UAAI,cAAc,SAAS,WAAW;AAClC,aAAK;AAAA,MACT;AACA;AACA,WAAK,gBAAgB,cAAc,GAAG;AAAA,QAClC;AAAA,MACJ,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC;AAEtC,UAAI,WAAW,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,IAAI,IAAI,QAAQ;AAAA,IAC9E;AACA,QAAI,qBAAqB,sBAAsB,eAAe,CAAC;AAC/D,QAAI,mBAAmB;AACvB,QAAI,mBAAmB;AAAA,EAC3B;AAGA,MAAI;AACJ,MAAI,cAAc,cAAc;AAChC,UAAQ,aAAa;AAAA,IACjB,KAAK;AACD,aAAO;AACP;AAAA,IACJ,KAAK;AACD,UAAI,UAAU,GAAG,CAAC,KAAK,IAAI,KAAK,MAAM,CAAC,GAAG;AACtC,eAAO;AAAA,MACX,OACK;AACD,eAAO;AAAA,MACX;AACA;AAAA,IACJ,KAAK;AACD,UAAI,UAAU,GAAG,CAAC,KAAK,IAAI,KAAK,MAAM,CAAC,GAAG;AACtC,eAAO;AAAA,MACX,OACK;AACD,eAAO;AAAA,MACX;AACA;AAAA,IACJ;AAEI,UAAI,MAAM,KAAK,MAAM,CAAC,GAAG;AACrB,eAAO;AAAA,MACX,WACS,IAAI,GAAG;AACZ,eAAO;AAAA,MACX,OACK;AACD,eAAO;AAAA,MACX;AAAA,EACR;AACA,SAAO,cAAc,EAAE,eAAe,GAAG,iBAAiB,GAAG,UAAoB,WAAsB,KAAW,GAAG,cAAc,gBAAgB,IAAI,aAAa;AACxK;AA/EA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;ACGO,SAAS,4BAA4B,cAAc,GAAG,GAAG,IAAI;AAChE,MAAI,mBAAmB,GAAG;AAC1B,MAAI,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG;AACtB,UAAM,IAAI,WAAW,wBAAwB;AAAA,EACjD;AACA,MAAI,SAAS,CAAC;AACd,MAAI,UAAU,uBAAuB,cAAc,GAAG,EAAE,iBAAmC,CAAC;AAC5F,MAAI,UAAU,uBAAuB,cAAc,GAAG,EAAE,iBAAmC,CAAC;AAC5F,MAAI,YAAY,SAAS;AACrB,WAAO,oBAAoB,cAAc,SAAS,EAAE,iBAAmC,CAAC;AAAA,EAC5F;AACA,WAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,QAAI,IAAI,UAAU,EAAE;AACpB,MAAE,SAAS;AAAA,EACf;AACA,WAAS,OAAO,OAAO,OAAO;AAC9B,MAAI,gBAAgB,iBAAiB,YAAY;AACjD,MAAI,UAAU,cAAc,eAAe,QAAQ,QAAQ,cAAc,eAAe;AACxF,SAAO,KAAK,EAAE,MAAM,WAAW,OAAO,QAAQ,WAAW,QAAQ,SAAS,CAAC;AAC3E,WAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,QAAI,IAAI,UAAU,EAAE;AACpB,MAAE,SAAS;AAAA,EACf;AACA,WAAS,OAAO,OAAO,OAAO;AAC9B,SAAO,oBAAoB,MAAM;AACrC;AA/BA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACEO,SAAS,mBAAmB,cAAc,GAAG,GAAG,IAAI;AACvD,MAAI,mBAAmB,GAAG;AAC1B,MAAI,QAAQ,4BAA4B,cAAc,GAAG,GAAG;AAAA,IACxD;AAAA,EACJ,CAAC;AACD,SAAO,MAAM,IAAI,SAAU,MAAM;AAAE,WAAO,KAAK;AAAA,EAAO,CAAC,EAAE,KAAK,EAAE;AACpE;AAVA;AAAA;AAAA;AAAA;AAAA;;;ACIO,SAAS,0BAA0B,cAAc,GAAG,GAAG,IAAI;AAC9D,MAAI,mBAAmB,GAAG;AAC1B,MAAI,QAAQ,4BAA4B,cAAc,GAAG,GAAG;AAAA,IACxD;AAAA,EACJ,CAAC;AACD,SAAO,MAAM,IAAI,SAAU,MAAM,OAAO;AAAE,WAAQ;AAAA,MAC9C,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,QAAQ,MAAM,SAAS;AAAA,IAC3B;AAAA,EAAI,CAAC;AACT;AAfA;AAAA;AAAA;AAAA;AAAA;;;ACEO,SAAS,qBAAqB,IAAI,GAAG,aAAa;AACrD,MAAI,QAAQ,uBAAuB,IAAI,GAAG,WAAW;AACrD,MAAI,SAAS,YAAY,CAAC;AAC1B,WAAS,KAAK,GAAG,UAAU,OAAO,KAAK,QAAQ,QAAQ,MAAM;AACzD,QAAI,OAAO,QAAQ,EAAE;AACrB,WAAO,KAAK;AAAA,MACR,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,IAChB,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAbA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACqBO,SAAS,wBAAwB,cAAc,YAAY;AAC9D,MAAI,YAAY;AACZ,WAAO,gBAAgB,YAAY;AAAA,EACvC;AACA,SAAO,gBAAgB,YAAY;AACvC;AA3BA,IAAI,iBAWA;AAXJ;AAAA;AAAA,IAAI,kBAAkB;AAAA,MAClB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA,IACd;AACA,IAAI,kBAAkB;AAAA,MAClB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA,IACd;AAAA;AAAA;;;ACfO,SAAS,4BAA4B,eAAe,MAAM,aAAa,aAAa,UAAU;AACjG,MAAI,OAAO,gBAAgB,MAAM,wBAAwB,GAAG,IAAI,CAAC;AACjE,MAAI,OAAO,KAAK;AAChB,MAAI,OAAO,KAAK;AAChB,MAAI,OAAO,KAAK;AAChB,MAAI,OAAO,KAAK;AAChB,gBAAc,uBAAuB;AACrC,MAAI,mBAAmB,UAAU,MAAM,oBAAoB,UAAU,CAAC,QAAQ,iBAAiB,eAAe,GAAG,MAAM;AACvH,MAAI,QAAQ,SAAS,UAAa,SAAS;AAC3C,MAAI,QAAQ,SAAS,UAAa,SAAS;AAC3C,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,qBAAqB,QAAQ;AAC7B,aAAS;AACT,QAAI,SAAU,CAAC,SAAS,aAAa,WAAY;AAC7C,eAAS;AAAA,IACb;AAAA,EACJ;AACA,MAAI,QAAQ;AACR,QAAI,OAAO;AACP,aAAO,oBAAoB,MAAM,GAAG,IAAI,CAAC;AACzC,aAAO,oBAAoB,MAAM,MAAM,IAAI,EAAE;AAC7C,oBAAc,2BAA2B;AACzC,oBAAc,2BAA2B;AAAA,IAC7C,OACK;AACD,oBAAc,2BAA2B;AACzC,oBAAc,2BAA2B;AAAA,IAC7C;AAAA,EACJ;AACA,MAAI,QAAQ;AACR,QAAI,OAAO;AACP,aAAO,oBAAoB,MAAM,GAAG,IAAI,MAAS;AACjD,aAAO,oBAAoB,MAAM,GAAG,IAAI,MAAS;AACjD,UAAI,SAAS,QAAW;AAEpB,eAAO,KAAK,IAAI,aAAa,IAAI;AAAA,MACrC,WACS,SAAS,QAAW;AACzB,eAAO,KAAK,IAAI,aAAa,IAAI;AAAA,MACrC,WACS,OAAO,MAAM;AAClB,cAAM,IAAI,WAAW,kBAAkB,OAAO,MAAM,KAAK,EAAE,OAAO,IAAI,CAAC;AAAA,MAC3E;AACA,oBAAc,wBAAwB;AACtC,oBAAc,wBAAwB;AAAA,IAC1C,OACK;AACD,oBAAc,wBAAwB;AACtC,oBAAc,wBAAwB;AAAA,IAC1C;AAAA,EACJ;AACA,MAAI,UAAU,QAAQ;AAClB,QAAI,qBAAqB,iBAAiB;AACtC,oBAAc,eAAe;AAAA,IACjC,WACS,qBAAqB,iBAAiB;AAC3C,oBAAc,eAAe;AAAA,IACjC,WACS,OAAO;AACZ,oBAAc,eAAe;AAAA,IACjC,OACK;AACD,oBAAc,eAAe;AAAA,IACjC;AAAA,EACJ,OACK;AACD,kBAAc,eAAe;AAC7B,kBAAc,wBAAwB;AACtC,kBAAc,wBAAwB;AACtC,kBAAc,2BAA2B;AACzC,kBAAc,2BAA2B;AAAA,EAC7C;AACJ;AA/EA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACIO,SAAS,2BAA2B,IAAI,SAAS,IAAI;AACxD,MAAI,YAAY,QAAQ;AAAE,cAAU,uBAAO,OAAO,IAAI;AAAA,EAAG;AACzD,MAAI,mBAAmB,GAAG;AAC1B,MAAI,gBAAgB,iBAAiB,EAAE;AACvC,MAAI,QAAQ,UAAU,SAAS,SAAS,UAAU,CAAC,WAAW,WAAW,YAAY,MAAM,GAAG,SAAS;AACvG,gBAAc,QAAQ;AACtB,MAAI,WAAW,UAAU,SAAS,YAAY,UAAU,QAAW,MAAS;AAC5E,MAAI,aAAa,UAAa,CAAC,yBAAyB,QAAQ,GAAG;AAC/D,UAAM,WAAW,yBAAyB;AAAA,EAC9C;AACA,MAAI,UAAU,cAAc,aAAa,QAAW;AAChD,UAAM,UAAU,8BAA8B;AAAA,EAClD;AACA,MAAI,kBAAkB,UAAU,SAAS,mBAAmB,UAAU,CAAC,QAAQ,UAAU,gBAAgB,MAAM,GAAG,QAAQ;AAC1H,MAAI,eAAe,UAAU,SAAS,gBAAgB,UAAU,CAAC,YAAY,YAAY,GAAG,UAAU;AACtG,MAAI,OAAO,UAAU,SAAS,QAAQ,UAAU,QAAW,MAAS;AACpE,MAAI,SAAS,UAAa,CAAC,2BAA2B,IAAI,GAAG;AACzD,UAAM,WAAW,+CAA+C;AAAA,EACpE;AACA,MAAI,UAAU,UAAU,SAAS,QAAW;AACxC,UAAM,UAAU,0BAA0B;AAAA,EAC9C;AACA,MAAI,cAAc,UAAU,SAAS,eAAe,UAAU,CAAC,SAAS,UAAU,MAAM,GAAG,OAAO;AAClG,MAAI,UAAU,YAAY;AACtB,kBAAc,WAAW,SAAS,YAAY;AAC9C,kBAAc,kBAAkB;AAChC,kBAAc,eAAe;AAAA,EACjC;AACA,MAAI,UAAU,QAAQ;AAClB,kBAAc,OAAO;AACrB,kBAAc,cAAc;AAAA,EAChC;AACJ;AAtCA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACcO,SAAS,uBAAuB,IAAI,SAAS,MAAM,IAAI;AAC1D,MAAI,mBAAmB,GAAG,kBAAkB,aAAa,GAAG,YAAY,mBAAmB,GAAG,kBAAkB,uBAAuB,GAAG,sBAAsB,mBAAmB,GAAG,kBAAkB,qBAAqB,GAAG;AAEhO,MAAI,mBAAmB,uBAAuB,OAAO;AACrD,MAAI,UAAU,sBAAsB,IAAI;AACxC,MAAI,MAAM,uBAAO,OAAO,IAAI;AAC5B,MAAI,UAAU,UAAU,SAAS,iBAAiB,UAAU,CAAC,UAAU,UAAU,GAAG,UAAU;AAC9F,MAAI,gBAAgB;AACpB,MAAI,kBAAkB,UAAU,SAAS,mBAAmB,UAAU,QAAW,MAAS;AAC1F,MAAI,oBAAoB,UACpB,qBAAqB,QAAQ,eAAe,IAAI,GAAG;AAGnD,UAAM,WAAW,6BAA6B,OAAO,eAAe,CAAC;AAAA,EACzE;AACA,MAAI,KAAK;AACT,MAAI,IAAI;AAAA,IAAc,MAAM,KAAK,gBAAgB;AAAA,IAAG;AAAA,IAAkB;AAAA;AAAA,IAEtE,CAAC,IAAI;AAAA,IAAG;AAAA,IAAY;AAAA,EAAgB;AACpC,MAAI,iBAAiB,WAAW,EAAE,UAAU;AAC5C,YAAU,CAAC,CAAC,gBAAgB,2BAA2B,OAAO,EAAE,UAAU,CAAC;AAC3E,MAAI,gBAAgB,iBAAiB,EAAE;AACvC,gBAAc,SAAS,EAAE;AACzB,gBAAc,aAAa,EAAE;AAC7B,gBAAc,kBAAkB,EAAE;AAClC,gBAAc,iBAAiB;AAC/B,6BAA2B,IAAI,SAAS,EAAE,iBAAmC,CAAC;AAC9E,MAAI,QAAQ,cAAc;AAC1B,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,YAAY;AACtB,QAAI,WAAW,cAAc;AAC7B,QAAI,UAAU,eAAe,UAAU,EAAE,mBAAuC,CAAC;AACjF,kBAAc;AACd,kBAAc;AAAA,EAClB,OACK;AACD,kBAAc;AACd,kBAAc,UAAU,YAAY,IAAI;AAAA,EAC5C;AACA,MAAI,WAAW,UAAU,SAAS,YAAY,UAAU,CAAC,YAAY,cAAc,eAAe,SAAS,GAAG,UAAU;AACxH,gBAAc,WAAW;AACzB,8BAA4B,eAAe,SAAS,aAAa,aAAa,QAAQ;AACtF,MAAI,oBAAoB,gBAAgB,SAAS,qBAAqB,GAAG,KAAM,CAAC;AAChF,MAAI,6BAA6B,QAAQ,iBAAiB,MAAM,IAAI;AAChE,UAAM,IAAI,WAAW,qCAAqC,OAAO,mBAAmB,sBAAsB,EAAE,OAAO,8BAA8B,GAAG,CAAC;AAAA,EACzJ;AACA,MAAI,sBAAsB,KACtB,cAAc,iBAAiB,kBAAkB;AACjD,UAAM,IAAI,UAAU,uEAAuE;AAAA,EAC/F;AACA,MAAI,sBAAsB,KACtB,cAAc,0BAA0B,cAAc,uBAAuB;AAC7E,UAAM,IAAI,WAAW,4FAA4F;AAAA,EACrH;AACA,gBAAc,oBAAoB;AAClC,MAAI,sBAAsB,UAAU,SAAS,uBAAuB,UAAU,CAAC,QAAQ,gBAAgB,GAAG,MAAM;AAChH,gBAAc,sBAAsB;AACpC,MAAI,iBAAiB,UAAU,SAAS,kBAAkB,UAAU,CAAC,SAAS,MAAM,GAAG,OAAO;AAC9F,MAAI,qBAAqB;AACzB,MAAI,aAAa,WAAW;AACxB,kBAAc,iBAAiB;AAC/B,yBAAqB;AAAA,EACzB;AACA,gBAAc,cAAc,yBAAyB,SAAS,eAAe,CAAC,QAAQ,QAAQ,QAAQ,GAAG,UAAU,OAAO,kBAAkB;AAC5I,gBAAc,cAAc,UAAU,SAAS,eAAe,UAAU,CAAC,QAAQ,SAAS,UAAU,cAAc,UAAU,GAAG,MAAM;AACrI,gBAAc,eAAe,UAAU,SAAS,gBAAgB,UAAU;AAAA,IACtE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,GAAG,YAAY;AACf,SAAO;AACX;AA9FA,IAUI;AAVJ;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,+BAA+B;AAAA,MAC/B;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAI;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAM;AAAA,IACvD;AAAA;AAAA;;;ACPO,SAAS,iBAAiB,SAAS;AACtC,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,QAAQ,QAAQ,GAAG;AACpC,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,MAAI,SAAS,QAAQ;AACrB,SAAO,aAAa,QAAQ,UAAU,aAAa,IAAI;AACnD,eAAW,QAAQ,QAAQ,KAAK,UAAU;AAC1C,cAAU,WAAW,YAAY,mBAAmB,OAAO,OAAO,CAAC;AACnE,QAAI,aAAa,WAAW;AACxB,aAAO,KAAK;AAAA,QACR,MAAM;AAAA,QACN,OAAO,QAAQ,UAAU,WAAW,UAAU;AAAA,MAClD,CAAC;AAAA,IACL;AACA,WAAO,KAAK;AAAA,MACR,MAAM,QAAQ,UAAU,aAAa,GAAG,QAAQ;AAAA,MAChD,OAAO;AAAA,IACX,CAAC;AACD,gBAAY,WAAW;AACvB,iBAAa,QAAQ,QAAQ,KAAK,SAAS;AAAA,EAC/C;AACA,MAAI,YAAY,QAAQ;AACpB,WAAO,KAAK;AAAA,MACR,MAAM;AAAA,MACN,OAAO,QAAQ,UAAU,WAAW,MAAM;AAAA,IAC9C,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAlCA;AAAA;AAAA;AAAA;AAAA;;;ACSO,SAAS,iBAAiB,kBAAkB,kBAAkB,SAAS;AAC1E,MAAI,UAAU;AACd,MAAI,YAAY,QAAW;AACvB,cAAU,SAAS,OAAO;AAC1B,cAAU,UAAU,SAAS,iBAAiB,UAAU,CAAC,UAAU,UAAU,GAAG,UAAU;AAAA,EAC9F;AACA,MAAI,YAAY,YAAY;AACxB,WAAO,uBAAuB,MAAM,KAAK,gBAAgB,GAAG,gBAAgB;AAAA,EAChF;AACA,SAAO,uBAAuB,MAAM,KAAK,gBAAgB,GAAG,gBAAgB;AAChF;AAnBA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACQO,SAAS,yBAAyB,GAAG;AACxC,SAAO,EAAE,SAAS;AACtB;AAZA,IACI;AADJ;AAAA;AAAA;AACA,IAAI;AAAA,IAAwC,SAAU,QAAQ;AAC1D,gBAAUC,yBAAwB,MAAM;AACxC,eAASA,0BAAyB;AAC9B,YAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,cAAM,OAAO;AACb,eAAO;AAAA,MACX;AACA,aAAOA;AAAA,IACX,EAAE,KAAK;AAAA;AAAA;;;ACTP,IAAW;AAAX;AAAA;AACA,KAAC,SAAUC,mBAAkB;AACzB,MAAAA,kBAAiB,YAAY,IAAI;AACjC,MAAAA,kBAAiB,QAAQ,IAAI;AAC7B,MAAAA,kBAAiB,UAAU,IAAI;AAAA,IACnC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAAA;AAAA;;;ACL9C;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,eAAA;AAAA,SAAAA,cAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC,YAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACzCA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,wBAAwB;AAChC,QAAI,qBAAqB;AACzB,QAAI,uBAAuB;AAC3B,aAAS,sBAAsB,IAAI,SAAS,SAAS,IAAI;AACrD,UAAI,mBAAmB,GAAG,kBAAkB,wBAAwB,GAAG,uBAAuB,aAAa,GAAG,YAAY,mBAAmB,GAAG,kBAAkB,mBAAmB,GAAG;AACxL,UAAI,oBAAoB,GAAG,mBAAmB,wBAAwB,OAAO;AAC7E,UAAI,MAAM,uBAAO,OAAO,IAAI;AAC5B,UAAI,QAAQ,GAAG,mBAAmB,uBAAuB,OAAO;AAChE,UAAI,gBAAgB,iBAAiB,EAAE;AACvC,oBAAc,yBAAyB;AACvC,UAAI,WAAW,GAAG,mBAAmB,WAAW,MAAM,iBAAiB,UAAU,CAAC,YAAY,QAAQ,GAAG,UAAU;AACnH,UAAI,gBAAgB;AACpB,oBAAc,QAAQ,GAAG,mBAAmB,WAAW,MAAM,QAAQ,UAAU,CAAC,YAAY,SAAS,GAAG,UAAU;AAClH,OAAC,GAAG,mBAAmB,6BAA6B,eAAe,MAAM,GAAG,GAAG,UAAU;AACzF,UAAI,KAAK,GAAG,qBAAqB,eAAe,kBAAkB,kBAAkB,KAAK,uBAAuB,YAAY,gBAAgB;AAC5I,oBAAc,SAAS,EAAE;AACzB,aAAO;AAAA,IACX;AACA,YAAQ,wBAAwB;AAAA;AAAA;;;ACpBhC;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,cAAc;AACtB,QAAI,qBAAqB;AAKzB,aAAS,YAAY,GAAG;AACpB,OAAC,GAAG,mBAAmB,WAAW,OAAO,MAAM,UAAU,mDAAmD;AAC5G,UAAI,KAAK,GAAG,mBAAmB,UAAU,CAAC;AAC1C,OAAC,GAAG,mBAAmB,WAAW,SAAS,CAAC,GAAG,oBAAoB;AACnE,UAAI,KAAK,EAAE,QAAQ,GAAG;AACtB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK;AACT,UAAI,OAAO,IAAI;AACX,aAAK;AACL,YAAI;AACJ,YAAI;AAAA,MACR,OACK;AACD,aAAK,EAAE,MAAM,GAAG,EAAE;AAClB,aAAK,EAAE,MAAM,IAAI,EAAE,MAAM;AACzB,aAAK,GAAG,mBAAmB,UAAU,EAAE;AACvC,YAAI,GAAG;AAAA,MACX;AACA,UAAI,IAAI,KAAK,KAAK,GAAG,mBAAmB,UAAU,EAAE,CAAC;AACrD,UAAI;AACJ,UAAI;AACJ,UAAI,MAAM,GAAG;AACT,YAAI,KAAK,GAAG,QAAQ,OAAO,EAAE;AAC7B,YAAI,GAAG;AACP,aAAK,GAAG,mBAAmB,UAAU,EAAE;AAAA,MAC3C,OACK;AACD,YAAI;AACJ,YAAI;AAAA,MACR;AACA,aAAO;AAAA,QACH,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uCAAuC;AAAA,QACvC,gBAAgB;AAAA,QAChB,+BAA+B;AAAA,MACnC;AAAA,IACJ;AACA,YAAQ,cAAc;AAAA;AAAA;;;ACjDtB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,gBAAgB;AACxB,QAAI,qBAAqB;AACzB,QAAI,gBAAgB;AAOpB,aAAS,cAAc,IAAI,GAAG,IAAI;AAC9B,UAAI,mBAAmB,GAAG,kBAAkB,mBAAmB,GAAG;AAClE,UAAI,gBAAgB,iBAAiB,EAAE;AACvC,OAAC,GAAG,mBAAmB,YAAY,GAAG,mBAAmB,MAAM,aAAa,MAAM,UAAU,wBAAwB;AACpH,OAAC,GAAG,mBAAmB,WAAW,4BAA4B,eAAe,iCAAiC;AAC9G,OAAC,GAAG,mBAAmB,YAAY,GAAG,mBAAmB,MAAM,CAAC,MAAM,UAAU,oBAAoB;AACpG,UAAI,CAAC,SAAS,CAAC,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,SAAS,cAAc,QAAQ,OAAO,cAAc;AACxD,UAAI,OAAO,GAAG,mBAAmB,uBAAuB,eAAe,CAAC;AACxE,UAAI,IAAI,IAAI;AACZ,UAAI,YAAY,GAAG,cAAc,aAAa,CAAC;AAC/C,aAAO,iBAAiB,QAAQ,MAAM,GAAG,QAAQ;AAAA,IACrD;AACA,YAAQ,gBAAgB;AAAA;AAAA;;;AC1BxB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,QAAI,kBAAkB,oBAAI,QAAQ;AAClC,aAAS,iBAAiB,GAAG;AACzB,UAAI,gBAAgB,gBAAgB,IAAI,CAAC;AACzC,UAAI,CAAC,eAAe;AAChB,wBAAgB,uBAAO,OAAO,IAAI;AAClC,wBAAgB,IAAI,GAAG,aAAa;AAAA,MACxC;AACA,aAAO;AAAA,IACX;AACA,YAAQ,UAAU;AAAA;AAAA;;;ACXlB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,cAAc;AACtB,QAAI,UAAU;AACd,QAAI,qBAAqB;AACzB,QAAI,0BAA0B;AAC9B,QAAI,kBAAkB;AACtB,QAAI,uBAAuB,QAAQ,gBAAgB,4BAA+B;AAClF,aAAS,iBAAiB,UAAU,QAAQ;AACxC,UAAI,EAAE,oBAAoB,cAAc;AACpC,cAAM,IAAI,UAAU,qCAAqC,OAAO,QAAQ,mCAAmC,EAAE,OAAO,OAAO,QAAQ,CAAC,CAAC;AAAA,MACzI;AAAA,IACJ;AAQA,aAAS,iBAAiB,QAAQ,MAAM,IAAI,IAAI;AAC5C,UAAI,gBAAgB,GAAG,eAAe,yBAAyB,GAAG,wBAAwB,iBAAiB,GAAG;AAC9G,aAAO,YAAY,WAAW,MAAM,EAAE,GAAG,yBACnC,GAAG,OAAO,eAAe,GAAG,EAAE,OAAO,cAAc,IACnD,eAAe,SAAS,SAAS;AAAA,IAC3C;AACA,QAAI;AAAA;AAAA,MAA6B,WAAY;AACzC,iBAASC,aAAY,SAAS,SAAS;AAGnC,cAAI,YAAY,QAAQ,gBAAgBA,eAAc,KAAK,cAAc;AACzE,cAAI,CAAC,WAAW;AACZ,kBAAM,IAAI,UAAU,4CAA4C;AAAA,UACpE;AACA,kBAAQ,GAAG,wBAAwB,uBAAuB,MAAM,SAAS,SAAS;AAAA,YAC9E,kBAAkBA,aAAY;AAAA,YAC9B,uBAAuBA,aAAY;AAAA,YACnC,YAAYA,aAAY;AAAA,YACxB,kBAAkBA,aAAY;AAAA,YAC9B,kBAAkB,qBAAqB;AAAA,UAC3C,CAAC;AAAA,QACL;AACA,QAAAA,aAAY,UAAU,kBAAkB,WAAY;AAChD,2BAAiB,MAAM,iBAAiB;AACxC,cAAI,OAAO,uBAAO,OAAO,IAAI;AAC7B,cAAI,iBAAiB,GAAG,qBAAqB,SAAS,IAAI;AAC1D,eAAK,SAAS,cAAc;AAC5B,eAAK,OAAO,cAAc;AAC1B;AAAA,YACI;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACJ,EAAE,QAAQ,SAAU,OAAO;AACvB,gBAAI,MAAM,cAAc,KAAK;AAC7B,gBAAI,QAAQ,QAAW;AACnB,mBAAK,KAAK,IAAI;AAAA,YAClB;AAAA,UACJ,CAAC;AACD,eAAK,mBAAmB,QAAQ,cAAc,CAAC,GAAGA,aAAY,WAAW,KAAK,MAAM,EAAE,WAAW,KAAK,IAAI,GAAG,IAAI;AACjH,iBAAO;AAAA,QACX;AACA,QAAAA,aAAY,UAAU,SAAS,SAAU,KAAK;AAC1C,cAAI,KAAK;AACT,2BAAiB,IAAI,QAAQ;AAC7B,cAAI,KAAK,GAAG,mBAAmB,UAAU,GAAG;AAC5C,kBAAQ,GAAG,gBAAgB,eAAe,IAAI,GAAG,EAAE,kBAAkB,qBAAqB,SAAS,iBAAmC,CAAC;AAAA,QAC3I;AACA,QAAAA,aAAY,UAAU,WAAW,WAAY;AACzC,iBAAO;AAAA,QACX;AACA,QAAAA,aAAY,qBAAqB,SAAU,SAAS,SAAS;AACzD,kBAAQ,GAAG,mBAAmB,kBAAkBA,aAAY,mBAAmB,GAAG,mBAAmB,wBAAwB,OAAO,GAAG,OAAO;AAAA,QAClJ;AACA,QAAAA,aAAY,kBAAkB,WAAY;AACtC,cAAI,OAAO,CAAC;AACZ,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAK,EAAE,IAAI,UAAU,EAAE;AAAA,UAC3B;AACA,mBAAS,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM;AACtD,gBAAI,KAAK,OAAO,EAAE,GAAG,IAAI,GAAG,MAAM,SAAS,GAAG;AAC9C,YAAAA,aAAY,WAAW,MAAM,IAAI;AACjC,YAAAA,aAAY,iBAAiB,IAAI,MAAM;AACvC,gBAAI,CAACA,aAAY,iBAAiB;AAC9B,cAAAA,aAAY,kBAAkB;AAAA,YAClC;AAAA,UACJ;AAAA,QACJ;AACA,QAAAA,aAAY,mBAAmB,WAAY;AACvC,iBAAOA,aAAY;AAAA,QACvB;AACA,QAAAA,aAAY,aAAa,CAAC;AAC1B,QAAAA,aAAY,mBAAmB,oBAAI,IAAI;AACvC,QAAAA,aAAY,kBAAkB;AAC9B,QAAAA,aAAY,wBAAwB,CAAC;AACrC,QAAAA,aAAY,aAAa;AACzB,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,cAAc;AACtB,QAAI;AAEA,UAAI,OAAO,WAAW,aAAa;AAC/B,eAAO,eAAe,YAAY,WAAW,OAAO,aAAa;AAAA,UAC7D,OAAO;AAAA,UACP,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AAAA,MACL;AACA,UAAI;AAEA,eAAO,eAAe,aAAa,UAAU;AAAA,UACzC,OAAO;AAAA,UACP,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AAAA,MACL,SACO,OAAO;AAAA,MAGd;AAEA,aAAO,eAAe,YAAY,UAAU,aAAa,UAAU;AAAA,QAC/D,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,MAClB,CAAC;AAED,aAAO,eAAe,YAAY,oBAAoB,UAAU;AAAA,QAC5D,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,MAClB,CAAC;AAAA,IACL,SACO,IAAI;AAAA,IAEX;AAAA;AAAA;;;AC5IA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,QAAI,KAAK;AACT,WAAO,eAAe,MAAM,eAAe;AAAA,MACvC,OAAO,GAAG;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AAAA;AAAA;", "names": ["x", "MissingLocaleDataError", "RangePatternType", "lib_exports", "init_lib", "PluralRules"]}