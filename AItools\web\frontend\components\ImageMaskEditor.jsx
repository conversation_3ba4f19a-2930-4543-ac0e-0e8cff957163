import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  <PERSON>,
  Stack,
  Button,
  RangeSlider,
  ButtonGroup,
  Text,
  Divider,
  Box
} from '@shopify/polaris';

const ImageMaskEditor = ({ 
  imageUrl, 
  onMaskChange, 
  initialMaskData = null,
  width = 600,
  height = 400 
}) => {
  const canvasRef = useRef(null);
  const overlayCanvasRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [brushSize, setBrushSize] = useState(40);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [maskPaths, setMaskPaths] = useState([]);
  const [currentPath, setCurrentPath] = useState([]);
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [originalImageSize, setOriginalImageSize] = useState({ width: 600, height: 400 });
  const [scaleRatio, setScaleRatio] = useState({ scaleX: 1, scaleY: 1 });
  const [imageDisplayInfo, setImageDisplayInfo] = useState({
    x: 0, y: 0, width: 600, height: 400, scale: 1
  });

  // 初始化Canvas
  useEffect(() => {
    if (!imageUrl) return;

    const canvas = canvasRef.current;
    const overlayCanvas = overlayCanvasRef.current;
    const ctx = canvas.getContext('2d');
    const overlayCtx = overlayCanvas.getContext('2d');

    // 设置Canvas尺寸
    canvas.width = width;
    canvas.height = height;
    overlayCanvas.width = width;
    overlayCanvas.height = height;

    // 加载图片
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      // 保存原始图片尺寸
      const originalSize = {
        width: img.naturalWidth,
        height: img.naturalHeight
      };
      setOriginalImageSize(originalSize);

      // 计算图片在canvas中的显示信息
      const scale = Math.min(width / img.width, height / img.height);
      const scaledWidth = img.width * scale;
      const scaledHeight = img.height * scale;
      const x = (width - scaledWidth) / 2;
      const y = (height - scaledHeight) / 2;

      // 保存图片显示信息
      setImageDisplayInfo({
        x, y,
        width: scaledWidth,
        height: scaledHeight,
        scale
      });

      // 计算坐标转换比例 (原始尺寸 / 显示在canvas中的尺寸)
      const scaleX = originalSize.width / scaledWidth;
      const scaleY = originalSize.height / scaledHeight;
      setScaleRatio({ scaleX, scaleY });

      // 清除Canvas并绘制图片
      ctx.clearRect(0, 0, width, height);
      ctx.drawImage(img, x, y, scaledWidth, scaledHeight);

      setImageLoaded(true);

      // 如果有初始蒙版数据，加载它
      if (initialMaskData) {
        // 直接在这里处理初始数据，避免循环依赖
        if (initialMaskData.paths) {
          // 检查是否是新格式（图片坐标），需要转换为canvas坐标进行显示
          let canvasCoordPaths;
          if (initialMaskData.metadata && initialMaskData.metadata.imageDisplayInfo) {
            // 新格式：从图片坐标转换为canvas坐标
            canvasCoordPaths = initialMaskData.paths.map(path =>
              path.map(point => ({
                x: point.x * scale + x,
                y: point.y * scale + y,
                size: point.size * scale  // 画笔大小也需要缩放
              }))
            );
          } else {
            // 旧格式：直接使用canvas坐标
            canvasCoordPaths = initialMaskData.paths;
          }

          setMaskPaths(canvasCoordPaths);
          // redrawMask会在后面定义，这里先设置数据
        }
      }
    };
    img.src = imageUrl;
  }, [imageUrl, width, height, initialMaskData]);

  // 重绘蒙版
  const redrawMask = useCallback((paths) => {
    const overlayCanvas = overlayCanvasRef.current;
    const overlayCtx = overlayCanvas.getContext('2d');

    overlayCtx.clearRect(0, 0, width, height);

    paths.forEach(path => {
      if (path.length < 2) return;

      overlayCtx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
      overlayCtx.lineWidth = path[0].size || brushSize;
      overlayCtx.lineCap = 'round';
      overlayCtx.lineJoin = 'round';

      overlayCtx.beginPath();
      overlayCtx.moveTo(path[0].x, path[0].y);

      for (let i = 1; i < path.length; i++) {
        overlayCtx.lineTo(path[i].x, path[i].y);
      }

      overlayCtx.stroke();
    });
  }, [width, height, brushSize]);

  // 获取鼠标/触摸位置，返回canvas坐标
  const getEventPos = useCallback((e) => {
    const canvas = overlayCanvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const clientX = e.clientX || (e.touches && e.touches[0].clientX);
    const clientY = e.clientY || (e.touches && e.touches[0].clientY);

    return {
      x: clientX - rect.left,
      y: clientY - rect.top
    };
  }, []);

  // 将canvas坐标转换为图片坐标
  const convertCanvasToImageCoords = useCallback((canvasPath) => {
    return canvasPath.map(point => ({
      x: (point.x - imageDisplayInfo.x) / imageDisplayInfo.scale,
      y: (point.y - imageDisplayInfo.y) / imageDisplayInfo.scale,
      size: point.size / imageDisplayInfo.scale  // 画笔大小也需要缩放
    }));
  }, [imageDisplayInfo]);

  // 将图片坐标转换为canvas坐标（用于显示已保存的路径）
  const convertImageToCanvasCoords = useCallback((imagePath) => {
    return imagePath.map(point => ({
      x: point.x * imageDisplayInfo.scale + imageDisplayInfo.x,
      y: point.y * imageDisplayInfo.scale + imageDisplayInfo.y,
      size: point.size * imageDisplayInfo.scale  // 画笔大小也需要缩放
    }));
  }, [imageDisplayInfo]);

  // 当maskPaths变化时重绘
  useEffect(() => {
    if (imageLoaded) {
      redrawMask(maskPaths);
    }
  }, [maskPaths, imageLoaded, redrawMask]);

  // 开始绘制
  const startDrawing = useCallback((e) => {
    if (!imageLoaded) return;
    
    setIsDrawing(true);
    const pos = getEventPos(e);
    const newPath = [{ x: pos.x, y: pos.y, size: brushSize }];
    setCurrentPath(newPath);
  }, [imageLoaded, getEventPos, brushSize]);

  // 绘制中
  const draw = useCallback((e) => {
    if (!isDrawing || !imageLoaded) return;
    
    const pos = getEventPos(e);
    const newPoint = { x: pos.x, y: pos.y, size: brushSize };
    
    setCurrentPath(prev => {
      const updated = [...prev, newPoint];
      
      // 实时绘制
      const overlayCanvas = overlayCanvasRef.current;
      const overlayCtx = overlayCanvas.getContext('2d');
      
      if (updated.length >= 2) {
        const prevPoint = updated[updated.length - 2];
        
        overlayCtx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
        overlayCtx.lineWidth = brushSize;
        overlayCtx.lineCap = 'round';
        overlayCtx.lineJoin = 'round';
        
        overlayCtx.beginPath();
        overlayCtx.moveTo(prevPoint.x, prevPoint.y);
        overlayCtx.lineTo(newPoint.x, newPoint.y);
        overlayCtx.stroke();
      }
      
      return updated;
    });
  }, [isDrawing, imageLoaded, getEventPos, brushSize]);

  // 结束绘制
  const stopDrawing = useCallback(() => {
    if (!isDrawing) return;
    
    setIsDrawing(false);
    
    if (currentPath.length > 0) {
      const newMaskPaths = [...maskPaths, currentPath];
      setMaskPaths(newMaskPaths);
      
      // 保存到历史记录
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push(newMaskPaths);
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
      
      // 通知父组件蒙版数据变化，转换为图片坐标
      if (onMaskChange) {
        const imageCoordPaths = newMaskPaths.map(path => convertCanvasToImageCoords(path));
        onMaskChange({
          paths: imageCoordPaths,
          timestamp: Date.now(),
          metadata: {
            originalSize: originalImageSize,
            displaySize: { width, height },
            imageDisplayInfo: imageDisplayInfo,
            scaleX: scaleRatio.scaleX,
            scaleY: scaleRatio.scaleY
          }
        });
      }
    }
    
    setCurrentPath([]);
  }, [isDrawing, currentPath, maskPaths, history, historyIndex, onMaskChange, originalImageSize, scaleRatio, imageDisplayInfo, width, height, convertCanvasToImageCoords]);

  // 清除蒙版
  const clearMask = useCallback(() => {
    setMaskPaths([]);
    setCurrentPath([]);
    
    const overlayCanvas = overlayCanvasRef.current;
    const overlayCtx = overlayCanvas.getContext('2d');
    overlayCtx.clearRect(0, 0, width, height);
    
    // 保存到历史记录
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push([]);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
    
    if (onMaskChange) {
      onMaskChange({
        paths: [], // 清空时不需要转换
        timestamp: Date.now(),
        metadata: {
          originalSize: originalImageSize,
          displaySize: { width, height },
          imageDisplayInfo: imageDisplayInfo,
          scaleX: scaleRatio.scaleX,
          scaleY: scaleRatio.scaleY
        }
      });
    }
  }, [width, height, history, historyIndex, onMaskChange, originalImageSize, scaleRatio, imageDisplayInfo]);

  // 撤销
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      const paths = history[newIndex];
      setMaskPaths(paths);
      setHistoryIndex(newIndex);
      redrawMask(paths);
      
      if (onMaskChange) {
        const imageCoordPaths = paths.map(path => convertCanvasToImageCoords(path));
        onMaskChange({
          paths: imageCoordPaths,
          timestamp: Date.now(),
          metadata: {
            originalSize: originalImageSize,
            displaySize: { width, height },
            imageDisplayInfo: imageDisplayInfo,
            scaleX: scaleRatio.scaleX,
            scaleY: scaleRatio.scaleY
          }
        });
      }
    }
  }, [history, historyIndex, redrawMask, onMaskChange, originalImageSize, scaleRatio, imageDisplayInfo, width, height, convertCanvasToImageCoords]);

  // 重做
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      const paths = history[newIndex];
      setMaskPaths(paths);
      setHistoryIndex(newIndex);
      redrawMask(paths);
      
      if (onMaskChange) {
        const imageCoordPaths = paths.map(path => convertCanvasToImageCoords(path));
        onMaskChange({
          paths: imageCoordPaths,
          timestamp: Date.now(),
          metadata: {
            originalSize: originalImageSize,
            displaySize: { width, height },
            imageDisplayInfo: imageDisplayInfo,
            scaleX: scaleRatio.scaleX,
            scaleY: scaleRatio.scaleY
          }
        });
      }
    }
  }, [history, historyIndex, redrawMask, onMaskChange, originalImageSize, scaleRatio, imageDisplayInfo, width, height, convertCanvasToImageCoords]);

  // 生成最终蒙版图像
  const generateMask = useCallback(() => {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    // 填充黑色背景
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, width, height);
    
    // 绘制白色蒙版区域
    ctx.strokeStyle = '#FFFFFF';
    ctx.fillStyle = '#FFFFFF';
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    maskPaths.forEach(path => {
      if (path.length < 2) return;
      
      ctx.lineWidth = path[0].size || brushSize;
      ctx.beginPath();
      ctx.moveTo(path[0].x, path[0].y);
      
      for (let i = 1; i < path.length; i++) {
        ctx.lineTo(path[i].x, path[i].y);
      }
      
      ctx.stroke();
    });
    
    return canvas.toDataURL('image/png');
  }, [maskPaths, width, height, brushSize]);

  return (
    
      <Stack vertical spacing="loose">
        <Text variant="headingMd">Image Mask Editor</Text>
        
        {/* 工具栏 */}
        <Stack alignment="center" spacing="loose">
          <Text variant="bodyMd">Brush Size:</Text>
          <div style={{ width: '200px' }}>
            <RangeSlider
              value={brushSize}
              min={5}
              max={50}
              onChange={setBrushSize}
            />
          </div>
          <Text variant="bodyMd">{brushSize}px</Text>
        </Stack>
        
        <Stack spacing="tight">
          <ButtonGroup>
            <Button
              onClick={undo}
              disabled={historyIndex <= 0}
            >
              Undo
            </Button>
            <Button
              onClick={redo}
              disabled={historyIndex >= history.length - 1}
            >
              Redo
            </Button>
            <Button onClick={clearMask} destructive>
              Clear
            </Button>
          </ButtonGroup>
        </Stack>
        
        <Divider />
        
        {/* Canvas容器 */}
        <div style={{ 
          position: 'relative', 
          width: `${width}px`, 
          height: `${height}px`,
          border: '1px solid #ddd',
          margin: '0 auto',
          cursor: isDrawing ? 'cell' : 'cell'
        }}>
          {/* 背景图片Canvas */}
          <canvas
            ref={canvasRef}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              zIndex: 1
            }}
          />
          
          {/* 蒙版绘制Canvas */}
          <canvas
            ref={overlayCanvasRef}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              zIndex: 2
            }}
            onMouseDown={startDrawing}
            onMouseMove={draw}
            onMouseUp={stopDrawing}
            onMouseLeave={stopDrawing}
            onTouchStart={startDrawing}
            onTouchMove={draw}
            onTouchEnd={stopDrawing}
          />
        </div>
        
        <Text variant="bodyMd" color="subdued">
          Paint red semi-transparent areas on the image to mark parts that need to be replaced
        </Text>
        <Divider />
        <Box></Box>
      </Stack>
    
  );
};

export default ImageMaskEditor;
