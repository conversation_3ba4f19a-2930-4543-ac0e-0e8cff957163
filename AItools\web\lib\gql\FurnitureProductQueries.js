// FurnitureProductQueries.js - GraphQL queries for Furniture AI product management

export class FurnitureProductQueries {
  
  // Query to get products with pagination and search
  static getProductsQuery() {
    return `
      query getProducts($first: Int, $after: String, $query: String) {
        products(first: $first, after: $after, query: $query) {
          nodes {
            id
            title
            handle
            status
            createdAt
            updatedAt
            onlineStoreUrl
            featuredImage {
              url
              altText
            }
            images(first: 5) {
              nodes {
                url
                altText
              }
            }
            variants(first: 1) {
              nodes {
                id
                price
              }
            }
            metafield(namespace: "Jaitools", key: "enabled") {
              value
            }
          }
          pageInfo {
            endCursor
            hasNextPage
          }
        }
      }
    `;
  }

  // Query to get products with pagination information (same as getProductsQuery but with edges structure)
  static getProductsWithPaginationQuery() {
    return `
      query getProducts($first: Int, $after: String, $query: String) {
        products(first: $first, after: $after, query: $query) {
          edges {
            node {
              id
              title
              handle
              status
              createdAt
              updatedAt
              onlineStoreUrl
              featuredImage {
                url
                altText
              }
              images(first: 5) {
                nodes {
                  url
                  altText
                }
              }
              variants(first: 1) {
                nodes {
                  id
                  price
                }
              }
              metafield(namespace: "Jaito<PERSON>", key: "enabled") {
                value
              }
            }
          }
          pageInfo {
            endCursor
            hasNextPage
            hasPreviousPage
            startCursor
          }
        }
      }
    `;
  }

  // Query to get a single product with full details
  static getProductQuery() {
    return `
      query getProduct($id: ID!) {
        product(id: $id) {
          id
          title
          handle
          status
          createdAt
          updatedAt
          onlineStoreUrl
          description
          featuredImage {
            url
            altText
          }
          images(first: 10) {
            nodes {
              url
              altText
            }
          }
          variants(first: 10) {
            nodes {
              id
              title
              price
              sku
              inventoryQuantity
            }
          }
          metafield(namespace: "Jaitools", key: "enabled") {
            id
            value
          }
        }
      }
    `;
  }

  // Query to get product metafield
  static getProductMetafieldQuery() {
    return `
      query getProductMetafield($id: ID!) {
        product(id: $id) {
          metafield(namespace: "Jaitools", key: "enabled") {
            id
            value
          }
        }
      }
    `;
  }

  // Query to check if app block is installed in theme
  static getThemeAppBlockQuery() {
    return `
      query getThemes {
        themes(first: 10) {
          nodes {
            id
            name
            role
            files(first: 100) {
              nodes {
                filename
                body
              }
            }
          }
        }
      }
    `;
  }

  // Mutation to create metafield
  static createMetafieldMutation() {
    return `
      mutation createMetafield($input: MetafieldInput!) {
        metafieldCreate(input: $input) {
          metafield {
            id
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `;
  }

  // Mutation to update metafield
  static updateMetafieldMutation() {
    return `
      mutation updateMetafield($input: MetafieldInput!) {
        metafieldUpdate(input: $input) {
          metafield {
            id
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `;
  }

  // Helper method to build search query string
  static buildSearchQuery(searchTerm, status = 'active') {
    let queryParts = [`status:${status}`];
    
    if (searchTerm && searchTerm.trim()) {
      // Search in title and handle
      queryParts.push(`(title:*${searchTerm.trim()}* OR handle:*${searchTerm.trim()}*)`);
    }
    
    return queryParts.join(' AND ');
  }

  // Helper method to convert GraphQL product to REST format
  static transformProductToRestFormat(product) {
    return {
      id: product.id.replace('gid://shopify/Product/', ''),
      title: product.title,
      handle: product.handle,
      status: product.status.toLowerCase(),
      created_at: product.createdAt,
      updated_at: product.updatedAt,
      image: product.featuredImage ? {
        src: product.featuredImage.url,
        alt: product.featuredImage.altText
      } : null,
      images: product.images?.nodes?.map(img => ({
        src: img.url,
        alt: img.altText
      })) || [],
      variants: product.variants?.nodes?.map(variant => ({
        id: variant.id.replace('gid://shopify/ProductVariant/', ''),
        title: variant.title,
        price: variant.price,
        sku: variant.sku,
        inventory_quantity: variant.inventoryQuantity
      })) || [],
      ar_enabled: product.metafield ? product.metafield.value === 'true' : false
    };
  }

  // Helper method to ensure product ID is in GraphQL format
  static ensureGraphQLProductId(productId) {
    return productId.startsWith('gid://shopify/Product/') 
      ? productId 
      : `gid://shopify/Product/${productId}`;
  }

  // Helper method to extract numeric ID from GraphQL ID
  static extractNumericId(gqlId) {
    return gqlId.replace('gid://shopify/Product/', '');
  }

  // Validate GraphQL response for errors
  static validateResponse(data, operation) {
    if (!data) {
      throw new Error(`${operation}: No data received from GraphQL`);
    }

    // Check for GraphQL errors in the response
    if (data.errors && data.errors.length > 0) {
      throw new Error(`${operation}: GraphQL errors - ${data.errors.map(e => e.message).join(', ')}`);
    }

    return true;
  }

  // Build variables for product queries
  static buildProductQueryVariables(page = 1, limit = 50, searchQuery = '') {
    const variables = {
      first: limit,
      after: null // For simplicity, starting from beginning each time
    };

    if (searchQuery && searchQuery.trim()) {
      variables.query = this.buildSearchQuery(searchQuery.trim());
    } else {
      variables.query = 'status:active';
    }

    return variables;
  }
}
