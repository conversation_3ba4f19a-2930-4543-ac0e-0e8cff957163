{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/locale-data/th.js"], "sourcesContent": ["/* @generated */\n// prettier-ignore\nif (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === 'function') {\n  Intl.PluralRules.__addLocaleData({\"data\":{\"categories\":{\"cardinal\":[\"other\"],\"ordinal\":[\"other\"]},\"fn\":function(n, ord) {\n  return 'other';\n}},\"locale\":\"th\"})\n}\n"], "mappings": ";AAEA,IAAI,KAAK,eAAe,OAAO,KAAK,YAAY,oBAAoB,YAAY;AAC9E,OAAK,YAAY,gBAAgB,EAAC,QAAO,EAAC,cAAa,EAAC,YAAW,CAAC,OAAO,GAAE,WAAU,CAAC,OAAO,EAAC,GAAE,MAAK,SAAS,GAAG,KAAK;AACxH,WAAO;AAAA,EACT,EAAC,GAAE,UAAS,KAAI,CAAC;AACjB;", "names": []}