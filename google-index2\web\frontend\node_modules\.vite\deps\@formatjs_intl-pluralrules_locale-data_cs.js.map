{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/locale-data/cs.js"], "sourcesContent": ["/* @generated */\n// prettier-ignore\nif (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === 'function') {\n  Intl.PluralRules.__addLocaleData({\"data\":{\"categories\":{\"cardinal\":[\"one\",\"few\",\"many\",\"other\"],\"ordinal\":[\"other\"]},\"fn\":function(n, ord) {\n  var s = String(n).split('.'), i = s[0], v0 = !s[1];\n  if (ord) return 'other';\n  return n == 1 && v0 ? 'one'\n    : (i >= 2 && i <= 4) && v0 ? 'few'\n    : !v0 ? 'many'\n    : 'other';\n}},\"locale\":\"cs\"})\n}\n"], "mappings": ";AAEA,IAAI,KAAK,eAAe,OAAO,KAAK,YAAY,oBAAoB,YAAY;AAC9E,OAAK,YAAY,gBAAgB,EAAC,QAAO,EAAC,cAAa,EAAC,YAAW,CAAC,OAAM,OAAM,QAAO,OAAO,GAAE,WAAU,CAAC,OAAO,EAAC,GAAE,MAAK,SAAS,GAAG,KAAK;AAC3I,QAAI,IAAI,OAAO,CAAC,EAAE,MAAM,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;AACjD,QAAI;AAAK,aAAO;AAChB,WAAO,KAAK,KAAK,KAAK,QACjB,KAAK,KAAK,KAAK,KAAM,KAAK,QAC3B,CAAC,KAAK,SACN;AAAA,EACN,EAAC,GAAE,UAAS,KAAI,CAAC;AACjB;", "names": []}