{"version": 3, "sources": ["../../@babel/runtime/helpers/esm/setPrototypeOf.js", "../../@babel/runtime/helpers/esm/inheritsLoose.js"], "sourcesContent": ["function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };"], "mappings": ";AAAA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUA,IAAGC,IAAG;AAC9F,WAAOD,GAAE,YAAYC,IAAGD;AAAA,EAC1B,GAAG,gBAAgB,GAAG,CAAC;AACzB;;;ACHA,SAAS,eAAe,GAAG,GAAG;AAC5B,IAAE,YAAY,OAAO,OAAO,EAAE,SAAS,GAAG,EAAE,UAAU,cAAc,GAAG,gBAAe,GAAG,CAAC;AAC5F;", "names": ["t", "e"]}