{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/locale-data/pl.js"], "sourcesContent": ["/* @generated */\n// prettier-ignore\nif (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === 'function') {\n  Intl.PluralRules.__addLocaleData({\"data\":{\"categories\":{\"cardinal\":[\"one\",\"few\",\"many\",\"other\"],\"ordinal\":[\"other\"]},\"fn\":function(n, ord) {\n  var s = String(n).split('.'), i = s[0], v0 = !s[1], i10 = i.slice(-1), i100 = i.slice(-2);\n  if (ord) return 'other';\n  return n == 1 && v0 ? 'one'\n    : v0 && (i10 >= 2 && i10 <= 4) && (i100 < 12 || i100 > 14) ? 'few'\n    : v0 && i != 1 && (i10 == 0 || i10 == 1) || v0 && (i10 >= 5 && i10 <= 9) || v0 && (i100 >= 12 && i100 <= 14) ? 'many'\n    : 'other';\n}},\"locale\":\"pl\"})\n}\n"], "mappings": ";AAEA,IAAI,KAAK,eAAe,OAAO,KAAK,YAAY,oBAAoB,YAAY;AAC9E,OAAK,YAAY,gBAAgB,EAAC,QAAO,EAAC,cAAa,EAAC,YAAW,CAAC,OAAM,OAAM,QAAO,OAAO,GAAE,WAAU,CAAC,OAAO,EAAC,GAAE,MAAK,SAAS,GAAG,KAAK;AAC3I,QAAI,IAAI,OAAO,CAAC,EAAE,MAAM,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE;AACxF,QAAI;AAAK,aAAO;AAChB,WAAO,KAAK,KAAK,KAAK,QAClB,OAAO,OAAO,KAAK,OAAO,OAAO,OAAO,MAAM,OAAO,MAAM,QAC3D,MAAM,KAAK,MAAM,OAAO,KAAK,OAAO,MAAM,OAAO,OAAO,KAAK,OAAO,MAAM,OAAO,QAAQ,MAAM,QAAQ,MAAM,SAC7G;AAAA,EACN,EAAC,GAAE,UAAS,KAAI,CAAC;AACjB;", "names": []}