{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/locale-data/da.js"], "sourcesContent": ["/* @generated */\n// prettier-ignore\nif (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === 'function') {\n  Intl.PluralRules.__addLocaleData({\"data\":{\"categories\":{\"cardinal\":[\"one\",\"other\"],\"ordinal\":[\"other\"]},\"fn\":function(n, ord) {\n  var s = String(n).split('.'), i = s[0], t0 = Number(s[0]) == n;\n  if (ord) return 'other';\n  return n == 1 || !t0 && (i == 0 || i == 1) ? 'one' : 'other';\n}},\"locale\":\"da\"})\n}\n"], "mappings": ";AAEA,IAAI,KAAK,eAAe,OAAO,KAAK,YAAY,oBAAoB,YAAY;AAC9E,OAAK,YAAY,gBAAgB,EAAC,QAAO,EAAC,cAAa,EAAC,YAAW,CAAC,OAAM,OAAO,GAAE,WAAU,CAAC,OAAO,EAAC,GAAE,MAAK,SAAS,GAAG,KAAK;AAC9H,QAAI,IAAI,OAAO,CAAC,EAAE,MAAM,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,OAAO,EAAE,CAAC,CAAC,KAAK;AAC7D,QAAI;AAAK,aAAO;AAChB,WAAO,KAAK,KAAK,CAAC,OAAO,KAAK,KAAK,KAAK,KAAK,QAAQ;AAAA,EACvD,EAAC,GAAE,UAAS,KAAI,CAAC;AACjB;", "names": []}