{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/node_modules/@formatjs/intl-localematcher/lib/abstract/CanonicalizeLocaleList.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/intl-localematcher/lib/abstract/languageMatching.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/intl-localematcher/lib/abstract/regions.generated.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/intl-localematcher/lib/abstract/utils.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/intl-localematcher/lib/abstract/BestFitMatcher.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/intl-localematcher/lib/abstract/BestAvailableLocale.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/intl-localematcher/lib/abstract/LookupMatcher.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/intl-localematcher/lib/abstract/UnicodeExtensionValue.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/intl-localematcher/lib/abstract/ResolveLocale.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/intl-localematcher/lib/abstract/LookupSupportedLocales.js", "../../@formatjs/intl-pluralrules/node_modules/@formatjs/intl-localematcher/lib/index.js"], "sourcesContent": ["/**\n * http://ecma-international.org/ecma-402/7.0/index.html#sec-canonicalizelocalelist\n * @param locales\n */\nexport function CanonicalizeLocaleList(locales) {\n    // TODO\n    return Intl.getCanonicalLocales(locales);\n}\n", "export var data = {\n    supplemental: {\n        languageMatching: {\n            'written-new': [\n                {\n                    paradigmLocales: {\n                        _locales: 'en en_GB es es_419 pt_BR pt_PT',\n                    },\n                },\n                {\n                    $enUS: {\n                        _value: 'AS+CA+GU+MH+MP+PH+PR+UM+US+VI',\n                    },\n                },\n                {\n                    $cnsar: {\n                        _value: 'HK+MO',\n                    },\n                },\n                {\n                    $americas: {\n                        _value: '019',\n                    },\n                },\n                {\n                    $maghreb: {\n                        _value: 'MA+DZ+TN+LY+MR+EH',\n                    },\n                },\n                {\n                    no: {\n                        _desired: 'nb',\n                        _distance: '1',\n                    },\n                },\n                {\n                    bs: {\n                        _desired: 'hr',\n                        _distance: '4',\n                    },\n                },\n                {\n                    bs: {\n                        _desired: 'sh',\n                        _distance: '4',\n                    },\n                },\n                {\n                    hr: {\n                        _desired: 'sh',\n                        _distance: '4',\n                    },\n                },\n                {\n                    sr: {\n                        _desired: 'sh',\n                        _distance: '4',\n                    },\n                },\n                {\n                    aa: {\n                        _desired: 'ssy',\n                        _distance: '4',\n                    },\n                },\n                {\n                    de: {\n                        _desired: 'gsw',\n                        _distance: '4',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    de: {\n                        _desired: 'lb',\n                        _distance: '4',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    no: {\n                        _desired: 'da',\n                        _distance: '8',\n                    },\n                },\n                {\n                    nb: {\n                        _desired: 'da',\n                        _distance: '8',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'ab',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ach',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    nl: {\n                        _desired: 'af',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ak',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'am',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'ay',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'az',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ur: {\n                        _desired: 'bal',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'be',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'bem',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'bh',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'bn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'bo',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'br',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'ca',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fil: {\n                        _desired: 'ceb',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'chr',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ckb',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'co',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'crs',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sk: {\n                        _desired: 'cs',\n                        _distance: '20',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'cy',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ee',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'eo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'eu',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    da: {\n                        _desired: 'fo',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    nl: {\n                        _desired: 'fy',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ga',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'gaa',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'gd',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'gl',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'gn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'gu',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ha',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'haw',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'ht',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'hy',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ia',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ig',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'is',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    id: {\n                        _desired: 'jv',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ka',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'kg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'kk',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'km',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'kn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'kri',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    tr: {\n                        _desired: 'ku',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'ky',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    it: {\n                        _desired: 'la',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'lg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'ln',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'lo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'loz',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'lua',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'mai',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'mfe',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'mg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'mi',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ml',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'mn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'mr',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    id: {\n                        _desired: 'ms',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'mt',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'my',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ne',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    nb: {\n                        _desired: 'nn',\n                        _distance: '20',\n                    },\n                },\n                {\n                    no: {\n                        _desired: 'nn',\n                        _distance: '20',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'nso',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ny',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'nyn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'oc',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'om',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'or',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'pa',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'pcm',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ps',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'qu',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    de: {\n                        _desired: 'rm',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'rn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'rw',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'sa',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sd',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'si',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'so',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sq',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'st',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    id: {\n                        _desired: 'su',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sw',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ta',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'te',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'tg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ti',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'tk',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'tlh',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'tn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'to',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'tt',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'tum',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'ug',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'uk',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ur',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'uz',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'wo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'xh',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'yi',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'yo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'za',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'zu',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'aao',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'abh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'abv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acx',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'adf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'aeb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'aec',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'afb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ajp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'apc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'apd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'arq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ars',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ary',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'arz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'auz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'avl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'bbz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'pga',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'shu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ssh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    az: {\n                        _desired: 'azb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    et: {\n                        _desired: 'vro',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'ffm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fub',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fue',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fui',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'gnw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'gui',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'gun',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'nhd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    iu: {\n                        _desired: 'ikt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'enb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'eyo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'niq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'oki',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'pko',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'sgc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'tec',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'tuy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kok: {\n                        _desired: 'gom',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kpe: {\n                        _desired: 'gkp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'ida',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lkb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lko',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lks',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lri',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lrm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lsm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lto',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lts',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lwg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'nle',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'nyd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'rag',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    lv: {\n                        _desired: 'ltg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bhr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bjq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bmm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bzc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'msh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'skg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'tdx',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'tkg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'txy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'xmv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'xmw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mn: {\n                        _desired: 'mvf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'bjn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'btj',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'bve',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'bvu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'coa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'dup',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'hji',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'id',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'jak',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'jax',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'kvb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'kvr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'kxd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'lce',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'lcf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'liw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'max',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'meo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mfa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mfb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'min',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mqg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'msi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mui',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'orn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'ors',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'pel',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'pse',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'tmw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'urk',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'vkk',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'vkt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'xmm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'zlm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'zmi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ne: {\n                        _desired: 'dty',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    om: {\n                        _desired: 'gax',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    om: {\n                        _desired: 'hae',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    om: {\n                        _desired: 'orc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    or: {\n                        _desired: 'spv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ps: {\n                        _desired: 'pbt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ps: {\n                        _desired: 'pst',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qub',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qud',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qug',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quk',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qul',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qup',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qur',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qus',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qux',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qva',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qve',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvj',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvs',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qwa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qwc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qwh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qws',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sc: {\n                        _desired: 'sdc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sc: {\n                        _desired: 'sdn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sc: {\n                        _desired: 'sro',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sq: {\n                        _desired: 'aae',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sq: {\n                        _desired: 'aat',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sq: {\n                        _desired: 'aln',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    syr: {\n                        _desired: 'aii',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    uz: {\n                        _desired: 'uzs',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    yi: {\n                        _desired: 'yih',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'cdo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'cjy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'cpx',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'czh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'czo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'gan',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'hak',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'hsn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'lzh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'mnp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'nan',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'wuu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'yue',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    '*': {\n                        _desired: '*',\n                        _distance: '80',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'am-Ethi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'az-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'bn-Beng',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'bo-Tibt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'hy-Armn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ka-Geor',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'km-Khmr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'kn-Knda',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'lo-Laoo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ml-Mlym',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'my-Mymr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ne-Deva',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'or-Orya',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'pa-Guru',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ps-Arab',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'sd-Arab',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'si-Sinh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ta-Taml',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'te-Telu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ti-Ethi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'tk-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ur-Arab',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'uz-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'yi-Hebr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'sr-Cyrl': {\n                        _desired: 'sr-Latn',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'za-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'zh-Hani',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hant': {\n                        _desired: 'zh-Hani',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ar-Arab': {\n                        _desired: 'ar-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'bn-Beng': {\n                        _desired: 'bn-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'gu-Gujr': {\n                        _desired: 'gu-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'hi-Deva': {\n                        _desired: 'hi-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'kn-Knda': {\n                        _desired: 'kn-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ml-Mlym': {\n                        _desired: 'ml-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'mr-Deva': {\n                        _desired: 'mr-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ta-Taml': {\n                        _desired: 'ta-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'te-Telu': {\n                        _desired: 'te-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'zh-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Latn',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Hani',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Hira',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Kana',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Hrkt',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Hrkt': {\n                        _desired: 'ja-Hira',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Hrkt': {\n                        _desired: 'ja-Kana',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Kore': {\n                        _desired: 'ko-Hani',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Kore': {\n                        _desired: 'ko-Hang',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Kore': {\n                        _desired: 'ko-Jamo',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Hang': {\n                        _desired: 'ko-Jamo',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    '*-*': {\n                        _desired: '*-*',\n                        _distance: '50',\n                    },\n                },\n                {\n                    'ar-*-$maghreb': {\n                        _desired: 'ar-*-$maghreb',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'ar-*-$!maghreb': {\n                        _desired: 'ar-*-$!maghreb',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'ar-*-*': {\n                        _desired: 'ar-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'en-*-$enUS': {\n                        _desired: 'en-*-$enUS',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'en-*-GB': {\n                        _desired: 'en-*-$!enUS',\n                        _distance: '3',\n                    },\n                },\n                {\n                    'en-*-$!enUS': {\n                        _desired: 'en-*-$!enUS',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'en-*-*': {\n                        _desired: 'en-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'es-*-$americas': {\n                        _desired: 'es-*-$americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'es-*-$!americas': {\n                        _desired: 'es-*-$!americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'es-*-*': {\n                        _desired: 'es-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'pt-*-$americas': {\n                        _desired: 'pt-*-$americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'pt-*-$!americas': {\n                        _desired: 'pt-*-$!americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'pt-*-*': {\n                        _desired: 'pt-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'zh-Hant-$cnsar': {\n                        _desired: 'zh-Hant-$cnsar',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'zh-Hant-$!cnsar': {\n                        _desired: 'zh-Hant-$!cnsar',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'zh-Hant-*': {\n                        _desired: 'zh-Hant-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    '*-*-*': {\n                        _desired: '*-*-*',\n                        _distance: '4',\n                    },\n                },\n            ],\n        },\n    },\n};\n", "// This file is generated from regions-gen.ts\nexport var regions = {\n    \"001\": [\n        \"001\",\n        \"001-status-grouping\",\n        \"002\",\n        \"005\",\n        \"009\",\n        \"011\",\n        \"013\",\n        \"014\",\n        \"015\",\n        \"017\",\n        \"018\",\n        \"019\",\n        \"021\",\n        \"029\",\n        \"030\",\n        \"034\",\n        \"035\",\n        \"039\",\n        \"053\",\n        \"054\",\n        \"057\",\n        \"061\",\n        \"142\",\n        \"143\",\n        \"145\",\n        \"150\",\n        \"151\",\n        \"154\",\n        \"155\",\n        \"AC\",\n        \"AD\",\n        \"AE\",\n        \"AF\",\n        \"AG\",\n        \"AI\",\n        \"AL\",\n        \"AM\",\n        \"AO\",\n        \"AQ\",\n        \"AR\",\n        \"AS\",\n        \"AT\",\n        \"AU\",\n        \"AW\",\n        \"AX\",\n        \"AZ\",\n        \"BA\",\n        \"BB\",\n        \"BD\",\n        \"BE\",\n        \"BF\",\n        \"BG\",\n        \"BH\",\n        \"BI\",\n        \"BJ\",\n        \"BL\",\n        \"BM\",\n        \"BN\",\n        \"BO\",\n        \"BQ\",\n        \"BR\",\n        \"BS\",\n        \"BT\",\n        \"BV\",\n        \"BW\",\n        \"BY\",\n        \"BZ\",\n        \"CA\",\n        \"CC\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CH\",\n        \"CI\",\n        \"CK\",\n        \"CL\",\n        \"CM\",\n        \"CN\",\n        \"CO\",\n        \"CP\",\n        \"CQ\",\n        \"CR\",\n        \"CU\",\n        \"CV\",\n        \"CW\",\n        \"CX\",\n        \"CY\",\n        \"CZ\",\n        \"DE\",\n        \"DG\",\n        \"DJ\",\n        \"DK\",\n        \"DM\",\n        \"DO\",\n        \"DZ\",\n        \"EA\",\n        \"EC\",\n        \"EE\",\n        \"EG\",\n        \"EH\",\n        \"ER\",\n        \"ES\",\n        \"ET\",\n        \"EU\",\n        \"EZ\",\n        \"FI\",\n        \"FJ\",\n        \"FK\",\n        \"FM\",\n        \"FO\",\n        \"FR\",\n        \"GA\",\n        \"GB\",\n        \"GD\",\n        \"GE\",\n        \"GF\",\n        \"GG\",\n        \"GH\",\n        \"GI\",\n        \"GL\",\n        \"GM\",\n        \"GN\",\n        \"GP\",\n        \"GQ\",\n        \"GR\",\n        \"GS\",\n        \"GT\",\n        \"GU\",\n        \"GW\",\n        \"GY\",\n        \"HK\",\n        \"HM\",\n        \"HN\",\n        \"HR\",\n        \"HT\",\n        \"HU\",\n        \"IC\",\n        \"ID\",\n        \"IE\",\n        \"IL\",\n        \"IM\",\n        \"IN\",\n        \"IO\",\n        \"IQ\",\n        \"IR\",\n        \"IS\",\n        \"IT\",\n        \"JE\",\n        \"JM\",\n        \"JO\",\n        \"JP\",\n        \"KE\",\n        \"KG\",\n        \"KH\",\n        \"KI\",\n        \"KM\",\n        \"KN\",\n        \"KP\",\n        \"KR\",\n        \"KW\",\n        \"KY\",\n        \"KZ\",\n        \"LA\",\n        \"LB\",\n        \"LC\",\n        \"LI\",\n        \"LK\",\n        \"LR\",\n        \"LS\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"LY\",\n        \"MA\",\n        \"MC\",\n        \"MD\",\n        \"ME\",\n        \"MF\",\n        \"MG\",\n        \"MH\",\n        \"MK\",\n        \"ML\",\n        \"MM\",\n        \"MN\",\n        \"MO\",\n        \"MP\",\n        \"MQ\",\n        \"MR\",\n        \"MS\",\n        \"MT\",\n        \"MU\",\n        \"MV\",\n        \"MW\",\n        \"MX\",\n        \"MY\",\n        \"MZ\",\n        \"NA\",\n        \"NC\",\n        \"NE\",\n        \"NF\",\n        \"NG\",\n        \"NI\",\n        \"NL\",\n        \"NO\",\n        \"NP\",\n        \"NR\",\n        \"NU\",\n        \"NZ\",\n        \"OM\",\n        \"PA\",\n        \"PE\",\n        \"PF\",\n        \"PG\",\n        \"PH\",\n        \"PK\",\n        \"PL\",\n        \"PM\",\n        \"PN\",\n        \"PR\",\n        \"PS\",\n        \"PT\",\n        \"PW\",\n        \"PY\",\n        \"QA\",\n        \"QO\",\n        \"RE\",\n        \"RO\",\n        \"RS\",\n        \"RU\",\n        \"RW\",\n        \"SA\",\n        \"SB\",\n        \"SC\",\n        \"SD\",\n        \"SE\",\n        \"SG\",\n        \"SH\",\n        \"SI\",\n        \"SJ\",\n        \"SK\",\n        \"SL\",\n        \"SM\",\n        \"SN\",\n        \"SO\",\n        \"SR\",\n        \"SS\",\n        \"ST\",\n        \"SV\",\n        \"SX\",\n        \"SY\",\n        \"SZ\",\n        \"TA\",\n        \"TC\",\n        \"TD\",\n        \"TF\",\n        \"TG\",\n        \"TH\",\n        \"TJ\",\n        \"TK\",\n        \"TL\",\n        \"TM\",\n        \"TN\",\n        \"TO\",\n        \"TR\",\n        \"TT\",\n        \"TV\",\n        \"TW\",\n        \"TZ\",\n        \"UA\",\n        \"UG\",\n        \"UM\",\n        \"UN\",\n        \"US\",\n        \"UY\",\n        \"UZ\",\n        \"VA\",\n        \"VC\",\n        \"VE\",\n        \"VG\",\n        \"VI\",\n        \"VN\",\n        \"VU\",\n        \"WF\",\n        \"WS\",\n        \"XK\",\n        \"YE\",\n        \"YT\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"002\": [\n        \"002\",\n        \"002-status-grouping\",\n        \"011\",\n        \"014\",\n        \"015\",\n        \"017\",\n        \"018\",\n        \"202\",\n        \"AO\",\n        \"BF\",\n        \"BI\",\n        \"BJ\",\n        \"BW\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CI\",\n        \"CM\",\n        \"CV\",\n        \"DJ\",\n        \"DZ\",\n        \"EA\",\n        \"EG\",\n        \"EH\",\n        \"ER\",\n        \"ET\",\n        \"GA\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GQ\",\n        \"GW\",\n        \"IC\",\n        \"IO\",\n        \"KE\",\n        \"KM\",\n        \"LR\",\n        \"LS\",\n        \"LY\",\n        \"MA\",\n        \"MG\",\n        \"ML\",\n        \"MR\",\n        \"MU\",\n        \"MW\",\n        \"MZ\",\n        \"NA\",\n        \"NE\",\n        \"NG\",\n        \"RE\",\n        \"RW\",\n        \"SC\",\n        \"SD\",\n        \"SH\",\n        \"SL\",\n        \"SN\",\n        \"SO\",\n        \"SS\",\n        \"ST\",\n        \"SZ\",\n        \"TD\",\n        \"TF\",\n        \"TG\",\n        \"TN\",\n        \"TZ\",\n        \"UG\",\n        \"YT\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"003\": [\n        \"003\",\n        \"013\",\n        \"021\",\n        \"029\",\n        \"AG\",\n        \"AI\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BM\",\n        \"BQ\",\n        \"BS\",\n        \"BZ\",\n        \"CA\",\n        \"CR\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"GD\",\n        \"GL\",\n        \"GP\",\n        \"GT\",\n        \"HN\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"PM\",\n        \"PR\",\n        \"SV\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"US\",\n        \"VC\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"005\": [\n        \"005\",\n        \"AR\",\n        \"BO\",\n        \"BR\",\n        \"BV\",\n        \"CL\",\n        \"CO\",\n        \"EC\",\n        \"FK\",\n        \"GF\",\n        \"GS\",\n        \"GY\",\n        \"PE\",\n        \"PY\",\n        \"SR\",\n        \"UY\",\n        \"VE\"\n    ],\n    \"009\": [\n        \"009\",\n        \"053\",\n        \"054\",\n        \"057\",\n        \"061\",\n        \"AC\",\n        \"AQ\",\n        \"AS\",\n        \"AU\",\n        \"CC\",\n        \"CK\",\n        \"CP\",\n        \"CX\",\n        \"DG\",\n        \"FJ\",\n        \"FM\",\n        \"GU\",\n        \"HM\",\n        \"KI\",\n        \"MH\",\n        \"MP\",\n        \"NC\",\n        \"NF\",\n        \"NR\",\n        \"NU\",\n        \"NZ\",\n        \"PF\",\n        \"PG\",\n        \"PN\",\n        \"PW\",\n        \"QO\",\n        \"SB\",\n        \"TA\",\n        \"TK\",\n        \"TO\",\n        \"TV\",\n        \"UM\",\n        \"VU\",\n        \"WF\",\n        \"WS\"\n    ],\n    \"011\": [\n        \"011\",\n        \"BF\",\n        \"BJ\",\n        \"CI\",\n        \"CV\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GW\",\n        \"LR\",\n        \"ML\",\n        \"MR\",\n        \"NE\",\n        \"NG\",\n        \"SH\",\n        \"SL\",\n        \"SN\",\n        \"TG\"\n    ],\n    \"013\": [\n        \"013\",\n        \"BZ\",\n        \"CR\",\n        \"GT\",\n        \"HN\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"SV\"\n    ],\n    \"014\": [\n        \"014\",\n        \"BI\",\n        \"DJ\",\n        \"ER\",\n        \"ET\",\n        \"IO\",\n        \"KE\",\n        \"KM\",\n        \"MG\",\n        \"MU\",\n        \"MW\",\n        \"MZ\",\n        \"RE\",\n        \"RW\",\n        \"SC\",\n        \"SO\",\n        \"SS\",\n        \"TF\",\n        \"TZ\",\n        \"UG\",\n        \"YT\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"015\": [\n        \"015\",\n        \"DZ\",\n        \"EA\",\n        \"EG\",\n        \"EH\",\n        \"IC\",\n        \"LY\",\n        \"MA\",\n        \"SD\",\n        \"TN\"\n    ],\n    \"017\": [\n        \"017\",\n        \"AO\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CM\",\n        \"GA\",\n        \"GQ\",\n        \"ST\",\n        \"TD\"\n    ],\n    \"018\": [\n        \"018\",\n        \"BW\",\n        \"LS\",\n        \"NA\",\n        \"SZ\",\n        \"ZA\"\n    ],\n    \"019\": [\n        \"003\",\n        \"005\",\n        \"013\",\n        \"019\",\n        \"019-status-grouping\",\n        \"021\",\n        \"029\",\n        \"419\",\n        \"AG\",\n        \"AI\",\n        \"AR\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BM\",\n        \"BO\",\n        \"BQ\",\n        \"BR\",\n        \"BS\",\n        \"BV\",\n        \"BZ\",\n        \"CA\",\n        \"CL\",\n        \"CO\",\n        \"CR\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"EC\",\n        \"FK\",\n        \"GD\",\n        \"GF\",\n        \"GL\",\n        \"GP\",\n        \"GS\",\n        \"GT\",\n        \"GY\",\n        \"HN\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"PE\",\n        \"PM\",\n        \"PR\",\n        \"PY\",\n        \"SR\",\n        \"SV\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"US\",\n        \"UY\",\n        \"VC\",\n        \"VE\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"021\": [\n        \"021\",\n        \"BM\",\n        \"CA\",\n        \"GL\",\n        \"PM\",\n        \"US\"\n    ],\n    \"029\": [\n        \"029\",\n        \"AG\",\n        \"AI\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BQ\",\n        \"BS\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"GD\",\n        \"GP\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"PR\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"VC\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"030\": [\n        \"030\",\n        \"CN\",\n        \"HK\",\n        \"JP\",\n        \"KP\",\n        \"KR\",\n        \"MN\",\n        \"MO\",\n        \"TW\"\n    ],\n    \"034\": [\n        \"034\",\n        \"AF\",\n        \"BD\",\n        \"BT\",\n        \"IN\",\n        \"IR\",\n        \"LK\",\n        \"MV\",\n        \"NP\",\n        \"PK\"\n    ],\n    \"035\": [\n        \"035\",\n        \"BN\",\n        \"ID\",\n        \"KH\",\n        \"LA\",\n        \"MM\",\n        \"MY\",\n        \"PH\",\n        \"SG\",\n        \"TH\",\n        \"TL\",\n        \"VN\"\n    ],\n    \"039\": [\n        \"039\",\n        \"AD\",\n        \"AL\",\n        \"BA\",\n        \"ES\",\n        \"GI\",\n        \"GR\",\n        \"HR\",\n        \"IT\",\n        \"ME\",\n        \"MK\",\n        \"MT\",\n        \"PT\",\n        \"RS\",\n        \"SI\",\n        \"SM\",\n        \"VA\",\n        \"XK\"\n    ],\n    \"053\": [\n        \"053\",\n        \"AU\",\n        \"CC\",\n        \"CX\",\n        \"HM\",\n        \"NF\",\n        \"NZ\"\n    ],\n    \"054\": [\n        \"054\",\n        \"FJ\",\n        \"NC\",\n        \"PG\",\n        \"SB\",\n        \"VU\"\n    ],\n    \"057\": [\n        \"057\",\n        \"FM\",\n        \"GU\",\n        \"KI\",\n        \"MH\",\n        \"MP\",\n        \"NR\",\n        \"PW\",\n        \"UM\"\n    ],\n    \"061\": [\n        \"061\",\n        \"AS\",\n        \"CK\",\n        \"NU\",\n        \"PF\",\n        \"PN\",\n        \"TK\",\n        \"TO\",\n        \"TV\",\n        \"WF\",\n        \"WS\"\n    ],\n    \"142\": [\n        \"030\",\n        \"034\",\n        \"035\",\n        \"142\",\n        \"143\",\n        \"145\",\n        \"AE\",\n        \"AF\",\n        \"AM\",\n        \"AZ\",\n        \"BD\",\n        \"BH\",\n        \"BN\",\n        \"BT\",\n        \"CN\",\n        \"CY\",\n        \"GE\",\n        \"HK\",\n        \"ID\",\n        \"IL\",\n        \"IN\",\n        \"IQ\",\n        \"IR\",\n        \"JO\",\n        \"JP\",\n        \"KG\",\n        \"KH\",\n        \"KP\",\n        \"KR\",\n        \"KW\",\n        \"KZ\",\n        \"LA\",\n        \"LB\",\n        \"LK\",\n        \"MM\",\n        \"MN\",\n        \"MO\",\n        \"MV\",\n        \"MY\",\n        \"NP\",\n        \"OM\",\n        \"PH\",\n        \"PK\",\n        \"PS\",\n        \"QA\",\n        \"SA\",\n        \"SG\",\n        \"SY\",\n        \"TH\",\n        \"TJ\",\n        \"TL\",\n        \"TM\",\n        \"TR\",\n        \"TW\",\n        \"UZ\",\n        \"VN\",\n        \"YE\"\n    ],\n    \"143\": [\n        \"143\",\n        \"KG\",\n        \"KZ\",\n        \"TJ\",\n        \"TM\",\n        \"UZ\"\n    ],\n    \"145\": [\n        \"145\",\n        \"AE\",\n        \"AM\",\n        \"AZ\",\n        \"BH\",\n        \"CY\",\n        \"GE\",\n        \"IL\",\n        \"IQ\",\n        \"JO\",\n        \"KW\",\n        \"LB\",\n        \"OM\",\n        \"PS\",\n        \"QA\",\n        \"SA\",\n        \"SY\",\n        \"TR\",\n        \"YE\"\n    ],\n    \"150\": [\n        \"039\",\n        \"150\",\n        \"151\",\n        \"154\",\n        \"155\",\n        \"AD\",\n        \"AL\",\n        \"AT\",\n        \"AX\",\n        \"BA\",\n        \"BE\",\n        \"BG\",\n        \"BY\",\n        \"CH\",\n        \"CQ\",\n        \"CZ\",\n        \"DE\",\n        \"DK\",\n        \"EE\",\n        \"ES\",\n        \"FI\",\n        \"FO\",\n        \"FR\",\n        \"GB\",\n        \"GG\",\n        \"GI\",\n        \"GR\",\n        \"HR\",\n        \"HU\",\n        \"IE\",\n        \"IM\",\n        \"IS\",\n        \"IT\",\n        \"JE\",\n        \"LI\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"MC\",\n        \"MD\",\n        \"ME\",\n        \"MK\",\n        \"MT\",\n        \"NL\",\n        \"NO\",\n        \"PL\",\n        \"PT\",\n        \"RO\",\n        \"RS\",\n        \"RU\",\n        \"SE\",\n        \"SI\",\n        \"SJ\",\n        \"SK\",\n        \"SM\",\n        \"UA\",\n        \"VA\",\n        \"XK\"\n    ],\n    \"151\": [\n        \"151\",\n        \"BG\",\n        \"BY\",\n        \"CZ\",\n        \"HU\",\n        \"MD\",\n        \"PL\",\n        \"RO\",\n        \"RU\",\n        \"SK\",\n        \"UA\"\n    ],\n    \"154\": [\n        \"154\",\n        \"AX\",\n        \"CQ\",\n        \"DK\",\n        \"EE\",\n        \"FI\",\n        \"FO\",\n        \"GB\",\n        \"GG\",\n        \"IE\",\n        \"IM\",\n        \"IS\",\n        \"JE\",\n        \"LT\",\n        \"LV\",\n        \"NO\",\n        \"SE\",\n        \"SJ\"\n    ],\n    \"155\": [\n        \"155\",\n        \"AT\",\n        \"BE\",\n        \"CH\",\n        \"DE\",\n        \"FR\",\n        \"LI\",\n        \"LU\",\n        \"MC\",\n        \"NL\"\n    ],\n    \"202\": [\n        \"011\",\n        \"014\",\n        \"017\",\n        \"018\",\n        \"202\",\n        \"AO\",\n        \"BF\",\n        \"BI\",\n        \"BJ\",\n        \"BW\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CI\",\n        \"CM\",\n        \"CV\",\n        \"DJ\",\n        \"ER\",\n        \"ET\",\n        \"GA\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GQ\",\n        \"GW\",\n        \"IO\",\n        \"KE\",\n        \"KM\",\n        \"LR\",\n        \"LS\",\n        \"MG\",\n        \"ML\",\n        \"MR\",\n        \"MU\",\n        \"MW\",\n        \"MZ\",\n        \"NA\",\n        \"NE\",\n        \"NG\",\n        \"RE\",\n        \"RW\",\n        \"SC\",\n        \"SH\",\n        \"SL\",\n        \"SN\",\n        \"SO\",\n        \"SS\",\n        \"ST\",\n        \"SZ\",\n        \"TD\",\n        \"TF\",\n        \"TG\",\n        \"TZ\",\n        \"UG\",\n        \"YT\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"419\": [\n        \"005\",\n        \"013\",\n        \"029\",\n        \"419\",\n        \"AG\",\n        \"AI\",\n        \"AR\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BO\",\n        \"BQ\",\n        \"BR\",\n        \"BS\",\n        \"BV\",\n        \"BZ\",\n        \"CL\",\n        \"CO\",\n        \"CR\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"EC\",\n        \"FK\",\n        \"GD\",\n        \"GF\",\n        \"GP\",\n        \"GS\",\n        \"GT\",\n        \"GY\",\n        \"HN\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"PE\",\n        \"PR\",\n        \"PY\",\n        \"SR\",\n        \"SV\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"UY\",\n        \"VC\",\n        \"VE\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"EU\": [\n        \"AT\",\n        \"BE\",\n        \"BG\",\n        \"CY\",\n        \"CZ\",\n        \"DE\",\n        \"DK\",\n        \"EE\",\n        \"ES\",\n        \"EU\",\n        \"FI\",\n        \"FR\",\n        \"GR\",\n        \"HR\",\n        \"HU\",\n        \"IE\",\n        \"IT\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"MT\",\n        \"NL\",\n        \"PL\",\n        \"PT\",\n        \"RO\",\n        \"SE\",\n        \"SI\",\n        \"SK\"\n    ],\n    \"EZ\": [\n        \"AT\",\n        \"BE\",\n        \"CY\",\n        \"DE\",\n        \"EE\",\n        \"ES\",\n        \"EZ\",\n        \"FI\",\n        \"FR\",\n        \"GR\",\n        \"IE\",\n        \"IT\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"MT\",\n        \"NL\",\n        \"PT\",\n        \"SI\",\n        \"SK\"\n    ],\n    \"QO\": [\n        \"AC\",\n        \"AQ\",\n        \"CP\",\n        \"DG\",\n        \"QO\",\n        \"TA\"\n    ],\n    \"UN\": [\n        \"AD\",\n        \"AE\",\n        \"AF\",\n        \"AG\",\n        \"AL\",\n        \"AM\",\n        \"AO\",\n        \"AR\",\n        \"AT\",\n        \"AU\",\n        \"AZ\",\n        \"BA\",\n        \"BB\",\n        \"BD\",\n        \"BE\",\n        \"BF\",\n        \"BG\",\n        \"BH\",\n        \"BI\",\n        \"BJ\",\n        \"BN\",\n        \"BO\",\n        \"BR\",\n        \"BS\",\n        \"BT\",\n        \"BW\",\n        \"BY\",\n        \"BZ\",\n        \"CA\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CH\",\n        \"CI\",\n        \"CL\",\n        \"CM\",\n        \"CN\",\n        \"CO\",\n        \"CR\",\n        \"CU\",\n        \"CV\",\n        \"CY\",\n        \"CZ\",\n        \"DE\",\n        \"DJ\",\n        \"DK\",\n        \"DM\",\n        \"DO\",\n        \"DZ\",\n        \"EC\",\n        \"EE\",\n        \"EG\",\n        \"ER\",\n        \"ES\",\n        \"ET\",\n        \"FI\",\n        \"FJ\",\n        \"FM\",\n        \"FR\",\n        \"GA\",\n        \"GB\",\n        \"GD\",\n        \"GE\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GQ\",\n        \"GR\",\n        \"GT\",\n        \"GW\",\n        \"GY\",\n        \"HN\",\n        \"HR\",\n        \"HT\",\n        \"HU\",\n        \"ID\",\n        \"IE\",\n        \"IL\",\n        \"IN\",\n        \"IQ\",\n        \"IR\",\n        \"IS\",\n        \"IT\",\n        \"JM\",\n        \"JO\",\n        \"JP\",\n        \"KE\",\n        \"KG\",\n        \"KH\",\n        \"KI\",\n        \"KM\",\n        \"KN\",\n        \"KP\",\n        \"KR\",\n        \"KW\",\n        \"KZ\",\n        \"LA\",\n        \"LB\",\n        \"LC\",\n        \"LI\",\n        \"LK\",\n        \"LR\",\n        \"LS\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"LY\",\n        \"MA\",\n        \"MC\",\n        \"MD\",\n        \"ME\",\n        \"MG\",\n        \"MH\",\n        \"MK\",\n        \"ML\",\n        \"MM\",\n        \"MN\",\n        \"MR\",\n        \"MT\",\n        \"MU\",\n        \"MV\",\n        \"MW\",\n        \"MX\",\n        \"MY\",\n        \"MZ\",\n        \"NA\",\n        \"NE\",\n        \"NG\",\n        \"NI\",\n        \"NL\",\n        \"NO\",\n        \"NP\",\n        \"NR\",\n        \"NZ\",\n        \"OM\",\n        \"PA\",\n        \"PE\",\n        \"PG\",\n        \"PH\",\n        \"PK\",\n        \"PL\",\n        \"PT\",\n        \"PW\",\n        \"PY\",\n        \"QA\",\n        \"RO\",\n        \"RS\",\n        \"RU\",\n        \"RW\",\n        \"SA\",\n        \"SB\",\n        \"SC\",\n        \"SD\",\n        \"SE\",\n        \"SG\",\n        \"SI\",\n        \"SK\",\n        \"SL\",\n        \"SM\",\n        \"SN\",\n        \"SO\",\n        \"SR\",\n        \"SS\",\n        \"ST\",\n        \"SV\",\n        \"SY\",\n        \"SZ\",\n        \"TD\",\n        \"TG\",\n        \"TH\",\n        \"TJ\",\n        \"TL\",\n        \"TM\",\n        \"TN\",\n        \"TO\",\n        \"TR\",\n        \"TT\",\n        \"TV\",\n        \"TZ\",\n        \"UA\",\n        \"UG\",\n        \"UN\",\n        \"US\",\n        \"UY\",\n        \"UZ\",\n        \"VC\",\n        \"VE\",\n        \"VN\",\n        \"VU\",\n        \"WS\",\n        \"YE\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ]\n};\n", "import { __spreadArray } from \"tslib\";\nimport { data as jsonData } from './languageMatching';\nimport { regions } from './regions.generated';\nexport var UNICODE_EXTENSION_SEQUENCE_REGEX = /-u(?:-[0-9a-z]{2,8})+/gi;\nexport function invariant(condition, message, Err) {\n    if (Err === void 0) { Err = Error; }\n    if (!condition) {\n        throw new Err(message);\n    }\n}\n// This is effectively 2 languages in 2 different regions in the same cluster\nvar DEFAULT_MATCHING_THRESHOLD = 838;\nvar PROCESSED_DATA;\nfunction processData() {\n    var _a, _b;\n    if (!PROCESSED_DATA) {\n        var paradigmLocales = (_b = (_a = jsonData.supplemental.languageMatching['written-new'][0]) === null || _a === void 0 ? void 0 : _a.paradigmLocales) === null || _b === void 0 ? void 0 : _b._locales.split(' ');\n        var matchVariables = jsonData.supplemental.languageMatching['written-new'].slice(1, 5);\n        var data = jsonData.supplemental.languageMatching['written-new'].slice(5);\n        var matches = data.map(function (d) {\n            var key = Object.keys(d)[0];\n            var value = d[key];\n            return {\n                supported: key,\n                desired: value._desired,\n                distance: +value._distance,\n                oneway: value.oneway === 'true' ? true : false,\n            };\n        }, {});\n        PROCESSED_DATA = {\n            matches: matches,\n            matchVariables: matchVariables.reduce(function (all, d) {\n                var key = Object.keys(d)[0];\n                var value = d[key];\n                all[key.slice(1)] = value._value.split('+');\n                return all;\n            }, {}),\n            paradigmLocales: __spreadArray(__spreadArray([], paradigmLocales, true), paradigmLocales.map(function (l) {\n                return new Intl.Locale(l.replace(/_/g, '-')).maximize().toString();\n            }), true),\n        };\n    }\n    return PROCESSED_DATA;\n}\nfunction isMatched(locale, languageMatchInfoLocale, matchVariables) {\n    var _a = languageMatchInfoLocale.split('-'), language = _a[0], script = _a[1], region = _a[2];\n    var matches = true;\n    if (region && region[0] === '$') {\n        var shouldInclude = region[1] !== '!';\n        var matchRegions = shouldInclude\n            ? matchVariables[region.slice(1)]\n            : matchVariables[region.slice(2)];\n        var expandedMatchedRegions = matchRegions\n            .map(function (r) { return regions[r] || [r]; })\n            .reduce(function (all, list) { return __spreadArray(__spreadArray([], all, true), list, true); }, []);\n        matches && (matches = !(expandedMatchedRegions.indexOf(locale.region || '') > 1 !=\n            shouldInclude));\n    }\n    else {\n        matches && (matches = locale.region\n            ? region === '*' || region === locale.region\n            : true);\n    }\n    matches && (matches = locale.script ? script === '*' || script === locale.script : true);\n    matches && (matches = locale.language\n        ? language === '*' || language === locale.language\n        : true);\n    return matches;\n}\nfunction serializeLSR(lsr) {\n    return [lsr.language, lsr.script, lsr.region].filter(Boolean).join('-');\n}\nfunction findMatchingDistanceForLSR(desired, supported, data) {\n    for (var _i = 0, _a = data.matches; _i < _a.length; _i++) {\n        var d = _a[_i];\n        var matches = isMatched(desired, d.desired, data.matchVariables) &&\n            isMatched(supported, d.supported, data.matchVariables);\n        if (!d.oneway && !matches) {\n            matches =\n                isMatched(desired, d.supported, data.matchVariables) &&\n                    isMatched(supported, d.desired, data.matchVariables);\n        }\n        if (matches) {\n            var distance = d.distance * 10;\n            if (data.paradigmLocales.indexOf(serializeLSR(desired)) > -1 !=\n                data.paradigmLocales.indexOf(serializeLSR(supported)) > -1) {\n                return distance - 1;\n            }\n            return distance;\n        }\n    }\n    throw new Error('No matching distance found');\n}\nexport function findMatchingDistance(desired, supported) {\n    var desiredLocale = new Intl.Locale(desired).maximize();\n    var supportedLocale = new Intl.Locale(supported).maximize();\n    var desiredLSR = {\n        language: desiredLocale.language,\n        script: desiredLocale.script || '',\n        region: desiredLocale.region || '',\n    };\n    var supportedLSR = {\n        language: supportedLocale.language,\n        script: supportedLocale.script || '',\n        region: supportedLocale.region || '',\n    };\n    var matchingDistance = 0;\n    var data = processData();\n    if (desiredLSR.language !== supportedLSR.language) {\n        matchingDistance += findMatchingDistanceForLSR({\n            language: desiredLocale.language,\n            script: '',\n            region: '',\n        }, {\n            language: supportedLocale.language,\n            script: '',\n            region: '',\n        }, data);\n    }\n    if (desiredLSR.script !== supportedLSR.script) {\n        matchingDistance += findMatchingDistanceForLSR({\n            language: desiredLocale.language,\n            script: desiredLSR.script,\n            region: '',\n        }, {\n            language: supportedLocale.language,\n            script: desiredLSR.script,\n            region: '',\n        }, data);\n    }\n    if (desiredLSR.region !== supportedLSR.region) {\n        matchingDistance += findMatchingDistanceForLSR(desiredLSR, supportedLSR, data);\n    }\n    return matchingDistance;\n}\nexport function findBestMatch(requestedLocales, supportedLocales, threshold) {\n    if (threshold === void 0) { threshold = DEFAULT_MATCHING_THRESHOLD; }\n    var lowestDistance = Infinity;\n    var result = {\n        matchedDesiredLocale: '',\n        distances: {},\n    };\n    requestedLocales.forEach(function (desired, i) {\n        if (!result.distances[desired]) {\n            result.distances[desired] = {};\n        }\n        supportedLocales.forEach(function (supported) {\n            // Add some weight to the distance based on the order of the supported locales\n            // Add penalty for the order of the requested locales, which currently is 0 since ECMA-402\n            // doesn't really have room for weighted locales like `en; q=0.1`\n            var distance = findMatchingDistance(desired, supported) + 0 + i * 40;\n            result.distances[desired][supported] = distance;\n            if (distance < lowestDistance) {\n                lowestDistance = distance;\n                result.matchedDesiredLocale = desired;\n                result.matchedSupportedLocale = supported;\n            }\n        });\n    });\n    if (lowestDistance >= threshold) {\n        result.matchedDesiredLocale = undefined;\n        result.matchedSupportedLocale = undefined;\n    }\n    return result;\n}\n", "import { UNICODE_EXTENSION_SEQUENCE_REGEX, findBestMatch } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-bestfitmatcher\n * @param availableLocales\n * @param requestedLocales\n * @param getDefaultLocale\n */\nexport function BestFitMatcher(availableLocales, requestedLocales, getDefaultLocale) {\n    var foundLocale;\n    var extension;\n    var noExtensionLocales = [];\n    var noExtensionLocaleMap = requestedLocales.reduce(function (all, l) {\n        var noExtensionLocale = l.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        noExtensionLocales.push(noExtensionLocale);\n        all[noExtensionLocale] = l;\n        return all;\n    }, {});\n    var result = findBestMatch(noExtensionLocales, availableLocales);\n    if (result.matchedSupportedLocale && result.matchedDesiredLocale) {\n        foundLocale = result.matchedSupportedLocale;\n        extension =\n            noExtensionLocaleMap[result.matchedDesiredLocale].slice(result.matchedDesiredLocale.length) || undefined;\n    }\n    if (!foundLocale) {\n        return { locale: getDefaultLocale() };\n    }\n    return {\n        locale: foundLocale,\n        extension: extension,\n    };\n}\n", "/**\n * https://tc39.es/ecma402/#sec-bestavailablelocale\n * @param availableLocales\n * @param locale\n */\nexport function BestAvailableLocale(availableLocales, locale) {\n    var candidate = locale;\n    while (true) {\n        if (availableLocales.indexOf(candidate) > -1) {\n            return candidate;\n        }\n        var pos = candidate.lastIndexOf('-');\n        if (!~pos) {\n            return undefined;\n        }\n        if (pos >= 2 && candidate[pos - 2] === '-') {\n            pos -= 2;\n        }\n        candidate = candidate.slice(0, pos);\n    }\n}\n", "import { BestAvailableLocale } from './BestAvailableLocale';\nimport { UNICODE_EXTENSION_SEQUENCE_REGEX } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-lookupmatcher\n * @param availableLocales\n * @param requestedLocales\n * @param getDefaultLocale\n */\nexport function LookupMatcher(availableLocales, requestedLocales, getDefaultLocale) {\n    var result = { locale: '' };\n    for (var _i = 0, requestedLocales_1 = requestedLocales; _i < requestedLocales_1.length; _i++) {\n        var locale = requestedLocales_1[_i];\n        var noExtensionLocale = locale.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        var availableLocale = BestAvailableLocale(availableLocales, noExtensionLocale);\n        if (availableLocale) {\n            result.locale = availableLocale;\n            if (locale !== noExtensionLocale) {\n                result.extension = locale.slice(noExtensionLocale.length, locale.length);\n            }\n            return result;\n        }\n    }\n    result.locale = getDefaultLocale();\n    return result;\n}\n", "import { invariant } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-unicodeextensionvalue\n * @param extension\n * @param key\n */\nexport function UnicodeExtensionValue(extension, key) {\n    invariant(key.length === 2, 'key must have 2 elements');\n    var size = extension.length;\n    var searchValue = \"-\".concat(key, \"-\");\n    var pos = extension.indexOf(searchValue);\n    if (pos !== -1) {\n        var start = pos + 4;\n        var end = start;\n        var k = start;\n        var done = false;\n        while (!done) {\n            var e = extension.indexOf('-', k);\n            var len = void 0;\n            if (e === -1) {\n                len = size - k;\n            }\n            else {\n                len = e - k;\n            }\n            if (len === 2) {\n                done = true;\n            }\n            else if (e === -1) {\n                end = size;\n                done = true;\n            }\n            else {\n                end = e;\n                k = e + 1;\n            }\n        }\n        return extension.slice(start, end);\n    }\n    searchValue = \"-\".concat(key);\n    pos = extension.indexOf(searchValue);\n    if (pos !== -1 && pos + 3 === size) {\n        return '';\n    }\n    return undefined;\n}\n", "import { BestFitMatcher } from './BestFitMatcher';\nimport { LookupMatcher } from './LookupMatcher';\nimport { UnicodeExtensionValue } from './UnicodeExtensionValue';\nimport { invariant } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-resolvelocale\n */\nexport function ResolveLocale(availableLocales, requestedLocales, options, relevantExtensionKeys, localeData, getDefaultLocale) {\n    var matcher = options.localeMatcher;\n    var r;\n    if (matcher === 'lookup') {\n        r = LookupMatcher(Array.from(availableLocales), requestedLocales, getDefaultLocale);\n    }\n    else {\n        r = BestFitMatcher(Array.from(availableLocales), requestedLocales, getDefaultLocale);\n    }\n    var foundLocale = r.locale;\n    var result = { locale: '', dataLocale: foundLocale };\n    var supportedExtension = '-u';\n    for (var _i = 0, relevantExtensionKeys_1 = relevantExtensionKeys; _i < relevantExtensionKeys_1.length; _i++) {\n        var key = relevantExtensionKeys_1[_i];\n        invariant(foundLocale in localeData, \"Missing locale data for \".concat(foundLocale));\n        var foundLocaleData = localeData[foundLocale];\n        invariant(typeof foundLocaleData === 'object' && foundLocaleData !== null, \"locale data \".concat(key, \" must be an object\"));\n        var keyLocaleData = foundLocaleData[key];\n        invariant(Array.isArray(keyLocaleData), \"keyLocaleData for \".concat(key, \" must be an array\"));\n        var value = keyLocaleData[0];\n        invariant(typeof value === 'string' || value === null, \"value must be string or null but got \".concat(typeof value, \" in key \").concat(key));\n        var supportedExtensionAddition = '';\n        if (r.extension) {\n            var requestedValue = UnicodeExtensionValue(r.extension, key);\n            if (requestedValue !== undefined) {\n                if (requestedValue !== '') {\n                    if (~keyLocaleData.indexOf(requestedValue)) {\n                        value = requestedValue;\n                        supportedExtensionAddition = \"-\".concat(key, \"-\").concat(value);\n                    }\n                }\n                else if (~requestedValue.indexOf('true')) {\n                    value = 'true';\n                    supportedExtensionAddition = \"-\".concat(key);\n                }\n            }\n        }\n        if (key in options) {\n            var optionsValue = options[key];\n            invariant(typeof optionsValue === 'string' ||\n                typeof optionsValue === 'undefined' ||\n                optionsValue === null, 'optionsValue must be String, Undefined or Null');\n            if (~keyLocaleData.indexOf(optionsValue)) {\n                if (optionsValue !== value) {\n                    value = optionsValue;\n                    supportedExtensionAddition = '';\n                }\n            }\n        }\n        result[key] = value;\n        supportedExtension += supportedExtensionAddition;\n    }\n    if (supportedExtension.length > 2) {\n        var privateIndex = foundLocale.indexOf('-x-');\n        if (privateIndex === -1) {\n            foundLocale = foundLocale + supportedExtension;\n        }\n        else {\n            var preExtension = foundLocale.slice(0, privateIndex);\n            var postExtension = foundLocale.slice(privateIndex, foundLocale.length);\n            foundLocale = preExtension + supportedExtension + postExtension;\n        }\n        foundLocale = Intl.getCanonicalLocales(foundLocale)[0];\n    }\n    result.locale = foundLocale;\n    return result;\n}\n", "import { BestAvailableLocale } from './BestAvailableLocale';\nimport { UNICODE_EXTENSION_SEQUENCE_REGEX } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-lookupsupportedlocales\n * @param availableLocales\n * @param requestedLocales\n */\nexport function LookupSupportedLocales(availableLocales, requestedLocales) {\n    var subset = [];\n    for (var _i = 0, requestedLocales_1 = requestedLocales; _i < requestedLocales_1.length; _i++) {\n        var locale = requestedLocales_1[_i];\n        var noExtensionLocale = locale.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        var availableLocale = BestAvailableLocale(availableLocales, noExtensionLocale);\n        if (availableLocale) {\n            subset.push(availableLocale);\n        }\n    }\n    return subset;\n}\n", "import { CanonicalizeLocaleList } from './abstract/CanonicalizeLocaleList';\nimport { ResolveLocale } from './abstract/ResolveLocale';\nexport function match(requestedLocales, availableLocales, defaultLocale, opts) {\n    return ResolveLocale(availableLocales, CanonicalizeLocaleList(requestedLocales), {\n        localeMatcher: (opts === null || opts === void 0 ? void 0 : opts.algorithm) || 'best fit',\n    }, [], {}, function () { return defaultLocale; }).locale;\n}\nexport { LookupSupportedLocales } from './abstract/LookupSupportedLocales';\nexport { ResolveLocale } from './abstract/ResolveLocale';\n"], "mappings": ";;;;;;;;;;AAIO,SAAS,uBAAuB,SAAS;AAE5C,SAAO,KAAK,oBAAoB,OAAO;AAC3C;AAPA;AAAA;AAAA;AAAA;;;ACAA,IAAW;AAAX;AAAA;AAAO,IAAI,OAAO;AAAA,MACd,cAAc;AAAA,QACV,kBAAkB;AAAA,UACd,eAAe;AAAA,YACX;AAAA,cACI,iBAAiB;AAAA,gBACb,UAAU;AAAA,cACd;AAAA,YACJ;AAAA,YACA;AAAA,cACI,OAAO;AAAA,gBACH,QAAQ;AAAA,cACZ;AAAA,YACJ;AAAA,YACA;AAAA,cACI,QAAQ;AAAA,gBACJ,QAAQ;AAAA,cACZ;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,QAAQ;AAAA,cACZ;AAAA,YACJ;AAAA,YACA;AAAA,cACI,UAAU;AAAA,gBACN,QAAQ;AAAA,cACZ;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,IAAI;AAAA,gBACA,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,gBACD,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,SAAS;AAAA,cACb;AAAA,YACJ;AAAA,YACA;AAAA,cACI,OAAO;AAAA,gBACH,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,iBAAiB;AAAA,gBACb,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,kBAAkB;AAAA,gBACd,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,UAAU;AAAA,gBACN,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,cAAc;AAAA,gBACV,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,WAAW;AAAA,gBACP,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,eAAe;AAAA,gBACX,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,UAAU;AAAA,gBACN,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,kBAAkB;AAAA,gBACd,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,mBAAmB;AAAA,gBACf,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,UAAU;AAAA,gBACN,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,kBAAkB;AAAA,gBACd,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,mBAAmB;AAAA,gBACf,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,UAAU;AAAA,gBACN,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,kBAAkB;AAAA,gBACd,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,mBAAmB;AAAA,gBACf,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,aAAa;AAAA,gBACT,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,YACA;AAAA,cACI,SAAS;AAAA,gBACL,UAAU;AAAA,gBACV,WAAW;AAAA,cACf;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;AC3jFA,IACW;AADX;AAAA;AACO,IAAI,UAAU;AAAA,MACjB,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;ACjzCO,SAAS,UAAU,WAAW,SAAS,KAAK;AAC/C,MAAI,QAAQ,QAAQ;AAAE,UAAM;AAAA,EAAO;AACnC,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,IAAI,OAAO;AAAA,EACzB;AACJ;AAIA,SAAS,cAAc;AACnB,MAAI,IAAI;AACR,MAAI,CAAC,gBAAgB;AACjB,QAAI,mBAAmB,MAAM,KAAK,KAAS,aAAa,iBAAiB,aAAa,EAAE,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,MAAM,GAAG;AAC/M,QAAI,iBAAiB,KAAS,aAAa,iBAAiB,aAAa,EAAE,MAAM,GAAG,CAAC;AACrF,QAAIA,QAAO,KAAS,aAAa,iBAAiB,aAAa,EAAE,MAAM,CAAC;AACxE,QAAI,UAAUA,MAAK,IAAI,SAAU,GAAG;AAChC,UAAI,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC;AAC1B,UAAI,QAAQ,EAAE,GAAG;AACjB,aAAO;AAAA,QACH,WAAW;AAAA,QACX,SAAS,MAAM;AAAA,QACf,UAAU,CAAC,MAAM;AAAA,QACjB,QAAQ,MAAM,WAAW,SAAS,OAAO;AAAA,MAC7C;AAAA,IACJ,GAAG,CAAC,CAAC;AACL,qBAAiB;AAAA,MACb;AAAA,MACA,gBAAgB,eAAe,OAAO,SAAU,KAAK,GAAG;AACpD,YAAI,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC;AAC1B,YAAI,QAAQ,EAAE,GAAG;AACjB,YAAI,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,OAAO,MAAM,GAAG;AAC1C,eAAO;AAAA,MACX,GAAG,CAAC,CAAC;AAAA,MACL,iBAAiB,cAAc,cAAc,CAAC,GAAG,iBAAiB,IAAI,GAAG,gBAAgB,IAAI,SAAU,GAAG;AACtG,eAAO,IAAI,KAAK,OAAO,EAAE,QAAQ,MAAM,GAAG,CAAC,EAAE,SAAS,EAAE,SAAS;AAAA,MACrE,CAAC,GAAG,IAAI;AAAA,IACZ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,UAAU,QAAQ,yBAAyB,gBAAgB;AAChE,MAAI,KAAK,wBAAwB,MAAM,GAAG,GAAG,WAAW,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC;AAC5F,MAAI,UAAU;AACd,MAAI,UAAU,OAAO,CAAC,MAAM,KAAK;AAC7B,QAAI,gBAAgB,OAAO,CAAC,MAAM;AAClC,QAAI,eAAe,gBACb,eAAe,OAAO,MAAM,CAAC,CAAC,IAC9B,eAAe,OAAO,MAAM,CAAC,CAAC;AACpC,QAAI,yBAAyB,aACxB,IAAI,SAAU,GAAG;AAAE,aAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;AAAA,IAAG,CAAC,EAC9C,OAAO,SAAU,KAAK,MAAM;AAAE,aAAO,cAAc,cAAc,CAAC,GAAG,KAAK,IAAI,GAAG,MAAM,IAAI;AAAA,IAAG,GAAG,CAAC,CAAC;AACxG,gBAAY,UAAU,EAAE,uBAAuB,QAAQ,OAAO,UAAU,EAAE,IAAI,KAC1E;AAAA,EACR,OACK;AACD,gBAAY,UAAU,OAAO,SACvB,WAAW,OAAO,WAAW,OAAO,SACpC;AAAA,EACV;AACA,cAAY,UAAU,OAAO,SAAS,WAAW,OAAO,WAAW,OAAO,SAAS;AACnF,cAAY,UAAU,OAAO,WACvB,aAAa,OAAO,aAAa,OAAO,WACxC;AACN,SAAO;AACX;AACA,SAAS,aAAa,KAAK;AACvB,SAAO,CAAC,IAAI,UAAU,IAAI,QAAQ,IAAI,MAAM,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC1E;AACA,SAAS,2BAA2B,SAAS,WAAWA,OAAM;AAC1D,WAAS,KAAK,GAAG,KAAKA,MAAK,SAAS,KAAK,GAAG,QAAQ,MAAM;AACtD,QAAI,IAAI,GAAG,EAAE;AACb,QAAI,UAAU,UAAU,SAAS,EAAE,SAASA,MAAK,cAAc,KAC3D,UAAU,WAAW,EAAE,WAAWA,MAAK,cAAc;AACzD,QAAI,CAAC,EAAE,UAAU,CAAC,SAAS;AACvB,gBACI,UAAU,SAAS,EAAE,WAAWA,MAAK,cAAc,KAC/C,UAAU,WAAW,EAAE,SAASA,MAAK,cAAc;AAAA,IAC/D;AACA,QAAI,SAAS;AACT,UAAI,WAAW,EAAE,WAAW;AAC5B,UAAIA,MAAK,gBAAgB,QAAQ,aAAa,OAAO,CAAC,IAAI,MACtDA,MAAK,gBAAgB,QAAQ,aAAa,SAAS,CAAC,IAAI,IAAI;AAC5D,eAAO,WAAW;AAAA,MACtB;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,IAAI,MAAM,4BAA4B;AAChD;AACO,SAAS,qBAAqB,SAAS,WAAW;AACrD,MAAI,gBAAgB,IAAI,KAAK,OAAO,OAAO,EAAE,SAAS;AACtD,MAAI,kBAAkB,IAAI,KAAK,OAAO,SAAS,EAAE,SAAS;AAC1D,MAAI,aAAa;AAAA,IACb,UAAU,cAAc;AAAA,IACxB,QAAQ,cAAc,UAAU;AAAA,IAChC,QAAQ,cAAc,UAAU;AAAA,EACpC;AACA,MAAI,eAAe;AAAA,IACf,UAAU,gBAAgB;AAAA,IAC1B,QAAQ,gBAAgB,UAAU;AAAA,IAClC,QAAQ,gBAAgB,UAAU;AAAA,EACtC;AACA,MAAI,mBAAmB;AACvB,MAAIA,QAAO,YAAY;AACvB,MAAI,WAAW,aAAa,aAAa,UAAU;AAC/C,wBAAoB,2BAA2B;AAAA,MAC3C,UAAU,cAAc;AAAA,MACxB,QAAQ;AAAA,MACR,QAAQ;AAAA,IACZ,GAAG;AAAA,MACC,UAAU,gBAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,QAAQ;AAAA,IACZ,GAAGA,KAAI;AAAA,EACX;AACA,MAAI,WAAW,WAAW,aAAa,QAAQ;AAC3C,wBAAoB,2BAA2B;AAAA,MAC3C,UAAU,cAAc;AAAA,MACxB,QAAQ,WAAW;AAAA,MACnB,QAAQ;AAAA,IACZ,GAAG;AAAA,MACC,UAAU,gBAAgB;AAAA,MAC1B,QAAQ,WAAW;AAAA,MACnB,QAAQ;AAAA,IACZ,GAAGA,KAAI;AAAA,EACX;AACA,MAAI,WAAW,WAAW,aAAa,QAAQ;AAC3C,wBAAoB,2BAA2B,YAAY,cAAcA,KAAI;AAAA,EACjF;AACA,SAAO;AACX;AACO,SAAS,cAAc,kBAAkB,kBAAkB,WAAW;AACzE,MAAI,cAAc,QAAQ;AAAE,gBAAY;AAAA,EAA4B;AACpE,MAAI,iBAAiB;AACrB,MAAI,SAAS;AAAA,IACT,sBAAsB;AAAA,IACtB,WAAW,CAAC;AAAA,EAChB;AACA,mBAAiB,QAAQ,SAAU,SAAS,GAAG;AAC3C,QAAI,CAAC,OAAO,UAAU,OAAO,GAAG;AAC5B,aAAO,UAAU,OAAO,IAAI,CAAC;AAAA,IACjC;AACA,qBAAiB,QAAQ,SAAU,WAAW;AAI1C,UAAI,WAAW,qBAAqB,SAAS,SAAS,IAAI,IAAI,IAAI;AAClE,aAAO,UAAU,OAAO,EAAE,SAAS,IAAI;AACvC,UAAI,WAAW,gBAAgB;AAC3B,yBAAiB;AACjB,eAAO,uBAAuB;AAC9B,eAAO,yBAAyB;AAAA,MACpC;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACD,MAAI,kBAAkB,WAAW;AAC7B,WAAO,uBAAuB;AAC9B,WAAO,yBAAyB;AAAA,EACpC;AACA,SAAO;AACX;AApKA,IAGW,kCAQP,4BACA;AAZJ;AAAA;AAAA;AACA;AACA;AACO,IAAI,mCAAmC;AAQ9C,IAAI,6BAA6B;AAAA;AAAA;;;ACJ1B,SAAS,eAAe,kBAAkB,kBAAkB,kBAAkB;AACjF,MAAI;AACJ,MAAI;AACJ,MAAI,qBAAqB,CAAC;AAC1B,MAAI,uBAAuB,iBAAiB,OAAO,SAAU,KAAK,GAAG;AACjE,QAAI,oBAAoB,EAAE,QAAQ,kCAAkC,EAAE;AACtE,uBAAmB,KAAK,iBAAiB;AACzC,QAAI,iBAAiB,IAAI;AACzB,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,MAAI,SAAS,cAAc,oBAAoB,gBAAgB;AAC/D,MAAI,OAAO,0BAA0B,OAAO,sBAAsB;AAC9D,kBAAc,OAAO;AACrB,gBACI,qBAAqB,OAAO,oBAAoB,EAAE,MAAM,OAAO,qBAAqB,MAAM,KAAK;AAAA,EACvG;AACA,MAAI,CAAC,aAAa;AACd,WAAO,EAAE,QAAQ,iBAAiB,EAAE;AAAA,EACxC;AACA,SAAO;AAAA,IACH,QAAQ;AAAA,IACR;AAAA,EACJ;AACJ;AA9BA;AAAA;AAAA;AAAA;AAAA;;;ACKO,SAAS,oBAAoB,kBAAkB,QAAQ;AAC1D,MAAI,YAAY;AAChB,SAAO,MAAM;AACT,QAAI,iBAAiB,QAAQ,SAAS,IAAI,IAAI;AAC1C,aAAO;AAAA,IACX;AACA,QAAI,MAAM,UAAU,YAAY,GAAG;AACnC,QAAI,CAAC,CAAC,KAAK;AACP,aAAO;AAAA,IACX;AACA,QAAI,OAAO,KAAK,UAAU,MAAM,CAAC,MAAM,KAAK;AACxC,aAAO;AAAA,IACX;AACA,gBAAY,UAAU,MAAM,GAAG,GAAG;AAAA,EACtC;AACJ;AApBA;AAAA;AAAA;AAAA;;;ACQO,SAAS,cAAc,kBAAkB,kBAAkB,kBAAkB;AAChF,MAAI,SAAS,EAAE,QAAQ,GAAG;AAC1B,WAAS,KAAK,GAAG,qBAAqB,kBAAkB,KAAK,mBAAmB,QAAQ,MAAM;AAC1F,QAAI,SAAS,mBAAmB,EAAE;AAClC,QAAI,oBAAoB,OAAO,QAAQ,kCAAkC,EAAE;AAC3E,QAAI,kBAAkB,oBAAoB,kBAAkB,iBAAiB;AAC7E,QAAI,iBAAiB;AACjB,aAAO,SAAS;AAChB,UAAI,WAAW,mBAAmB;AAC9B,eAAO,YAAY,OAAO,MAAM,kBAAkB,QAAQ,OAAO,MAAM;AAAA,MAC3E;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO,SAAS,iBAAiB;AACjC,SAAO;AACX;AAxBA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACKO,SAAS,sBAAsB,WAAW,KAAK;AAClD,YAAU,IAAI,WAAW,GAAG,0BAA0B;AACtD,MAAI,OAAO,UAAU;AACrB,MAAI,cAAc,IAAI,OAAO,KAAK,GAAG;AACrC,MAAI,MAAM,UAAU,QAAQ,WAAW;AACvC,MAAI,QAAQ,IAAI;AACZ,QAAI,QAAQ,MAAM;AAClB,QAAI,MAAM;AACV,QAAI,IAAI;AACR,QAAI,OAAO;AACX,WAAO,CAAC,MAAM;AACV,UAAI,IAAI,UAAU,QAAQ,KAAK,CAAC;AAChC,UAAI,MAAM;AACV,UAAI,MAAM,IAAI;AACV,cAAM,OAAO;AAAA,MACjB,OACK;AACD,cAAM,IAAI;AAAA,MACd;AACA,UAAI,QAAQ,GAAG;AACX,eAAO;AAAA,MACX,WACS,MAAM,IAAI;AACf,cAAM;AACN,eAAO;AAAA,MACX,OACK;AACD,cAAM;AACN,YAAI,IAAI;AAAA,MACZ;AAAA,IACJ;AACA,WAAO,UAAU,MAAM,OAAO,GAAG;AAAA,EACrC;AACA,gBAAc,IAAI,OAAO,GAAG;AAC5B,QAAM,UAAU,QAAQ,WAAW;AACnC,MAAI,QAAQ,MAAM,MAAM,MAAM,MAAM;AAChC,WAAO;AAAA,EACX;AACA,SAAO;AACX;AA7CA;AAAA;AAAA;AAAA;AAAA;;;ACOO,SAAS,cAAc,kBAAkB,kBAAkB,SAAS,uBAAuB,YAAY,kBAAkB;AAC5H,MAAI,UAAU,QAAQ;AACtB,MAAI;AACJ,MAAI,YAAY,UAAU;AACtB,QAAI,cAAc,MAAM,KAAK,gBAAgB,GAAG,kBAAkB,gBAAgB;AAAA,EACtF,OACK;AACD,QAAI,eAAe,MAAM,KAAK,gBAAgB,GAAG,kBAAkB,gBAAgB;AAAA,EACvF;AACA,MAAI,cAAc,EAAE;AACpB,MAAI,SAAS,EAAE,QAAQ,IAAI,YAAY,YAAY;AACnD,MAAI,qBAAqB;AACzB,WAAS,KAAK,GAAG,0BAA0B,uBAAuB,KAAK,wBAAwB,QAAQ,MAAM;AACzG,QAAI,MAAM,wBAAwB,EAAE;AACpC,cAAU,eAAe,YAAY,2BAA2B,OAAO,WAAW,CAAC;AACnF,QAAI,kBAAkB,WAAW,WAAW;AAC5C,cAAU,OAAO,oBAAoB,YAAY,oBAAoB,MAAM,eAAe,OAAO,KAAK,oBAAoB,CAAC;AAC3H,QAAI,gBAAgB,gBAAgB,GAAG;AACvC,cAAU,MAAM,QAAQ,aAAa,GAAG,qBAAqB,OAAO,KAAK,mBAAmB,CAAC;AAC7F,QAAI,QAAQ,cAAc,CAAC;AAC3B,cAAU,OAAO,UAAU,YAAY,UAAU,MAAM,wCAAwC,OAAO,OAAO,OAAO,UAAU,EAAE,OAAO,GAAG,CAAC;AAC3I,QAAI,6BAA6B;AACjC,QAAI,EAAE,WAAW;AACb,UAAI,iBAAiB,sBAAsB,EAAE,WAAW,GAAG;AAC3D,UAAI,mBAAmB,QAAW;AAC9B,YAAI,mBAAmB,IAAI;AACvB,cAAI,CAAC,cAAc,QAAQ,cAAc,GAAG;AACxC,oBAAQ;AACR,yCAA6B,IAAI,OAAO,KAAK,GAAG,EAAE,OAAO,KAAK;AAAA,UAClE;AAAA,QACJ,WACS,CAAC,eAAe,QAAQ,MAAM,GAAG;AACtC,kBAAQ;AACR,uCAA6B,IAAI,OAAO,GAAG;AAAA,QAC/C;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO,SAAS;AAChB,UAAI,eAAe,QAAQ,GAAG;AAC9B,gBAAU,OAAO,iBAAiB,YAC9B,OAAO,iBAAiB,eACxB,iBAAiB,MAAM,gDAAgD;AAC3E,UAAI,CAAC,cAAc,QAAQ,YAAY,GAAG;AACtC,YAAI,iBAAiB,OAAO;AACxB,kBAAQ;AACR,uCAA6B;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,GAAG,IAAI;AACd,0BAAsB;AAAA,EAC1B;AACA,MAAI,mBAAmB,SAAS,GAAG;AAC/B,QAAI,eAAe,YAAY,QAAQ,KAAK;AAC5C,QAAI,iBAAiB,IAAI;AACrB,oBAAc,cAAc;AAAA,IAChC,OACK;AACD,UAAI,eAAe,YAAY,MAAM,GAAG,YAAY;AACpD,UAAI,gBAAgB,YAAY,MAAM,cAAc,YAAY,MAAM;AACtE,oBAAc,eAAe,qBAAqB;AAAA,IACtD;AACA,kBAAc,KAAK,oBAAoB,WAAW,EAAE,CAAC;AAAA,EACzD;AACA,SAAO,SAAS;AAChB,SAAO;AACX;AAzEA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;ACIO,SAAS,uBAAuB,kBAAkB,kBAAkB;AACvE,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,qBAAqB,kBAAkB,KAAK,mBAAmB,QAAQ,MAAM;AAC1F,QAAI,SAAS,mBAAmB,EAAE;AAClC,QAAI,oBAAoB,OAAO,QAAQ,kCAAkC,EAAE;AAC3E,QAAI,kBAAkB,oBAAoB,kBAAkB,iBAAiB;AAC7E,QAAI,iBAAiB;AACjB,aAAO,KAAK,eAAe;AAAA,IAC/B;AAAA,EACJ;AACA,SAAO;AACX;AAlBA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,SAAS,MAAM,kBAAkB,kBAAkB,eAAe,MAAM;AAC3E,SAAO,cAAc,kBAAkB,uBAAuB,gBAAgB,GAAG;AAAA,IAC7E,gBAAgB,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,cAAc;AAAA,EACnF,GAAG,CAAC,GAAG,CAAC,GAAG,WAAY;AAAE,WAAO;AAAA,EAAe,CAAC,EAAE;AACtD;AANA;AAAA;AAAA;AACA;AAMA;AACA;AAAA;AAAA;", "names": ["data"]}