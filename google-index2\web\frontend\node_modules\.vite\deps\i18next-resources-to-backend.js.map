{"version": 3, "sources": ["../../i18next-resources-to-backend/dist/esm/index.js"], "sourcesContent": ["var resourcesToBackend = function resourcesToBackend(res) {\n  return {\n    type: 'backend',\n    init: function init(services, backendOptions, i18nextOptions) {},\n    read: function read(language, namespace, callback) {\n      if (typeof res === 'function') {\n        if (res.length < 3) {\n          try {\n            var r = res(language, namespace);\n            if (r && typeof r.then === 'function') {\n              r.then(function (data) {\n                return callback(null, data && data.default || data);\n              }).catch(callback);\n            } else {\n              callback(null, r);\n            }\n          } catch (err) {\n            callback(err);\n          }\n          return;\n        }\n        res(language, namespace, callback);\n        return;\n      }\n      callback(null, res && res[language] && res[language][namespace]);\n    }\n  };\n};\n\nexport { resourcesToBackend as default };\n"], "mappings": ";;;AAAA,IAAI,qBAAqB,SAASA,oBAAmB,KAAK;AACxD,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM,SAAS,KAAK,UAAU,gBAAgB,gBAAgB;AAAA,IAAC;AAAA,IAC/D,MAAM,SAAS,KAAK,UAAU,WAAW,UAAU;AACjD,UAAI,OAAO,QAAQ,YAAY;AAC7B,YAAI,IAAI,SAAS,GAAG;AAClB,cAAI;AACF,gBAAI,IAAI,IAAI,UAAU,SAAS;AAC/B,gBAAI,KAAK,OAAO,EAAE,SAAS,YAAY;AACrC,gBAAE,KAAK,SAAU,MAAM;AACrB,uBAAO,SAAS,MAAM,QAAQ,KAAK,WAAW,IAAI;AAAA,cACpD,CAAC,EAAE,MAAM,QAAQ;AAAA,YACnB,OAAO;AACL,uBAAS,MAAM,CAAC;AAAA,YAClB;AAAA,UACF,SAAS,KAAK;AACZ,qBAAS,GAAG;AAAA,UACd;AACA;AAAA,QACF;AACA,YAAI,UAAU,WAAW,QAAQ;AACjC;AAAA,MACF;AACA,eAAS,MAAM,OAAO,IAAI,QAAQ,KAAK,IAAI,QAAQ,EAAE,SAAS,CAAC;AAAA,IACjE;AAAA,EACF;AACF;", "names": ["resourcesToBackend"]}