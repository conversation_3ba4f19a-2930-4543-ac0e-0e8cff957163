{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/locale-data/it.js"], "sourcesContent": ["/* @generated */\n// prettier-ignore\nif (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === 'function') {\n  Intl.PluralRules.__addLocaleData({\"data\":{\"categories\":{\"cardinal\":[\"one\",\"many\",\"other\"],\"ordinal\":[\"many\",\"other\"]},\"fn\":function(n, ord) {\n  var _n = String(n), se = _n.split(/[ce]/), e = se[1] || 0, c = e, s = String(e ? Number(se[0]) * Math.pow(10, e) : _n).split(\".\"), i = s[0], v0 = !s[1], i1000000 = i.slice(-6);\n  if (ord) return (n == 11 || n == 8 || n == 80 || n == 800) ? 'many' : 'other';\n  return n == 1 && v0 ? 'one'\n    : e == 0 && i != 0 && i1000000 == 0 && v0 || (e < 0 || e > 5) ? 'many'\n    : 'other';\n}},\"locale\":\"it\"})\n}\n"], "mappings": ";AAEA,IAAI,KAAK,eAAe,OAAO,KAAK,YAAY,oBAAoB,YAAY;AAC9E,OAAK,YAAY,gBAAgB,EAAC,QAAO,EAAC,cAAa,EAAC,YAAW,CAAC,OAAM,QAAO,OAAO,GAAE,WAAU,CAAC,QAAO,OAAO,EAAC,GAAE,MAAK,SAAS,GAAG,KAAK;AAC5I,QAAI,KAAK,OAAO,CAAC,GAAG,KAAK,GAAG,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,MAAM,EAAE;AAC9K,QAAI;AAAK,aAAQ,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,MAAO,SAAS;AACtE,WAAO,KAAK,KAAK,KAAK,QAClB,KAAK,KAAK,KAAK,KAAK,YAAY,KAAK,OAAO,IAAI,KAAK,IAAI,KAAK,SAC9D;AAAA,EACN,EAAC,GAAE,UAAS,KAAI,CAAC;AACjB;", "names": []}