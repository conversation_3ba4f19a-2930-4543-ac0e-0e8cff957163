{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/locale-data/pt-PT.js"], "sourcesContent": ["/* @generated */\n// prettier-ignore\nif (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === 'function') {\n  Intl.PluralRules.__addLocaleData({\"data\":{\"categories\":{\"cardinal\":[\"one\",\"many\",\"other\"],\"ordinal\":[\"other\"]},\"fn\":function(n, ord) {\n  var _n = String(n), se = _n.split(/[ce]/), e = se[1] || 0, c = e, s = String(e ? Number(se[0]) * Math.pow(10, e) : _n).split(\".\"), i = s[0], v0 = !s[1], i1000000 = i.slice(-6);\n  if (ord) return 'other';\n  return n == 1 && v0 ? 'one'\n    : e == 0 && i != 0 && i1000000 == 0 && v0 || (e < 0 || e > 5) ? 'many'\n    : 'other';\n}},\"locale\":\"pt-PT\"})\n}\n"], "mappings": ";AAEA,IAAI,KAAK,eAAe,OAAO,KAAK,YAAY,oBAAoB,YAAY;AAC9E,OAAK,YAAY,gBAAgB,EAAC,QAAO,EAAC,cAAa,EAAC,YAAW,CAAC,OAAM,QAAO,OAAO,GAAE,WAAU,CAAC,OAAO,EAAC,GAAE,MAAK,SAAS,GAAG,KAAK;AACrI,QAAI,KAAK,OAAO,CAAC,GAAG,KAAK,GAAG,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,MAAM,EAAE;AAC9K,QAAI;AAAK,aAAO;AAChB,WAAO,KAAK,KAAK,KAAK,QAClB,KAAK,KAAK,KAAK,KAAK,YAAY,KAAK,OAAO,IAAI,KAAK,IAAI,KAAK,SAC9D;AAAA,EACN,EAAC,GAAE,UAAS,QAAO,CAAC;AACpB;", "names": []}