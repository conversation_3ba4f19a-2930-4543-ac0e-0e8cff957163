{"version": 3, "sources": ["../../@formatjs/intl-locale/should-polyfill.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.shouldPolyfill = void 0;\n/**\n * https://bugs.chromium.org/p/v8/issues/detail?id=10682\n */\nfunction hasIntlGetCanonicalLocalesBug() {\n    try {\n        return new Intl.Locale('und-x-private').toString() === 'x-private';\n    }\n    catch (e) {\n        return true;\n    }\n}\nfunction shouldPolyfill() {\n    return !('Locale' in Intl) || hasIntlGetCanonicalLocalesBug();\n}\nexports.shouldPolyfill = shouldPolyfill;\n"], "mappings": ";;;;;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB;AAIzB,aAAS,gCAAgC;AACrC,UAAI;AACA,eAAO,IAAI,KAAK,OAAO,eAAe,EAAE,SAAS,MAAM;AAAA,MAC3D,SACO,GAAG;AACN,eAAO;AAAA,MACX;AAAA,IACJ;AACA,aAAS,iBAAiB;AACtB,aAAO,EAAE,YAAY,SAAS,8BAA8B;AAAA,IAChE;AACA,YAAQ,iBAAiB;AAAA;AAAA;", "names": []}