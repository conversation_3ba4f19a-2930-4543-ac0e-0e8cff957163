{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/locale-data/de.js"], "sourcesContent": ["/* @generated */\n// prettier-ignore\nif (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === 'function') {\n  Intl.PluralRules.__addLocaleData({\"data\":{\"categories\":{\"cardinal\":[\"one\",\"other\"],\"ordinal\":[\"other\"]},\"fn\":function(n, ord) {\n  var s = String(n).split('.'), v0 = !s[1];\n  if (ord) return 'other';\n  return n == 1 && v0 ? 'one' : 'other';\n}},\"locale\":\"de\"})\n}\n"], "mappings": ";AAEA,IAAI,KAAK,eAAe,OAAO,KAAK,YAAY,oBAAoB,YAAY;AAC9E,OAAK,YAAY,gBAAgB,EAAC,QAAO,EAAC,cAAa,EAAC,YAAW,CAAC,OAAM,OAAO,GAAE,WAAU,CAAC,OAAO,EAAC,GAAE,MAAK,SAAS,GAAG,KAAK;AAC9H,QAAI,IAAI,OAAO,CAAC,EAAE,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC;AACvC,QAAI;AAAK,aAAO;AAChB,WAAO,KAAK,KAAK,KAAK,QAAQ;AAAA,EAChC,EAAC,GAAE,UAAS,KAAI,CAAC;AACjB;", "names": []}