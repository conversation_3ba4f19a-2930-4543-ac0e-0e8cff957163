import { useState, useEffect, useCallback } from 'react';
import {
  Page,
  Layout,
  Card,
  Button,
  Spinner,
  Banner,
  TextField,
  Select,
  RangeSlider,
  Checkbox,
  FormLayout,
  Tabs,
  ResourceList,
  ResourceItem,
  Avatar,
  Badge,
  Stack
} from '@shopify/polaris';

export default function MaskConfigTest() {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [maskConfig, setMaskConfig] = useState(null);
  const [arSettings, setArSettings] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Fetch products on component mount
  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/furniture/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ page: 1, limit: 20 })
      });

      const result = await response.json();

      if (result.success) {
        setProducts(result.data.products);
      } else {
        setError(result.error || 'Failed to fetch products');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchMaskConfig = useCallback(async (productId) => {
    setLoading(true);
    try {
      const response = await fetch('/api/furniture/product/mask-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: productId,
          action: 'get'
        })
      });

      const result = await response.json();

      if (result.success) {
        setMaskConfig(result.data.maskConfig);
        setArSettings(result.data.arSettings);
        setSelectedProduct(result.data.product);
      } else {
        setError(result.error || 'Failed to fetch mask configuration');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  const saveMaskConfig = useCallback(async () => {
    if (!selectedProduct) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/furniture/product/mask-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: selectedProduct.id,
          action: 'save',
          maskConfig: maskConfig,
          arSettings: arSettings
        })
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('Mask configuration saved successfully');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(result.error || 'Failed to save mask configuration');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  }, [selectedProduct, maskConfig, arSettings]);

  const resetMaskConfig = useCallback(async () => {
    if (!selectedProduct) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/furniture/product/mask-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: selectedProduct.id,
          action: 'reset'
        })
      });

      const result = await response.json();

      if (result.success) {
        await fetchMaskConfig(selectedProduct.id);
        setSuccess('Mask configuration reset to defaults');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(result.error || 'Failed to reset mask configuration');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  }, [selectedProduct, fetchMaskConfig]);

  const handleProductSelect = (productId) => {
    fetchMaskConfig(productId);
  };

  const updateMaskConfig = (field, value) => {
    setMaskConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateArSettings = (section, field, value) => {
    setArSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const blendModeOptions = [
    { label: 'Normal', value: 'normal' },
    { label: 'Multiply', value: 'multiply' },
    { label: 'Screen', value: 'screen' },
    { label: 'Overlay', value: 'overlay' },
    { label: 'Soft Light', value: 'soft-light' },
    { label: 'Hard Light', value: 'hard-light' }
  ];

  const tabs = [
    {
      id: 'mask-settings',
      content: 'Mask Settings',
      panelID: 'mask-settings-panel'
    },
    {
      id: 'ar-settings', 
      content: 'AR Settings',
      panelID: 'ar-settings-panel'
    }
  ];

  const renderMaskSettings = () => (
    <Card sectioned>
      <FormLayout>
        <Checkbox
          label="Enable Mask Layer"
          checked={maskConfig?.enabled || false}
          onChange={(value) => updateMaskConfig('enabled', value)}
        />

        <RangeSlider
          label={`Opacity: ${Math.round((maskConfig?.opacity || 0.5) * 100)}%`}
          value={maskConfig?.opacity || 0.5}
          min={0}
          max={1}
          step={0.01}
          onChange={(value) => updateMaskConfig('opacity', value)}
        />

        <TextField
          label="Mask Color"
          value={maskConfig?.color || '#000000'}
          onChange={(value) => updateMaskConfig('color', value)}
          type="color"
        />

        <Select
          label="Blend Mode"
          options={blendModeOptions}
          value={maskConfig?.blendMode || 'multiply'}
          onChange={(value) => updateMaskConfig('blendMode', value)}
        />

        <Checkbox
          label="Auto Detect Mask"
          checked={maskConfig?.autoDetect || false}
          onChange={(value) => updateMaskConfig('autoDetect', value)}
        />

        <RangeSlider
          label={`Detection Threshold: ${Math.round((maskConfig?.threshold || 0.5) * 100)}%`}
          value={maskConfig?.threshold || 0.5}
          min={0}
          max={1}
          step={0.01}
          onChange={(value) => updateMaskConfig('threshold', value)}
        />

        <TextField
          label="Feather Radius (px)"
          type="number"
          value={maskConfig?.featherRadius?.toString() || '2'}
          onChange={(value) => updateMaskConfig('featherRadius', parseInt(value) || 2)}
        />

        <Checkbox
          label="Invert Mask"
          checked={maskConfig?.invertMask || false}
          onChange={(value) => updateMaskConfig('invertMask', value)}
        />
      </FormLayout>
    </Card>
  );

  return (
    <Page 
      title="Mask Configuration Test" 
      subtitle="Test mask layer configuration functionality"
      primaryAction={{
        content: 'Save Configuration',
        onAction: saveMaskConfig,
        disabled: !selectedProduct || saving,
        loading: saving
      }}
      secondaryActions={[
        {
          content: 'Reset to Defaults',
          onAction: resetMaskConfig,
          disabled: !selectedProduct || saving
        }
      ]}
    >
      <Layout>
        {error && (
          <Layout.Section>
            <Banner status="critical" title="Error">
              <p>{error}111</p>
            </Banner>
          </Layout.Section>
        )}

        {success && (
          <Layout.Section>
            <Banner status="success" title="Success">
              <p>{success}</p>
            </Banner>
          </Layout.Section>
        )}

        <Layout.Section oneThird>
          {loading && !selectedProduct ? (
            <Card sectioned>
              <div style={{ textAlign: 'center', padding: '2rem' }}>
                <Spinner size="large" />
                <p style={{ marginTop: '1rem' }}>Loading products...</p>
              </div>
            </Card>
          ) : (
            <Card>
              <Card.Section>
                <h3>Select Product</h3>
                <p>Choose a product to configure its mask settings.</p>
              </Card.Section>

              <ResourceList
                resourceName={{ singular: 'product', plural: 'products' }}
                items={products}
                renderItem={(product) => {
                  const { id, title, image, ar_enabled } = product;
                  const media = image ? <Avatar customer size="medium" source={image.src} /> : undefined;

                  return (
                    <ResourceItem
                      id={id}
                      media={media}
                      accessibilityLabel={`Select ${title}`}
                      onClick={() => handleProductSelect(id)}
                    >
                      <Stack>
                        <Stack.Item fill>
                          <h3>{title}</h3>
                        </Stack.Item>
                        <Stack.Item>
                          <Badge status={ar_enabled ? 'success' : 'info'}>
                            {ar_enabled ? 'AI Enabled' : 'AI disabled'}
                          </Badge>
                        </Stack.Item>
                      </Stack>
                    </ResourceItem>
                  );
                }}
              />
            </Card>
          )}
        </Layout.Section>

        <Layout.Section>
          {selectedProduct ? (
            <Stack vertical spacing="loose">
              <Card sectioned>
                <Stack>
                  <Stack.Item>
                    {selectedProduct.image && (
                      <Avatar size="large" source={selectedProduct.image.src} />
                    )}
                  </Stack.Item>
                  <Stack.Item fill>
                    <h2>{selectedProduct.title}</h2>
                    <p>Configure mask settings for this product.</p>
                  </Stack.Item>
                </Stack>
              </Card>

              {maskConfig ? (
                <Card>
                  <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab}>
                    <Card.Section>
                      {selectedTab === 0 && renderMaskSettings()}
                      {selectedTab === 1 && (
                        <Card sectioned>
                          <p>AR Settings panel - Coming soon</p>
                        </Card>
                      )}
                    </Card.Section>
                  </Tabs>
                </Card>
              ) : (
                <Card sectioned>
                  <div style={{ textAlign: 'center', padding: '2rem' }}>
                    <Spinner size="large" />
                    <p style={{ marginTop: '1rem' }}>Loading configuration...</p>
                  </div>
                </Card>
              )}
            </Stack>
          ) : (
            <Card sectioned>
              <div style={{ textAlign: 'center', padding: '4rem' }}>
                <h3>No Product Selected</h3>
                <p>Please select a product from the list to configure its mask settings.</p>
              </div>
            </Card>
          )}
        </Layout.Section>
      </Layout>
    </Page>
  );
}
