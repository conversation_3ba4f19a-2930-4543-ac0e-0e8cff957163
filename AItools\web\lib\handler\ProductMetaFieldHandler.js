import { GqlConnecter } from '../gql/GqlConnecter.js';
import { MetaFieldQueries } from '../gql/MetaFieldQueries.js';

export class ProductMetaFieldHandler {
  
  // Check if AR metafield definition exists
  static async checkMetaFieldDefinitionExists(session) {
    try {
      const query = MetaFieldQueries.getMetaFieldDefinitionsQuery();
      const variables = MetaFieldQueries.buildMetaFieldDefinitionQueryVariables();

      const data = await GqlConnecter.AdminGqlExe(session, query, variables);
      MetaFieldQueries.validateResponse(data, 'checkMetaFieldDefinitionExists');

      const definitions = data.metafieldDefinitions.edges;
      return definitions.length > 0 ? definitions[0].node : null;
    } catch (error) {
      console.error('Error checking metafield definition:', error);
      throw error;
    }
  }

  // Create AR metafield definition
  static async createMetaFieldDefinition(session) {
    try {
      const mutation = MetaFieldQueries.createMetaFieldDefinitionMutation();
      const variables = MetaFieldQueries.buildMetaFieldDefinitionVariables();

      const data = await GqlConnecter.AdminGqlExe(session, mutation, variables);
      MetaFieldQueries.validateResponse(data, 'createMetaFieldDefinition');

      const result = data.metafieldDefinitionCreate;
      const userErrors = MetaFieldQueries.extractUserErrors(result);
      
      if (userErrors) {
        throw new Error(`Failed to create metafield definition: ${userErrors}`);
      }  

      return {
        success: true,
        definition: result.createdDefinition
      };
    } catch (error) {
      console.error('Error creating metafield definition:', error);
      throw error;
    }
  }

  // Delete AR metafield definition
  static async deleteMetaFieldDefinition(session, definitionId) {
    try {
      const mutation = MetaFieldQueries.deleteMetaFieldDefinitionMutation();
      const variables = {
        id: definitionId,
        deleteAllAssociatedMetafields: true
      };

      const data = await GqlConnecter.AdminGqlExe(session, mutation, variables);
      MetaFieldQueries.validateResponse(data, 'deleteMetaFieldDefinition');

      const result = data.metafieldDefinitionDelete;
      const userErrors = MetaFieldQueries.extractUserErrors(result);
      
      if (userErrors) {
        throw new Error(`Failed to delete metafield definition: ${userErrors}`);
      }

      return {
        success: true,
        deletedId: result.deletedDefinitionId
      };
    } catch (error) {
      console.error('Error deleting metafield definition:', error);
      throw error;
    }
  }

  // Set AI Enabled status for a product
  static async setProductAREnabled(session, productId, enabled) {
    try {
      const mutation = MetaFieldQueries.setMetaFieldsMutation();
      const variables = MetaFieldQueries.buildSetMetaFieldVariables(productId, enabled);

      const data = await GqlConnecter.AdminGqlExe(session, mutation, variables);
      MetaFieldQueries.validateResponse(data, 'setProductAREnabled');

      const result = data.metafieldsSet;
      const userErrors = MetaFieldQueries.extractUserErrors(result);
      
      if (userErrors) {
        throw new Error(`Failed to set product AR status: ${userErrors}`);
      }

      return {
        success: true,
        metafields: result.metafields
      };
    } catch (error) {
      console.error('Error setting product AR status:', error);
      throw error;
    }
  }

  // Get product with AR status from metafield
  static async getProductWithARStatus(session, productId) {
    try {
      const query = MetaFieldQueries.getProductWithMetaFieldQuery();
      const variables = MetaFieldQueries.buildProductQueryVariables(productId);

      const data = await GqlConnecter.AdminGqlExe(session, query, variables);
      MetaFieldQueries.validateResponse(data, 'getProductWithARStatus');

      if (!data.product) {
        throw new Error('Product not found');
      }

      const product = data.product;
      const arEnabled = product.metafield ? product.metafield.value === 'true' : false;

      return {
        success: true,
        product: {
          id: product.id,
          title: product.title,
          handle: product.handle,
          status: product.status,
          created_at: product.createdAt,
          updated_at: product.updatedAt,
          image: product.featuredImage ? { src: product.featuredImage.url } : null,
          images: product.images.nodes.map(img => ({ src: img.url, alt: img.altText })),
          ar_enabled: arEnabled,
          metafield: product.metafield
        }
      };
    } catch (error) {
      console.error('Error getting product with AR status:', error);
      throw error;
    }
  }

  // Get multiple products with AR status from metafields
  static async getProductsWithARStatus(session, page = 1, limit = 20, searchQuery = '') {
    try {
      const query = MetaFieldQueries.getProductsWithMetaFieldsQuery();
      const variables = MetaFieldQueries.buildProductsQueryVariables(page, limit, searchQuery);

      const data = await GqlConnecter.AdminGqlExe(session, query, variables);
      MetaFieldQueries.validateResponse(data, 'getProductsWithARStatus');

      if (!data.products) {
        return {
          success: true,
          products: [],
          totalPages: 0,
          totalProducts: 0,
          hasNextPage: false,
          hasPreviousPage: false
        };
      }

      const products = data.products.edges.map(edge => {
        const product = edge.node;
        const arEnabled = product.metafield ? product.metafield.value === 'true' : false;

        return {
          id: product.id,
          title: product.title,
          handle: product.handle,
          status: product.status,
          created_at: product.createdAt,
          updated_at: product.updatedAt,
          image: product.featuredImage ? { src: product.featuredImage.url } : null,
          ar_enabled: arEnabled,
          metafield: product.metafield
        };
      });

      const pageInfo = data.products.pageInfo;
      
      // Calculate approximate pagination info
      const totalProducts = products.length < limit ? (page - 1) * limit + products.length : page * limit;
      const totalPages = Math.ceil(totalProducts / limit);

      return {
        success: true,
        products: products,
        totalPages: pageInfo.hasNextPage ? totalPages + 1 : totalPages,
        totalProducts: totalProducts,
        hasNextPage: pageInfo.hasNextPage,
        hasPreviousPage: pageInfo.hasPreviousPage,
        currentPage: page
      };
    } catch (error) {
      console.error('Error getting products with AR status:', error);
      throw error;
    }
  }

  // Check if product has AI Enabled (for proxy requests)
  static async checkProductAREnabled(session, productId) {
    try {
      const result = await this.getProductWithARStatus(session, productId);
      return result.success && result.product.ar_enabled;
    } catch (error) {
      console.error('Error checking product AR status:', error);
      return false;
    }
  }

  // Enable AR for product and create mask configuration
  static async enableProductAR(session, productId, maskData = null) {
    try {
      // Set metafield to true
      await this.setProductAREnabled(session, productId, true);

      // If mask data is provided, save it to database
      if (maskData) {
        // Import here to avoid circular dependency
        const { FurnitureARDBWorker } = await import('../db/FurnitureARDBWorker.js');
        const { FurnitureProductHandler } = await import('./FurnitureProductHandler.js');
        
        const shop = await FurnitureProductHandler.getOrCreateShop(session);
        
        // Save mask data to database
        await FurnitureARDBWorker.createOrUpdateProductConfig(
          shop.id,
          shop.shop_name,
          productId,
          true, // app_enabled
          null, // product_image_url
          JSON.stringify(maskData), // mask_data
          null, // mask_config
          null  // ar_settings
        );
      }

      return {
        success: true,
        message: 'AI Enabled for product'
      };
    } catch (error) {
      console.error('Error enabling product AR:', error);
      throw error;
    }
  }

  // Disable AR for product
  static async disableProductAR(session, productId) {
    try {
      // Set metafield to false
      await this.setProductAREnabled(session, productId, false);

      return {
        success: true,
        message: 'AI disabled for product'
      };
    } catch (error) {
      console.error('Error disabling product AR:', error);
      throw error;
    }
  }

  // Initialize metafield definition for shop (create if not exists)
  static async initializeMetaFieldDefinition(session) {
    try {
      // Check if definition already exists
      const existingDefinition = await this.checkMetaFieldDefinitionExists(session);
      
      if (existingDefinition) {
        return {
          success: true,
          exists: true,
          definition: existingDefinition
        };
      }

      // Create new definition
      const result = await this.createMetaFieldDefinition(session);
      
      return {
        success: true,
        exists: false,
        definition: result.definition
      };
    } catch (error) {
      console.error('Error initializing metafield definition:', error);
      throw error;
    }
  }
}
