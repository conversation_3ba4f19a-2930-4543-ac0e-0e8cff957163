{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/locale-data/sv.js"], "sourcesContent": ["/* @generated */\n// prettier-ignore\nif (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === 'function') {\n  Intl.PluralRules.__addLocaleData({\"data\":{\"categories\":{\"cardinal\":[\"one\",\"other\"],\"ordinal\":[\"one\",\"other\"]},\"fn\":function(n, ord) {\n  var s = String(n).split('.'), v0 = !s[1], t0 = Number(s[0]) == n, n10 = t0 && s[0].slice(-1), n100 = t0 && s[0].slice(-2);\n  if (ord) return (n10 == 1 || n10 == 2) && n100 != 11 && n100 != 12 ? 'one' : 'other';\n  return n == 1 && v0 ? 'one' : 'other';\n}},\"locale\":\"sv\"})\n}\n"], "mappings": ";AAEA,IAAI,KAAK,eAAe,OAAO,KAAK,YAAY,oBAAoB,YAAY;AAC9E,OAAK,YAAY,gBAAgB,EAAC,QAAO,EAAC,cAAa,EAAC,YAAW,CAAC,OAAM,OAAO,GAAE,WAAU,CAAC,OAAM,OAAO,EAAC,GAAE,MAAK,SAAS,GAAG,KAAK;AACpI,QAAI,IAAI,OAAO,CAAC,EAAE,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,OAAO,EAAE,CAAC,CAAC,KAAK,GAAG,MAAM,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,OAAO,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE;AACxH,QAAI;AAAK,cAAQ,OAAO,KAAK,OAAO,MAAM,QAAQ,MAAM,QAAQ,KAAK,QAAQ;AAC7E,WAAO,KAAK,KAAK,KAAK,QAAQ;AAAA,EAChC,EAAC,GAAE,UAAS,KAAI,CAAC;AACjB;", "names": []}