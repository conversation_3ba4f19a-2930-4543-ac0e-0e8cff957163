{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/supported-locales.generated.js", "../../@formatjs/intl-pluralrules/should-polyfill.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.supportedLocales = void 0;\nexports.supportedLocales = [\"af\", \"ak\", \"am\", \"an\", \"ar\", \"ars\", \"as\", \"asa\", \"ast\", \"az\", \"bal\", \"be\", \"bem\", \"bez\", \"bg\", \"bho\", \"bm\", \"bn\", \"bo\", \"br\", \"brx\", \"bs\", \"ca\", \"ce\", \"ceb\", \"cgg\", \"chr\", \"ckb\", \"cs\", \"cy\", \"da\", \"de\", \"doi\", \"dsb\", \"dv\", \"dz\", \"ee\", \"el\", \"en\", \"eo\", \"es\", \"et\", \"eu\", \"fa\", \"ff\", \"fi\", \"fil\", \"fo\", \"fr\", \"fur\", \"fy\", \"ga\", \"gd\", \"gl\", \"gsw\", \"gu\", \"guw\", \"gv\", \"ha\", \"haw\", \"he\", \"hi\", \"hnj\", \"hr\", \"hsb\", \"hu\", \"hy\", \"ia\", \"id\", \"ig\", \"ii\", \"io\", \"is\", \"it\", \"iu\", \"ja\", \"jbo\", \"jgo\", \"jmc\", \"jv\", \"jw\", \"ka\", \"kab\", \"kaj\", \"kcg\", \"kde\", \"kea\", \"kk\", \"kkj\", \"kl\", \"km\", \"kn\", \"ko\", \"ks\", \"ksb\", \"ksh\", \"ku\", \"kw\", \"ky\", \"lag\", \"lb\", \"lg\", \"lij\", \"lkt\", \"ln\", \"lo\", \"lt\", \"lv\", \"mas\", \"mg\", \"mgo\", \"mk\", \"ml\", \"mn\", \"mo\", \"mr\", \"ms\", \"mt\", \"my\", \"nah\", \"naq\", \"nb\", \"nd\", \"ne\", \"nl\", \"nn\", \"nnh\", \"no\", \"nqo\", \"nr\", \"nso\", \"ny\", \"nyn\", \"om\", \"or\", \"os\", \"osa\", \"pa\", \"pap\", \"pcm\", \"pl\", \"prg\", \"ps\", \"pt\", \"pt-PT\", \"rm\", \"ro\", \"rof\", \"ru\", \"rwk\", \"sah\", \"saq\", \"sat\", \"sc\", \"scn\", \"sd\", \"sdh\", \"se\", \"seh\", \"ses\", \"sg\", \"sh\", \"shi\", \"si\", \"sk\", \"sl\", \"sma\", \"smi\", \"smj\", \"smn\", \"sms\", \"sn\", \"so\", \"sq\", \"sr\", \"ss\", \"ssy\", \"st\", \"su\", \"sv\", \"sw\", \"syr\", \"ta\", \"te\", \"teo\", \"th\", \"ti\", \"tig\", \"tk\", \"tl\", \"tn\", \"to\", \"tpi\", \"tr\", \"ts\", \"tzm\", \"ug\", \"uk\", \"und\", \"ur\", \"uz\", \"ve\", \"vi\", \"vo\", \"vun\", \"wa\", \"wae\", \"wo\", \"xh\", \"xog\", \"yi\", \"yo\", \"yue\", \"zh\", \"zu\"];\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.shouldPolyfill = void 0;\nvar intl_localematcher_1 = require(\"@formatjs/intl-localematcher\");\nvar supported_locales_generated_1 = require(\"./supported-locales.generated\");\nfunction supportedLocalesOf(locale) {\n    if (!locale) {\n        return true;\n    }\n    var locales = Array.isArray(locale) ? locale : [locale];\n    return Intl.PluralRules.supportedLocalesOf(locales).length === locales.length;\n}\nfunction shouldPolyfill(locale) {\n    if (locale === void 0) { locale = 'en'; }\n    if (!('PluralRules' in Intl) ||\n        new Intl.PluralRules('en', { minimumFractionDigits: 2 }).select(1) ===\n            'one' ||\n        !supportedLocalesOf(locale)) {\n        return locale ? (0, intl_localematcher_1.match)([locale], supported_locales_generated_1.supportedLocales, 'en') : undefined;\n    }\n}\nexports.shouldPolyfill = shouldPolyfill;\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,mBAAmB;AAC3B,YAAQ,mBAAmB,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,OAAO,MAAM,OAAO,MAAM,OAAO,OAAO,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,OAAO,MAAM,OAAO,MAAM,MAAM,SAAS,MAAM,MAAM,OAAO,MAAM,OAAO,OAAO,OAAO,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,IAAI;AAAA;AAAA;;;ACHh3C;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB;AACzB,QAAI,uBAAuB;AAC3B,QAAI,gCAAgC;AACpC,aAAS,mBAAmB,QAAQ;AAChC,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MACX;AACA,UAAI,UAAU,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACtD,aAAO,KAAK,YAAY,mBAAmB,OAAO,EAAE,WAAW,QAAQ;AAAA,IAC3E;AACA,aAAS,eAAe,QAAQ;AAC5B,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAM;AACxC,UAAI,EAAE,iBAAiB,SACnB,IAAI,KAAK,YAAY,MAAM,EAAE,uBAAuB,EAAE,CAAC,EAAE,OAAO,CAAC,MAC7D,SACJ,CAAC,mBAAmB,MAAM,GAAG;AAC7B,eAAO,UAAU,GAAG,qBAAqB,OAAO,CAAC,MAAM,GAAG,8BAA8B,kBAAkB,IAAI,IAAI;AAAA,MACtH;AAAA,IACJ;AACA,YAAQ,iBAAiB;AAAA;AAAA;", "names": []}