// @ts-check
import { join } from "path";
import { readFileSync } from "fs";
import express from "express";
import serveStatic from "serve-static";

import shopify from "./shopify.js";
import productCreator from "./product-creator.js";
import GDPRWebhookHandlers from "./gdpr.js";
import {OAuth2Strategy} from "passport-google-oauth"; 
import session from 'express-session';
import passport from "passport";
import {createServiceAccount,refreshToken} from "./gcs.js";
import cors from 'cors';
import {DBconnecter} from './lib/db/DBconnecter.js';

import { GSCAuthWorker } from "./lib/gsc/GscAuthWorker.js";

import { IndexHandler } from "./lib/handler/IndexHandler.js";
import { ShopHandler } from "./lib/handler/ShopHandler.js";
import { ServiceAccountHandler } from "./lib/handler/ServiceAccountHandler.js";
import {IndexLogDBWorker} from './lib/db/IndexLogDBWorker.js';
import {IndexScheduler} from './lib/handler/IndexScheduler.js';
import cron from 'node-cron';
import connectPg from 'connect-pg-simple';



const PORT = parseInt(
  process.env.BACKEND_PORT || process.env.PORT || "3000", 
  10
);




const STATIC_PATH =
  process.env.NODE_ENV === "production"
    ? `${process.cwd()}/frontend/dist`
    : `${process.cwd()}/frontend/`;

const app = express();

const dbPath=`${process.cwd()}/database.sqlite`;

const myDBconnecter= new DBconnecter(dbPath);
console.log(myDBconnecter);
global.DBconnecter = myDBconnecter; 
global.DBconnecter.DBinitate();
global.envShopify= shopify;

const gscAuthWorker=new GSCAuthWorker();
global.gscAuthWorker=gscAuthWorker;

// 创建global变量stopCreateService
global.stopCreateService = new Map();

// Set up Shopify authentication and webhook handling
app.get(shopify.config.auth.path, shopify.auth.begin());
app.get(
  shopify.config.auth.callbackPath,
  shopify.auth.callback(),
  (req, res, next) => {
    console.log('开始做自己的事情');
    console.log(res.locals.shopify.session.shop);
    next();
  },
  shopify.redirectToShopifyOrAppRoot()
);

// 在 webhook 路由前添加调试中间件
app.use(shopify.config.webhooks.path, (req, res, next) => {
  console.log('Webhook received:', {
    headers: req.headers,
    body: req.body?.substring(0, 100) + '...' // 只显示前100个字符
  });
  next();
});

app.post(
  shopify.config.webhooks.path,
  shopify.processWebhooks({ webhookHandlers: GDPRWebhookHandlers })
);

// If you are adding routes outside of the /api path, remember to
// also add a proxy rule for them in web/frontend/vite.config.js

app.use("/api/*", shopify.validateAuthenticatedSession());
app.use(express.json());


const PgSession = connectPg(session);

app.use("/google/*",session({
  store: process.env.NODE_ENV === 'production' ? new PgSession({
    conString: process.env.DATABASE_URL,
    tableName: 'user_sessions',
    createTableIfMissing: true
  }) : undefined,
  resave: false,
  saveUninitialized: true,
  secret: process.env.SESSION_SECRET || 'SECRET',
}));


app.use("/google/*",cors({
    origin: 'https://accounts.google.com',
    credentials: true // 允许发送凭据（如 Cookie）
}));

app.use("/google/*",global.gscAuthWorker.passportInit());
app.use("/google/*",global.gscAuthWorker.passportSession());


app.get('/google/googleLogin',
  function(req, res) {
    const myPrivateShop = req.query.privateShop;
    const myauth = global.gscAuthWorker.authLogin(myPrivateShop);
    myauth(req,res);
  }
);  

app.get('/google/callback',
  passport.authenticate('google', { failureRedirect: '/' }),
  function(req, res) {
    const myCallback = global.gscAuthWorker.authCallback();
    const requestData=req.query;

    myCallback(req,res,function(){
      // 获取当前页面所在域名
      //const protocol = req.protocol; // http 或 https
      const protocol = 'https'; // 固定为 https
      const host = req.get('host'); // 域名和端口
      const baseUrl = `${protocol}://${host}`;

      console.log(`OAuth callback redirect: ${baseUrl}/native/loginSuccess?privateShop=${requestData.state}`);
      res.redirect(`${baseUrl}/native/loginSuccess?privateShop=${requestData.state}`);
    });
});


  





app.post('/api/getURLs', 
  async function(req, res) {
    const requestData = req.body;


    const session=res.locals.shopify.session;
    const urls=await IndexHandler.getURLs(session,requestData.queryType,requestData.component,requestData.page,requestData.size);
    // 发送 JSON 响应给前端
    res.json(JSON.stringify(urls));

  });

  app.post('/api/getPageCount', 
    async function(req, res) {
      const requestData = req.body;
  
  
      const session=res.locals.shopify.session;
      const count=await IndexHandler.getPageCount(session,requestData.component,requestData.pageStatus);
      // 发送 JSON 响应给前端
      res.json(JSON.stringify(count));

    });

app.post('/api/consoleMetris', 
  async function(req, res) {

    try{
      const session=res.locals.shopify.session;
      const result = await ShopHandler.consoleMetris(session);

      if(result){
        res.json(result);
      }else{
        res.json(null);
      }
    }catch(error){
      console.log(`consoleMetris Error:${error}`);
    }
    
  });

  
app.post('/api/checkShopStatus', 
  async function(req, res) {



    const session=res.locals.shopify.session;
    const result=await ShopHandler.checkShopStatus(session);
  

    //const aa={billPlan:'11'};

    if(result){  
      res.json(result); 
    }else{
      res.json(null);
    }
     //发送 JSON 响应给前端

  });

app.post('/api/IndexAll', 
  async function(req, res) {
    const requestData = req.body;


    const session=res.locals.shopify.session;
    const urls=await IndexHandler.indexAll(session);
    // 发送 JSON 响应给前端
    res.json(urls);

});

// app.get('/google/creatServiceAccount', 
//   async function(req, res) {
//     res.setHeader('Cache-Control', 'no-store');
//     const requestData = req.query;


//     const myPrivateShop=requestData.privateShop;
//     const clientID=await ServiceAccountHandler.createServiceAccount(myPrivateShop);
//     // 发送 JSON 响应给前端

//     const responseData = {
//       clientID: clientID,
//     };
    
//     // 发送 JSON 响应给前端
//     res.json(responseData);
// });

app.post('/api/urlScan', 
  async function(req, res) {
    const requestData = req.body;


    const session=res.locals.shopify.session;
    const myStatus=await ShopHandler.urlScan(session);
    // 发送 JSON 响应给前端

    const responseData = {
      status: myStatus,
    };
    
    // 发送 JSON 响应给前端
    res.json(responseData);
});

app.post('/api/getServiceAccount', 
  async function(req, res) {
    const requestData = req.body;


    const session=res.locals.shopify.session;
    const serviceAccount=await ServiceAccountHandler.getServicesAccount(session);
    // 发送 JSON 响应给前端
    res.json(serviceAccount);

});

app.post('/api/payBill', 
  async function(req, res) {
    const requestData = req.body;

    const session=res.locals.shopify.session;
    const shopID=await ShopHandler.getShopIDbySession(session);

    const billPlan=await ShopSettingDBWorker.getBillPlan(shopID);
    let billPath

    console.log(`Find Bill Plan:${shopID}`);

    if (billPlan.length>0){
      console.log('Find Bill Plan');
      if (billPlan[0].billDate){
            billPath=await ShopGqlWorker.payBill(session,'Monthly-B'); // 已有过付款记录，则没有试用期
      }else{
            billPath=await ShopGqlWorker.payBill(session,'Monthly-A'); // 没有过付款记录，则有试用期
      }
    }else{
      billPath=await ShopGqlWorker.payBill(session,'Monthly-A'); // 没有过付款记录，则有试用期
    }

    // 发送 JSON 响应给前端
    const responseData = {
      billPath: billPath,
    };

    // 发送 JSON 响应给前端
    res.json(responseData);

});

app.post('/api/getConfigs', 
  async function(req, res) {
    const requestData = req.body;


    const session=res.locals.shopify.session;
    const  result =await ShopHandler.getConfigs(session);
    // 发送 JSON 响应给前端

    // 发送 JSON 响应给前端
    res.json(result);

});

app.post('/api/SetConfigs', 
  async function(req, res) {
    const requestData = req.body;


    const session=res.locals.shopify.session;
    const mystatus=await ShopHandler.setConfig(session,requestData.prodEnb,requestData.blogEnb,requestData.collnEnb,requestData.pageEnb);
    // 发送 JSON 响应给前端
    const responseData = {
      status: mystatus,
    };
    res.json(responseData);

});

app.post('/api/getShopDomains',
  async function(req, res) {
    const requestData = req.body;


    const session=res.locals.shopify.session;
    const shopDomains=await ShopHandler.getShopDomains(session);
    // 发送 JSON 响应给前端

    res.json(shopDomains);

});

app.post('/api/getClientID',
  async function(req, res) {
    try {
      const session=res.locals.shopify.session;
      const serviceAccount=await ServiceAccountHandler.getServicesAccount(session);

      if (serviceAccount && serviceAccount.clientMail) {
        res.json({ clientID: serviceAccount.clientMail });
      } else {
        res.json({ clientID: null });
      }
    } catch (error) {
      console.error('Error getting client ID:', error);
      res.json({ clientID: null });
    }
});

app.post('/api/setStopCreateService',
  async function(req, res) {
    try {
      const { privateShop } = req.body;

      if (privateShop) {
        // 设置当前服务器时间
        const currentTime = new Date().toISOString();
        global.stopCreateService.set(privateShop, currentTime);

        console.log(`StopCreateService set for ${privateShop} at ${currentTime}`);
        res.json({ success: true, timestamp: currentTime });
      } else {
        res.status(400).json({ success: false, error: 'privateShop is required' });
      }
    } catch (error) {
      console.error('Error setting stopCreateService:', error);
      res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/checkWorkReady-Remote', 
  async function(req, res) {
    const requestData = req.body;


    const session=res.locals.shopify.session;
    const myStatus=await ServiceAccountHandler.checkWorkReady(session);

    const responseData = {
      status: myStatus,
    };

    // 发送 JSON 响应给前端
    res.json(responseData);

});


app.post('/api/IndexURLs', 
  async function(req, res) {
  const requestData = req.body;
  
  
  const session=res.locals.shopify.session;
  const urls=await IndexHandler.IndexURLs(session,requestData);
  // 发送 JSON 响应给前端
  res.json(urls); 
  });

app.post('/api/NoIndexURLs', 
async function(req, res) {
const requestData = req.body;
console.log(`NoIndexURLs requestData:${requestData}`);


const session=res.locals.shopify.session;
const urls=await IndexHandler.NoIndexURLs(session,requestData);
// 发送 JSON 响应给前端
res.json(urls); 
});


app.post('/api/DelIndexURLs', 
async function(req, res) {
  const requestData = req.body;
  console.log(`DelIndexURLs requestData:${requestData}`);


  const session=res.locals.shopify.session;
  const urls=await IndexHandler.DelIndexURLs(session,requestData);
  // 发送 JSON 响应给前端
  res.json(urls); 
});


app.post('/api/ReturnToIndex', 
  async function(req, res) {
    const requestData = req.body;
  
  
    const session=res.locals.shopify.session;
    const urls=await IndexHandler.ReturnToIndex(session,requestData);
    // 发送 JSON 响应给前端
    res.json(urls); 
  });





app.post('/api/getIndexLogs', 
  async function(req, res) {
    const requestData = req.body;

    const session = res.locals.shopify.session;
    const shopID = await ShopHandler.getShopIDbySession(session);
    
    let logs = [];
    let total = 0;
    
    try {
      if (requestData.url) {
        // 获取特定 URL 的日志
        logs = await IndexLogDBWorker.getLogsByURL(requestData.url);
        total = logs.length;
      } else {
        // 获取商店的所有日志，带分页
        const page = parseInt(requestData.page, 10) || 1;
        const size = parseInt(requestData.size, 10) || 50;
        const offset = (page - 1) * size;
        
        logs = await IndexLogDBWorker.getLogsByShopID(
          shopID, 
          size, 
          offset
        );
        
        total = await IndexLogDBWorker.getLogCountByShopID(shopID);
      }
      
      res.json({
        logs: logs,
        total: total
      });
    } catch (error) {
      console.error('Error getting index logs:', error);
      res.status(500).json({
        error: 'Failed to retrieve logs',
        message: error.message
      });
    }
  });



app.use(shopify.cspHeaders());
app.use(serveStatic(STATIC_PATH, { index: false }));


app.use("/*", shopify.ensureInstalledOnShop(), async (_req, res, _next) => {
  return res
    .status(200)
    .set("Content-Type", "text/html")
    .send(readFileSync(join(STATIC_PATH, "index.html")));
});


import { ShopSettingDBWorker } from "./lib/db/ShopSettingDBWorker.js";
import { ShopGqlWorker } from "./lib/gql/ShopGqlWorker.js";




console.log("BACKEND_PORT:"+process.env.BACKEND_PORT); 
console.log("FRONTEND_PORT:"+process.env.FRONTEND_PORT);  
app.listen(PORT);
console.log("listen at:"+PORT);  

 


//For public use!!!

 
// 在应用启动后设置 cron 任务
// 每30分钟执行一次 (0 */30 * * * *)
// 或者每小时的第0分钟和第30分钟执行 (0,30 * * * *)
//await IndexScheduler.scheduleIndexingForAllShops();
//<EMAIL>
//await IndexScheduler.scheduleIndexingForAllShops();
cron.schedule('0 */15 * * * *', async () => {
  try {
    console.log(`[${new Date().toISOString()}] Cron job started - scheduled indexing`);
    await IndexScheduler.scheduleIndexingForAllShops();
    console.log(`[${new Date().toISOString()}] Cron job completed - scheduled indexing`);
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Cron job error:`, error);
  }
}, {
  timezone: "Asia/Shanghai" // 设置时区
});

console.log("Cron job scheduled: IndexScheduler will run every 30 minutes");




////await IndexScheduler.scheduleIndexingForAllShops();    

 










