// node_modules/@formatjs/intl-pluralrules/locale-data/vi.js
if (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === "function") {
  Intl.PluralRules.__addLocaleData({ "data": { "categories": { "cardinal": ["other"], "ordinal": ["one", "other"] }, "fn": function(n, ord) {
    if (ord)
      return n == 1 ? "one" : "other";
    return "other";
  } }, "locale": "vi" });
}
//# sourceMappingURL=@formatjs_intl-pluralrules_locale-data_vi.js.map
