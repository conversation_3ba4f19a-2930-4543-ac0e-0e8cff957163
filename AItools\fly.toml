# fly.toml app configuration file generated for seeinplance on 2025-09-05T09:48:20+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'seeinplance'
primary_region = 'mia'


[build]
  [build.args]
    SHOPIFY_API_KEY = "e06f3b66e32ecf6f8889cf1d8a7a62ec"

[env]
  PORT = "8081"
  HOST = "https://mail.jindex.org"  
  SCOPES = "read_products,write_products,unauthenticated_read_product_listings,read_themes"
  SHOPIFY_API_KEY = "e06f3b66e32ecf6f8889cf1d8a7a62ec"
  DB_TYPE = "server"

[http_service]
  internal_port = 8081
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
