import React, { useState, useEffect, useCallback } from 'react';
import {
  Page,
  Layout,
  Card,
  DataTable,
  Filters,
  Button,
  Badge,
  Avatar,
  Stack,
  Text,
  Pagination,
  Modal,
  Spinner,
  Banner
} from '@shopify/polaris';

export default function Report() {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    status: '',
    dateRange: '',
    email: ''
  });
  const [selectedTasks, setSelectedTasks] = useState([]);
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [downloading, setDownloading] = useState(false);
  const [usageInfo, setUsageInfo] = useState(null);
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);

  // Fetch tasks data
  const fetchTasks = useCallback(async (page = 1) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...filters
      });

      const response = await fetch(`/api/report/tasks?${params}`);
      const result = await response.json();

      if (result.success) {
        setTasks(result.data.tasks);
        setTotalPages(result.data.totalPages);
        setCurrentPage(page);
      }
    } catch (error) {
      console.error('Error fetching tasks:', error);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Fetch usage info
  const fetchUsageInfo = useCallback(async () => {
    try {
      console.log('Fetching usage info...');
      const response = await fetch('/api/usage/current');
      const result = await response.json();

      console.log('Usage info response:', result);

      if (result.success) {
        setUsageInfo(result.data);
        console.log('Usage info set:', result.data);
      } else {
        console.error('Usage info fetch failed:', result.error);
      }
    } catch (error) {
      console.error('Error fetching usage info:', error);
    }
  }, []);

  useEffect(() => {
    fetchTasks(1);
    fetchUsageInfo();
  }, [fetchTasks, fetchUsageInfo]);

  // Status badge component
  const StatusBadge = ({ status }) => {
    const statusConfig = {
      'completed': { status: 'success', children: 'Completed' },
      'processing': { status: 'info', children: 'Processing' },
      'pending': { status: 'attention', children: 'Pending' },
      'failed': { status: 'critical', children: 'Failed' }
    };

    return <Badge {...statusConfig[status]} />;
  };

  // Table rows
  const rows = tasks.map((task) => {
    // 解析result_image_url从response_data
    let resultImageUrl = null;
    let uploadedImageUrl = null;

    if (task.response_data) {
      try {
        const responseData = JSON.parse(task.response_data);
        resultImageUrl = responseData.results?.resultUrl || null;
      } catch (error) {
        console.error('Error parsing response_data:', error);
      }
    }

    // 解析上传的图片URL从request_data
    if (task.request_data) {
      try {
        const requestData = JSON.parse(task.request_data);
        uploadedImageUrl = requestData.sourceImageUrl || null;
      } catch (error) {
        console.error('Error parsing request_data:', error);
      }
    }

    return [
      task.user_email,
      new Date(task.created_at).toLocaleString(),
      task.product_title || 'Unknown Product',
      <StatusBadge status={task.status} />,
      // 查看详情按钮
      <Button
        plain
        onClick={() => {
          setSelectedTask({
            ...task,
            uploadedImageUrl,
            resultImageUrl
          });
          setShowTaskModal(true);
        }}
      >
        View Details
      </Button>
    ];
  });

  // Table headers
  const headings = [
    'Email',
    'Generation Time',
    'Product Name',
    'Status',
    'Actions'
  ];

  // Download emails
  const downloadEmails = async () => {
    setDownloading(true);
    try {
      const response = await fetch('/api/report/download-emails', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          taskIds: [], // 空数组表示下载所有邮箱
          filters: filters // 传递当前过滤条件
        })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'emails.csv';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error downloading emails:', error);
    } finally {
      setDownloading(false);
    }
  };

  // Filter options
  const statusOptions = [
    { label: 'All Status', value: '' },
    { label: 'Completed', value: 'completed' },
    { label: 'Processing', value: 'processing' },
    { label: 'Pending', value: 'pending' },
    { label: 'Failed', value: 'failed' }
  ];

  const appliedFilters = [];
  if (filters.email) {
    appliedFilters.push({
      key: 'email',
      label: `Email: ${filters.email}`,
      onRemove: () => setFilters({ ...filters, email: '' })
    });
  }

  return (
    <Page
      title="AI Generation Report"
      subtitle="View and manage AI generation requests for your store"
      primaryAction={{
        content: 'Download All Emails',
        onAction: downloadEmails,
        disabled: tasks.length === 0,
        loading: downloading
      }}
    >
      <Layout>
        {/* Usage Info Banner */}
        {usageInfo && (
          <Layout.Section>
            <Banner
              title="Usage Information"
              status="info"
            >
              <p>
                <strong>Remaining Calls:</strong> {usageInfo.remaining || '0'} |
                <strong> Current Usage:</strong> {usageInfo.used || 0} |
                <strong> Plan:</strong> {usageInfo.plan || 'Free'}
              </p>
            </Banner>
          </Layout.Section>
        )}
      </Layout>
      <Layout>
        <Layout.Section>
          <Card>
            <Card.Section>
              <Filters
                queryValue={filters.email}
                queryPlaceholder="Search by email"
                onQueryChange={(value) => setFilters({ ...filters, email: value })}
                onQueryClear={() => setFilters({ ...filters, email: '' })}
                filters={[]}
                appliedFilters={appliedFilters}
                onClearAll={() => setFilters({ email: '' })}
              />
            </Card.Section>

            {loading ? (
              <Card.Section>
                <div style={{ textAlign: 'center', padding: '2rem' }}>
                  <Spinner size="large" />
                </div>
              </Card.Section>
            ) : (
              <>
                <DataTable
                  columnContentTypes={[
                    'text',
                    'text',
                    'text',
                    'text',
                    'text',
                    'numeric',
                    'text',
                    'text'
                  ]}
                  headings={headings}
                  rows={rows}
                  selectable
                  selectedRows={selectedTasks}
                  onSelectionChange={setSelectedTasks}
                />

                {totalPages > 1 && (
                  <Card.Section>
                    <Stack alignment="center">
                      <Pagination
                        hasPrevious={currentPage > 1}
                        onPrevious={() => fetchTasks(currentPage - 1)}
                        hasNext={currentPage < totalPages}
                        onNext={() => fetchTasks(currentPage + 1)}
                        label={`Page ${currentPage} of ${totalPages}`}
                      />
                    </Stack>
                  </Card.Section>
                )}
              </>
            )}
          </Card>
        </Layout.Section>
      </Layout>

      {/* Image Modal */}
      <Modal
        open={showImageModal}
        onClose={() => setShowImageModal(false)}
        title="Image Preview"
        large
      >
        <Modal.Section>
          {selectedImage && (
            <div style={{ textAlign: 'center' }}>
              <img
                src={selectedImage}
                alt="Preview"
                style={{ maxWidth: '100%', maxHeight: '500px' }}
              />
            </div>
          )}
        </Modal.Section>
      </Modal>

      {/* Task Details Modal */}
      <Modal
        open={showTaskModal}
        onClose={() => setShowTaskModal(false)}
        title="Task Details"
        large
      >
        <Modal.Section>
          {selectedTask && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
              {/* Task Info */}
              <div>
                <Text variant="headingMd">Task Information</Text>
                <div style={{ marginTop: '10px' }}>
                  <p><strong>Email:</strong> {selectedTask.user_email}</p>
                  <p><strong>Product ID:</strong> {selectedTask.product_id}</p>
                  <p><strong>Status:</strong> <StatusBadge status={selectedTask.status} /></p>
                  <p><strong>Created:</strong> {new Date(selectedTask.created_at).toLocaleString()}</p>
                  {selectedTask.updated_at && (
                    <p><strong>Updated:</strong> {new Date(selectedTask.updated_at).toLocaleString()}</p>
                  )}
                </div>
              </div>

              {/* Images */}
              <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap' }}>
                {/* Uploaded Image */}
                {selectedTask.uploadedImageUrl && (
                  <div style={{ flex: '1', minWidth: '300px' }}>
                    <Text variant="headingMd">User Uploaded Image</Text>
                    <div style={{ marginTop: '10px', textAlign: 'center' }}>
                      <img
                        src={selectedTask.uploadedImageUrl}
                        alt="Uploaded"
                        //style={{ maxWidth: '100%', maxHeight: '300px', border: '1px solid #ddd', borderRadius: '8px' }}
                        onClick={() => {
                          setSelectedImage(selectedTask.uploadedImageUrl);
                          setShowImageModal(true);
                          setShowTaskModal(false);
                        }}
                        style={{ cursor: 'pointer', maxWidth: '100%', maxHeight: '300px', border: '1px solid #ddd', borderRadius: '8px' }}
                      />
                    </div>
                  </div>
                )}

                {/* Generated Result */}
                {selectedTask.resultImageUrl && (
                  <div style={{ flex: '1', minWidth: '300px' }}>
                    <Text variant="headingMd">AI Generated Result</Text>
                    <div style={{ marginTop: '10px', textAlign: 'center' }}>
                      <img
                        src={selectedTask.resultImageUrl}
                        alt="Generated"
                        onClick={() => {
                          setSelectedImage(selectedTask.resultImageUrl);
                          setShowImageModal(true);
                          setShowTaskModal(false);
                        }}
                        style={{ cursor: 'pointer', maxWidth: '100%', maxHeight: '300px', border: '1px solid #ddd', borderRadius: '8px' }}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Error Message */}
              {selectedTask.error_message && (
                <div>
                  <Text variant="headingMd" tone="critical">Error Message</Text>
                  <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#fef2f2', border: '1px solid #fecaca', borderRadius: '4px' }}>
                    <Text tone="critical">{selectedTask.error_message}</Text>
                  </div>
                </div>
              )}
            </div>
          )}
        </Modal.Section>
      </Modal>

      {/* Task Details Modal */}
      <Modal
        open={showTaskModal}
        onClose={() => setShowTaskModal(false)}
        title="Task Details"
        large
      >
        <Modal.Section>
          {selectedTask && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
              {/* Task Info */}
              <div>
                <Text variant="headingMd">Task Information</Text>
                <div style={{ marginTop: '10px' }}>
                  <p><strong>Email:</strong> {selectedTask.user_email}</p>
                  <p><strong>Product:</strong> {selectedTask.product_title || 'Unknown Product'}</p>
                  <p><strong>Status:</strong> <StatusBadge status={selectedTask.status} /></p>
                  <p><strong>Created:</strong> {new Date(selectedTask.created_at).toLocaleString()}</p>
                  {selectedTask.updated_at && (
                    <p><strong>Updated:</strong> {new Date(selectedTask.updated_at).toLocaleString()}</p>
                  )}
                </div>
              </div>

              {/* Images */}
              <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap' }}>
                {/* Uploaded Image */}
                {selectedTask.uploadedImageUrl && (
                  <div style={{ flex: '1', minWidth: '300px' }}>
                    <Text variant="headingMd">User Uploaded Image</Text>
                    <div style={{ marginTop: '10px', textAlign: 'center' }}>
                      <img
                        src={selectedTask.uploadedImageUrl}
                        alt="Uploaded"
                        onClick={() => {
                          setSelectedImage(selectedTask.uploadedImageUrl);
                          setShowImageModal(true);
                          setShowTaskModal(false);
                        }}
                        style={{ cursor: 'pointer', maxWidth: '100%', maxHeight: '300px', border: '1px solid #ddd', borderRadius: '8px' }}
                      />
                    </div>
                  </div>
                )}

                {/* Generated Result */}
                {selectedTask.resultImageUrl && (
                  <div style={{ flex: '1', minWidth: '300px' }}>
                    <Text variant="headingMd">AI Generated Result</Text>
                    <div style={{ marginTop: '10px', textAlign: 'center' }}>
                      <img
                        src={selectedTask.resultImageUrl}
                        alt="Generated"
                        onClick={() => {
                          setSelectedImage(selectedTask.resultImageUrl);
                          setShowImageModal(true);
                          setShowTaskModal(false);
                        }}
                        style={{ cursor: 'pointer', maxWidth: '100%', maxHeight: '300px', border: '1px solid #ddd', borderRadius: '8px' }}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Error Message */}
              {selectedTask.error_message && (
                <div>
                  <Text variant="headingMd" tone="critical">Error Message</Text>
                  <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#fef2f2', border: '1px solid #fecaca', borderRadius: '4px' }}>
                    <Text tone="critical">{selectedTask.error_message}</Text>
                  </div>
                </div>
              )}
            </div>
          )}
        </Modal.Section>
      </Modal>
    </Page>
  );
}
