{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/locale-data/vi.js"], "sourcesContent": ["/* @generated */\n// prettier-ignore\nif (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === 'function') {\n  Intl.PluralRules.__addLocaleData({\"data\":{\"categories\":{\"cardinal\":[\"other\"],\"ordinal\":[\"one\",\"other\"]},\"fn\":function(n, ord) {\n  if (ord) return n == 1 ? 'one' : 'other';\n  return 'other';\n}},\"locale\":\"vi\"})\n}\n"], "mappings": ";AAEA,IAAI,KAAK,eAAe,OAAO,KAAK,YAAY,oBAAoB,YAAY;AAC9E,OAAK,YAAY,gBAAgB,EAAC,QAAO,EAAC,cAAa,EAAC,YAAW,CAAC,OAAO,GAAE,WAAU,CAAC,OAAM,OAAO,EAAC,GAAE,MAAK,SAAS,GAAG,KAAK;AAC9H,QAAI;AAAK,aAAO,KAAK,IAAI,QAAQ;AACjC,WAAO;AAAA,EACT,EAAC,GAAE,UAAS,KAAI,CAAC;AACjB;", "names": []}