<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{90D75E7A-41A0-8814-61A2-B5859FC0E033}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>canvas</RootNamespace>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <WindowsTargetPlatformVersion>10.0.22000.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Label="Locals">
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props"/>
  <ImportGroup Label="ExtensionSettings"/>
  <ImportGroup Label="PropertySheets">
    <Import Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props"/>
  </ImportGroup>
  <PropertyGroup Label="UserMacros"/>
  <PropertyGroup>
    <ExecutablePath>$(ExecutablePath);$(MSBuildProjectDirectory)\..\bin\;$(MSBuildProjectDirectory)\..\bin\</ExecutablePath>
    <IgnoreImportLibrary>true</IgnoreImportLibrary>
    <IntDir>$(Configuration)\obj\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.node</TargetExt>
    <TargetName>$(ProjectName)</TargetName>
    <TargetPath>$(OutDir)\$(ProjectName).node</TargetPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\v8\include;..\node_modules\node-addon-api;D:\a\_temp\msys64\ucrt64\include;D:\a\_temp\msys64\ucrt64\include\harfbuzz;D:\a\_temp\msys64\ucrt64\include\pango-1.0;D:\a\_temp\msys64\ucrt64\include\cairo;D:\a\_temp\msys64\ucrt64\include\libpng16;D:\a\_temp\msys64\ucrt64\include\glib-2.0;D:\a\_temp\msys64\ucrt64\lib\glib-2.0\include;D:\a\_temp\msys64\ucrt64\include\pixman-1;D:\a\_temp\msys64\ucrt64\include\freetype2;D:\a\_temp\msys64\ucrt64\include\fontconfig;D:\a\_temp\msys64\ucrt64\include\librsvg-2.0;D:\a\_temp\msys64\ucrt64\include\gdk-pixbuf-2.0;D:\a\_temp\msys64\ucrt64\include\libgsf-1;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++17 %(AdditionalOptions)</AdditionalOptions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4100;4127;4201;4244;4267;4506;4611;4714;4512;4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <MinimalRebuild>false</MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>false</OmitFramePointers>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=canvas;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HAVE_GIF;HAVE_JPEG;HAVE_RSVG;HAVE_BOOLEAN;_USE_MATH_DEFINES;NOMINMAX;NAPI_DISABLE_CPP_EXCEPTIONS;NODE_ADDON_API_ENABLE_MAYBE;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level4</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\21.7.3\\x64\\node.lib&quot;;D:\a\_temp\msys64\ucrt64\lib\libcairo-2.lib;D:\a\_temp\msys64\ucrt64\lib\libpng16-16.lib;D:\a\_temp\msys64\ucrt64\lib\libjpeg-8.lib;D:\a\_temp\msys64\ucrt64\lib\libpango-1.0-0.lib;D:\a\_temp\msys64\ucrt64\lib\libpangocairo-1.0-0.lib;D:\a\_temp\msys64\ucrt64\lib\libgobject-2.0-0.lib;D:\a\_temp\msys64\ucrt64\lib\libglib-2.0-0.lib;D:\a\_temp\msys64\ucrt64\lib\libturbojpeg.lib;D:\a\_temp\msys64\ucrt64\lib\libgif-7.lib;D:\a\_temp\msys64\ucrt64\lib\libfreetype-6.lib;D:\a\_temp\msys64\ucrt64\lib\librsvg-2-2.lib</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)$(ProjectName).node</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetExt>.node</TargetExt>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\v8\include;..\node_modules\node-addon-api;D:\a\_temp\msys64\ucrt64\include;D:\a\_temp\msys64\ucrt64\include\harfbuzz;D:\a\_temp\msys64\ucrt64\include\pango-1.0;D:\a\_temp\msys64\ucrt64\include\cairo;D:\a\_temp\msys64\ucrt64\include\libpng16;D:\a\_temp\msys64\ucrt64\include\glib-2.0;D:\a\_temp\msys64\ucrt64\lib\glib-2.0\include;D:\a\_temp\msys64\ucrt64\include\pixman-1;D:\a\_temp\msys64\ucrt64\include\freetype2;D:\a\_temp\msys64\ucrt64\include\fontconfig;D:\a\_temp\msys64\ucrt64\include\librsvg-2.0;D:\a\_temp\msys64\ucrt64\include\gdk-pixbuf-2.0;D:\a\_temp\msys64\ucrt64\include\libgsf-1;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=canvas;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HAVE_GIF;HAVE_JPEG;HAVE_RSVG;HAVE_BOOLEAN;_USE_MATH_DEFINES;NOMINMAX;NAPI_DISABLE_CPP_EXCEPTIONS;NODE_ADDON_API_ENABLE_MAYBE;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\v8\include;..\node_modules\node-addon-api;D:\a\_temp\msys64\ucrt64\include;D:\a\_temp\msys64\ucrt64\include\harfbuzz;D:\a\_temp\msys64\ucrt64\include\pango-1.0;D:\a\_temp\msys64\ucrt64\include\cairo;D:\a\_temp\msys64\ucrt64\include\libpng16;D:\a\_temp\msys64\ucrt64\include\glib-2.0;D:\a\_temp\msys64\ucrt64\lib\glib-2.0\include;D:\a\_temp\msys64\ucrt64\include\pixman-1;D:\a\_temp\msys64\ucrt64\include\freetype2;D:\a\_temp\msys64\ucrt64\include\fontconfig;D:\a\_temp\msys64\ucrt64\include\librsvg-2.0;D:\a\_temp\msys64\ucrt64\include\gdk-pixbuf-2.0;D:\a\_temp\msys64\ucrt64\include\libgsf-1;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++17 %(AdditionalOptions)</AdditionalOptions>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4100;4127;4201;4244;4267;4506;4611;4714;4512;4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>Full</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=canvas;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HAVE_GIF;HAVE_JPEG;HAVE_RSVG;HAVE_BOOLEAN;_USE_MATH_DEFINES;NOMINMAX;NAPI_DISABLE_CPP_EXCEPTIONS;NODE_ADDON_API_ENABLE_MAYBE;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level4</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\21.7.3\\x64\\node.lib&quot;;D:\a\_temp\msys64\ucrt64\lib\libcairo-2.lib;D:\a\_temp\msys64\ucrt64\lib\libpng16-16.lib;D:\a\_temp\msys64\ucrt64\lib\libjpeg-8.lib;D:\a\_temp\msys64\ucrt64\lib\libpango-1.0-0.lib;D:\a\_temp\msys64\ucrt64\lib\libpangocairo-1.0-0.lib;D:\a\_temp\msys64\ucrt64\lib\libgobject-2.0-0.lib;D:\a\_temp\msys64\ucrt64\lib\libglib-2.0-0.lib;D:\a\_temp\msys64\ucrt64\lib\libturbojpeg.lib;D:\a\_temp\msys64\ucrt64\lib\libgif-7.lib;D:\a\_temp\msys64\ucrt64\lib\libfreetype-6.lib;D:\a\_temp\msys64\ucrt64\lib\librsvg-2-2.lib</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)$(ProjectName).node</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetExt>.node</TargetExt>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\21.7.3\deps\v8\include;..\node_modules\node-addon-api;D:\a\_temp\msys64\ucrt64\include;D:\a\_temp\msys64\ucrt64\include\harfbuzz;D:\a\_temp\msys64\ucrt64\include\pango-1.0;D:\a\_temp\msys64\ucrt64\include\cairo;D:\a\_temp\msys64\ucrt64\include\libpng16;D:\a\_temp\msys64\ucrt64\include\glib-2.0;D:\a\_temp\msys64\ucrt64\lib\glib-2.0\include;D:\a\_temp\msys64\ucrt64\include\pixman-1;D:\a\_temp\msys64\ucrt64\include\freetype2;D:\a\_temp\msys64\ucrt64\include\fontconfig;D:\a\_temp\msys64\ucrt64\include\librsvg-2.0;D:\a\_temp\msys64\ucrt64\include\gdk-pixbuf-2.0;D:\a\_temp\msys64\ucrt64\include\libgsf-1;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=canvas;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HAVE_GIF;HAVE_JPEG;HAVE_RSVG;HAVE_BOOLEAN;_USE_MATH_DEFINES;NOMINMAX;NAPI_DISABLE_CPP_EXCEPTIONS;NODE_ADDON_API_ENABLE_MAYBE;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\binding.gyp"/>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\backend\Backend.cc">
      <ObjectFileName>$(IntDir)\src\backend\Backend.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\backend\ImageBackend.cc">
      <ObjectFileName>$(IntDir)\src\backend\ImageBackend.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\backend\PdfBackend.cc">
      <ObjectFileName>$(IntDir)\src\backend\PdfBackend.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\backend\SvgBackend.cc">
      <ObjectFileName>$(IntDir)\src\backend\SvgBackend.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\bmp\BMPParser.cc">
      <ObjectFileName>$(IntDir)\src\bmp\BMPParser.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\Backends.cc">
      <ObjectFileName>$(IntDir)\src\Backends.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\Canvas.cc">
      <ObjectFileName>$(IntDir)\src\Canvas.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\CanvasGradient.cc">
      <ObjectFileName>$(IntDir)\src\CanvasGradient.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\CanvasPattern.cc">
      <ObjectFileName>$(IntDir)\src\CanvasPattern.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\CanvasRenderingContext2d.cc">
      <ObjectFileName>$(IntDir)\src\CanvasRenderingContext2d.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\closure.cc">
      <ObjectFileName>$(IntDir)\src\closure.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\color.cc">
      <ObjectFileName>$(IntDir)\src\color.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\Image.cc">
      <ObjectFileName>$(IntDir)\src\Image.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\ImageData.cc">
      <ObjectFileName>$(IntDir)\src\ImageData.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\init.cc">
      <ObjectFileName>$(IntDir)\src\init.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\register_font.cc">
      <ObjectFileName>$(IntDir)\src\register_font.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\FontParser.cc">
      <ObjectFileName>$(IntDir)\src\FontParser.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="C:\hostedtoolcache\windows\node\21.7.3\x64\node_modules\npm\node_modules\node-gyp\src\win_delay_load_hook.cc"/>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets"/>
  <ImportGroup Label="ExtensionTargets"/>
</Project>
