// Rate limiting middleware
export class RateLimiter {
  constructor() {
    this.requests = new Map(); // Store requests by IP
    this.cleanupInterval = setInterval(() => this.cleanup(), 60000); // Cleanup every minute
  }

  // Rate limit middleware (10 requests per minute)
  middleware(maxRequests = 10, windowMs = 60000) {
    return (req, res, next) => {
      const ip = req.ip || req.connection.remoteAddress;
      const now = Date.now();
      const windowStart = now - windowMs;

      // Get or create request history for this IP
      if (!this.requests.has(ip)) {
        this.requests.set(ip, []);
      }

      const requestHistory = this.requests.get(ip);
      
      // Remove old requests outside the window
      const validRequests = requestHistory.filter(timestamp => timestamp > windowStart);
      this.requests.set(ip, validRequests);

      // Check if limit exceeded
      if (validRequests.length >= maxRequests) {
        return res.status(429).json({
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: Math.ceil((validRequests[0] + windowMs - now) / 1000)
        });
      }

      // Add current request
      validRequests.push(now);
      this.requests.set(ip, validRequests);

      next();
    };
  }

  // Cleanup old entries
  cleanup() {
    const now = Date.now();
    const oneHourAgo = now - 3600000; // 1 hour

    for (const [ip, requests] of this.requests.entries()) {
      const validRequests = requests.filter(timestamp => timestamp > oneHourAgo);
      if (validRequests.length === 0) {
        this.requests.delete(ip);
      } else {
        this.requests.set(ip, validRequests);
      }
    }
  }

  // Verify Turnstile token
  static async verifyTurnstile(token, remoteip) {
    try {
      const response = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          secret: process.env.TURNSTILE_SECRET_KEY || '0x4AAAAAABtwS6HFoWxdKIY0MmiVL_9aut4', // Replace with your secret key
          response: token,
          remoteip: remoteip
        })
      });

      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('Turnstile verification error:', error);
      return false;
    }
  }

  // Turnstile verification middleware
  static turnstileMiddleware() {
    return async (req, res, next) => {
      const token = req.body.turnstileToken || req.query.turnstileToken;
      console.log('Turnstile token:');
      console.log (req.body.turnstileToken);
      console.log (req.query.turnstileToken); 

      
      if (!token) {
        return res.status(400).json({
          success: false,
          error: 'Turnstile token required'
        });
      }

      const ip = req.ip || req.connection.remoteAddress;
      const isValid = await RateLimiter.verifyTurnstile(token, ip);

      if (!isValid) {
        return res.status(400).json({
          success: false,
          error: 'Invalid security verification'
        });
      }

      next();
    };
  }
}

// Create global instance
export const rateLimiter = new RateLimiter();
