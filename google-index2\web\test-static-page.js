// 简单的测试脚本来验证静态页面路由
import http from 'http';
import { URL } from 'url';

const testUrl = 'http://localhost:3000/static/loginSuccess.html?privateShop=test123';

console.log('Testing static login success page...');
console.log('URL:', testUrl);

const url = new URL(testUrl);

const options = {
  hostname: url.hostname,
  port: url.port || 3000,
  path: url.pathname + url.search,
  method: 'GET',
  headers: {
    'User-Agent': 'Test-Script/1.0'
  }
};

const req = http.request(options, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);
  
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('\n--- Response Body ---');
    
    if (res.statusCode === 200) {
      // 检查HTML内容是否包含预期的元素
      const expectedElements = [
        'Authentication Successful!',
        'Google OAuth authentication',
        'Shop ID: test123',
        'countdown',
        'closeWindow()'
      ];
      
      let allFound = true;
      expectedElements.forEach(element => {
        if (data.includes(element)) {
          console.log(`✓ Found: ${element}`);
        } else {
          console.log(`✗ Missing: ${element}`);
          allFound = false;
        }
      });
      
      if (allFound) {
        console.log('\n🎉 Test PASSED: All expected elements found in the static page!');
      } else {
        console.log('\n❌ Test FAILED: Some expected elements are missing.');
      }
    } else {
      console.log('❌ Test FAILED: Unexpected status code');
      console.log(data);
    }
  });
});

req.on('error', (err) => {
  console.error('❌ Test FAILED: Request error:', err.message);
  console.log('\nMake sure the server is running with: npm run dev');
});

req.end();
