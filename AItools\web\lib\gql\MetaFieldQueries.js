export class MetaFieldQueries {
  // Create metafield definition for AI Enabled status
  static createMetaFieldDefinitionMutation() {
    return `
      mutation metafieldDefinitionCreate($definition: MetafieldDefinitionInput!) {
        metafieldDefinitionCreate(definition: $definition) {
          createdDefinition {
            id
            namespace
            key
            name
            description
            type {
              name
            }
            ownerType
          }
          userErrors {
            field
            message
          }
        }
      }
    `;
  }

  // Delete metafield definition
  static deleteMetaFieldDefinitionMutation() {
    return `
      mutation metafieldDefinitionDelete($id: ID!, $deleteAllAssociatedMetafields: Boolean!) {
        metafieldDefinitionDelete(id: $id, deleteAllAssociatedMetafields: $deleteAllAssociatedMetafields) {
          deletedDefinitionId
          userErrors {
            field
            message
          }
        }
      }
    `;
  }

  // Set metafield values for products
  static setMetaFieldsMutation() {
    return `
      mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            id
            namespace
            key
            value
            type
            owner {
              ... on Product {
                id
                title
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;
  }

  // Query to check if metafield definition exists
  static getMetaFieldDefinitionsQuery() {
    return `
      query metafieldDefinitions($namespace: String!, $key: String!, $ownerType: MetafieldOwnerType!) {
        metafieldDefinitions(first: 10, namespace: $namespace, key: $key, ownerType: $ownerType) {
          edges {
            node {
              id
              namespace
              key
              name
              description
              type {
                name
              }
              ownerType
            }
          }
        }
      }
    `;
  }

  // Query to get product with metafield
  static getProductWithMetaFieldQuery() {
    return `
      query getProduct($id: ID!) {
        product(id: $id) {
          id
          title
          handle
          status
          createdAt
          updatedAt
          featuredImage {
            url
            altText
          }
          images(first: 5) {
            nodes {
              url
              altText
            }
          }
          metafield(namespace: "Jaitools", key: "enabled") {
            id
            value
            type
          }
        }
      }
    `;
  }

  // Query to get multiple products with metafields
  static getProductsWithMetaFieldsQuery() {
    return `
      query getProducts($first: Int, $after: String, $query: String) {
        products(first: $first, after: $after, query: $query) {
          edges {
            node {
              id
              title
              handle
              status
              createdAt
              updatedAt
              featuredImage {
                url
                altText
              }
              metafield(namespace: "Jaitools", key: "enabled") {
                id
                value
                type
              }
            }
          }
          pageInfo {
            endCursor
            hasNextPage
            hasPreviousPage
            startCursor
          }
        }
      }
    `;
  }

  // Build variables for metafield definition creation
  static buildMetaFieldDefinitionVariables() {
    return {
      definition: {
        namespace: "Jaitools",
        key: "enabled",
        name: "AI Enabled",
        description: "Indicates whether AR functionality is enabled for this product",
        type: "boolean",
        ownerType: "PRODUCT"
      }
    };
  }

  // Build variables for setting metafield value
  static buildSetMetaFieldVariables(productId, enabled) {
    return {
      metafields: [
        {
          namespace: "Jaitools",
          key: "enabled",
          value: enabled.toString(),
          type: "boolean",
          ownerId: `gid://shopify/Product/${productId}`,
        }
      ]
    };
  }

  // Build variables for metafield definition query
  static buildMetaFieldDefinitionQueryVariables() {
    return {
      namespace: "Jaitools",
      key: "enabled",
      ownerType: "PRODUCT"
    };
  }

  // Build variables for product query
  static buildProductQueryVariables(productId) {
    return {
      id: `gid://shopify/Product/${productId}`
    };
  }

  // Build variables for products query with pagination
  static buildProductsQueryVariables(page = 1, limit = 20, searchQuery = '') {
    const first = limit;
    const after = page > 1 ? btoa(`arrayconnection:${(page - 1) * limit - 1}`) : null;
    const query = searchQuery ? `title:*${searchQuery}*` : null;

    return {
      first,
      after,
      query
    };
  }

  // Validate GraphQL response
  static validateResponse(data, operationName) {
    if (!data) {
      throw new Error(`${operationName}: No data received from GraphQL`);
    }

    // Check for GraphQL errors
    if (data.errors && data.errors.length > 0) {
      throw new Error(`${operationName}: GraphQL errors: ${data.errors.map(e => e.message).join(', ')}`);
    }

    return true;
  }

  // Extract user errors from mutation response
  static extractUserErrors(mutationResult) {
    if (mutationResult.userErrors && mutationResult.userErrors.length > 0) {
      return mutationResult.userErrors.map(error => error.message).join(', ');
    }
    return null;
  }
}
