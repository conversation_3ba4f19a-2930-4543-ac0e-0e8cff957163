import { defineConfig } from "vite";
import { dirname } from "path";
import { fileURLToPath } from "url";
import https from "https";
import react from "@vitejs/plugin-react";

if (
  process.env.npm_lifecycle_event === "build" &&
  !process.env.CI &&
  !process.env.SHOPIFY_API_KEY
) {
  console.warn(
    "\nBuilding the frontend app without an API key. The frontend build will not run without an API key. Set the SHOPIFY_API_KEY environment variable when running the build command.\n",
  );
}
//process.env.BACKEND_PORT=56561;
//For local testing !!!
process.env.FRONTEND_PORT=443;



const proxyOptions = {
  target: `https://127.0.0.1:${process.env.BACKEND_PORT}`,
  changeOrigin: false,
  secure: false,
  ws: false,
  onError: (err, req, res) => {
    console.error('Proxy error:', err);
    res.writeHead(500, {
      'Content-Type': 'text/plain',
    });
    res.end('Proxy error: ' + err.message);
  }
};

const host = process.env.HOST
  ? process.env.HOST.replace(/https?:\/\//, "")
  : "127.0.0.1"; // For local Testing!!!: "127.0.0.1";

let hmrConfig;
if (host === "localhost") {
  hmrConfig = {
    protocol: "ws",
    host: "127.0.0.1",
    port: 64999,
    clientPort: 64999,
  };
} else {
  hmrConfig = {
    protocol: "wss",
    //host: "127.0.0.1",
    host: host,
    port: process.env.FRONTEND_PORT,

    //For local testing!!!
    https: {
      key: "./cert/local.key",
      cert: "./cert/local.crt"
    },
    strictSSL: false,
    clientPort: 443,
  };
}
import fs from 'fs';

export default defineConfig({
  root: dirname(fileURLToPath(import.meta.url)),
  plugins: [react()],
  define: {
    "process.env.SHOPIFY_API_KEY": JSON.stringify(process.env.SHOPIFY_API_KEY),
  },
  resolve: {
    preserveSymlinks: true,
  },
  server: {
    host: "127.0.0.1",
    port: process.env.FRONTEND_PORT,
    hot: true, 
    https: {
      key: fs.readFileSync(`${process.cwd()}\\cert\\local.key`),
      cert: fs.readFileSync(`${process.cwd()}\\cert\\local.crt`)
    },
    strictSSL: false,
    proxy: {
      "^/(\\?.*)?$": proxyOptions,
      "^/api(/|(\\?.*)?$)": proxyOptions,
      "^/google(/|(\\?.*)?$)": proxyOptions,
      "^/static(/|(\\?.*)?$)": proxyOptions,
    },
    dnsLookupIpVersion: "IPv4", 
  },
});

