{"version": 3, "sources": ["../../@formatjs/intl-pluralrules/locale-data/nb.js"], "sourcesContent": ["/* @generated */\n// prettier-ignore\nif (Intl.PluralRules && typeof Intl.PluralRules.__addLocaleData === 'function') {\n  Intl.PluralRules.__addLocaleData({\"data\":{\"categories\":{\"cardinal\":[\"one\",\"other\"],\"ordinal\":[\"other\"]},\"fn\":function(n, ord) {\n  if (ord) return 'other';\n  return n == 1 ? 'one' : 'other';\n}},\"locale\":\"nb\"})\n}\n"], "mappings": ";AAEA,IAAI,KAAK,eAAe,OAAO,KAAK,YAAY,oBAAoB,YAAY;AAC9E,OAAK,YAAY,gBAAgB,EAAC,QAAO,EAAC,cAAa,EAAC,YAAW,CAAC,OAAM,OAAO,GAAE,WAAU,CAAC,OAAO,EAAC,GAAE,MAAK,SAAS,GAAG,KAAK;AAC9H,QAAI;AAAK,aAAO;AAChB,WAAO,KAAK,IAAI,QAAQ;AAAA,EAC1B,EAAC,GAAE,UAAS,KAAI,CAAC;AACjB;", "names": []}