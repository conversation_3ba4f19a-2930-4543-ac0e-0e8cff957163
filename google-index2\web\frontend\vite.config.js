import { defineConfig } from "vite";
import { dirname } from "path";
import { fileURLToPath } from "url";
import https from "https";
import react from "@vitejs/plugin-react";

const useLocal =
  process.env.NODE_ENV === "1"
    ? false
    : true;
    
if (
  process.env.npm_lifecycle_event === "build" &&
  !process.env.CI &&
  !process.env.SHOPIFY_API_KEY
) {
  console.warn(
    "\nBuilding the frontend app without an API key. The frontend build will not run without an API key. Set the SHOPIFY_API_KEY environment variable when running the build command.\n"
  );
}

if(useLocal==true){
  process.env.FRONTEND_PORT=443;
};

const proxyOptions = {
  target: `http://127.0.0.1:${process.env.BACKEND_PORT}`,
  changeOrigin: false,
  secure: true,
  ws: false,
};

const host = process.env.HOST
  ? process.env.HOST.replace(/https?:\/\//, "")
  : "localhost";



let hmrConfig;
let myServer;
import fs from 'fs';


  if(useLocal==true){
       hmrConfig = {
         protocol: "wss",
         //host: "127.0.0.1",
         host: host,
         port: process.env.FRONTEND_PORT,
     
         //For local testing!!!
         https: {
           key: "./cert/local.key",
           cert: "./cert/local.crt"
         },
         strictSSL: false,
         clientPort: 443,
       };

       myServer={
          
            host: "127.0.0.1",
            port: process.env.FRONTEND_PORT,
            hot: true, 
            https: {
              key: fs.readFileSync(`${process.cwd()}\\cert\\local.key`),
              cert: fs.readFileSync(`${process.cwd()}\\cert\\local.crt`)
            },
            strictSSL: false,
            proxy: {
              "^/(?!native).*?(\\?.*)?$": proxyOptions,
              "^/api(/|(\\?.*)?$)": proxyOptions,
              "^/google(/|(\\?.*)?$)": proxyOptions,
            },
            dnsLookupIpVersion: "IPv4", 
          
       }
  }else{
      if (host === "localhost") {
        hmrConfig = {
          protocol: "ws",
          host: "localhost",
          port: 64999,
          clientPort: 64999,
        };
      } else {
        hmrConfig = {
          protocol: "wss",
          host: host,
          port: process.env.FRONTEND_PORT,
          clientPort: 443,
        };
      }

      myServer={
        host: "localhost",
        port: process.env.FRONTEND_PORT,
        hmr: hmrConfig,
        proxy: {
          "^/(?!native).*?(\\?.*)?$": proxyOptions,
          "^/api(/|(\\?.*)?$)": proxyOptions,
          "^/google(/|(\\?.*)?$)": proxyOptions,
          "^/static(/|(\\?.*)?$)": proxyOptions,

        },
      }
    }
          






export default defineConfig({
  root: dirname(fileURLToPath(import.meta.url)),
  plugins: [react()],
  define: {
    "process.env.SHOPIFY_API_KEY": JSON.stringify(process.env.SHOPIFY_API_KEY),
  },
  resolve: {
    preserveSymlinks: true,
  },
  server: myServer
  
});
